# 飞渡iid
CVE_FREEDO_IID = '2578274935523'

# base api
CVE_VUE_APP_BASE_API = ./api

# 应用基础路径
VITE_BASE_PATH = /yjp

# 构建输出目录
VITE_OUT_DIR = /yjp

# 后台的服务名
CVE_VUE_APP_BASE_API_SERVICENAME = 'project-yjp-localtest'
CVE_VUE_APP_BASE_TCS_SERVICENAME = 'project-yjp-tcs-localtest'
CVE_VUE_APP_BASE_SCS_SERVICENAME = 'project-yjp-scs-localtest'
CVE_VUE_APP_BASE_WATER_SERVICENAME = 'project-yjp-water-localtest'