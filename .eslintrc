{"env": {"browser": true, "node": true}, "globals": {"NodeJS": "readonly", "NodeListOf": "readonly", "ResizeObserverOptions": "readonly"}, "parser": "vue-eslint-parser", "parserOptions": {"ecmaVersion": 12, "sourceType": "module", "parser": "@typescript-eslint/parser"}, "extends": ["plugin:vue/essential", "eslint:recommended", "prettier"], "plugins": ["prettier"], "rules": {"no-console": ["off"], "no-var": "error", "object-property-newline": "error", "prefer-promise-reject-errors": "error", "prettier/prettier": ["error", {"arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "endOfLine": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": false, "singleAttributePerLine": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "vueIndentScriptAndStyle": false}], "quotes": ["error", "single", {"allowTemplateLiterals": true}], "import/order": "off", "sort-imports": "off"}}