FROM reg.hdec.com/basicimage/nginx:1.27.4

ADD ./dist/ /usr/share/nginx/html/

RUN rm /etc/nginx/conf.d/default.conf

ADD ./nginx.conf /etc/nginx/conf.d/nginx.template

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN echo 'Asia/Shanghai' >/etc/timezone

ENV ORIGIN $http_upgrade

ENV HOST http://127.0.0.1:18200/

ENV SUB_APP_BPM http://127.0.0.1:18200/

ENV SUB_APP_WP http://127.0.0.1:18200/

ENV FILE_PREVIEW http://127.0.0.1:8012/

ENV VUE_APP_SAAS_MODE false

EXPOSE 80

ENTRYPOINT sed -i 's|VUE_APP_SAAS_MODE|'${VUE_APP_SAAS_MODE}'|g' /usr/share/nginx/html/index.html && chmod 644 /usr/share/nginx/html/index.html && envsubst '${ORIGIN},${HOST},${SUB_APP_BPM},${SUB_APP_WP},${FILE_PREVIEW}' < /etc/nginx/conf.d/nginx.template > /etc/nginx/conf.d/default.conf && cat /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'
