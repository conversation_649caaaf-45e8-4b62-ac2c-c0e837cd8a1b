import { createApp } from 'vue'
import { RouterView } from 'vue-router'
import { createPinia } from 'pinia'
import Component from '~/router/svg-icon'
import router from '~/router'
import VChart from 'vue-echarts'
import ElementPlus from 'element-plus'
import { ElMessage } from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import '@assets/css/app.scss' //app公共样式
import 'video.js/dist/video-js.css'
// import { getPixelStreamingInstance } from '~/utils/app'

const app = createApp(RouterView)
app.component('SvgIcon', Component)
app.use(router)

// 注册全局 message
app.config.globalProperties.$message = ElMessage

app.use(ElementPlus, {
  locale: zhCn,
})
app.component('v-chart', VChart)
// 创建 Pinia 实例
const pinia = createPinia()
app.use(pinia)
app.mount('#app')