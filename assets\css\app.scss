@use "./variables.scss";
@use "./mixin.scss";

html,
body {
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    text-rendering: optimizeLegibility;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
}

*,
*::before,
*::after {
    box-sizing: inherit;
}

html,
body,
div,
ul,
li,
h1,
h2,
h3,
h4,
h5,
h6,
p,
dl,
dt,
dd,
ol,
form,
input,
textarea,
th,
td,
select,
button {
    font-family: inherit;
}

a,
a:focus,
a:hover {
    text-decoration: none;
    color: inherit;
    cursor: pointer;
}

a:focus,
a:active {
    outline: none;
}

div:focus {
    outline: none;
}

ul {
    padding: 0;
    margin: 0;
}

ul li {
    list-style-type: none;
}

th {
    font-weight: normal;
}

.clearfix {
    &::after {
        content: " ";
        clear: both;
        display: block;
        height: 0;
        visibility: hidden;
        font-size: 0;
    }
}

//菜单栏popover样式
.toolbar-popover.el-popover {
    padding: 8px;
}

//  自适应宽度
.el-cascader,
.el-select,
.el-input,
.ivu-date-picker {
    width: -webkit-fill-available;
}


// 弹窗
.el-dialog {
    padding: 10px 16px 0 16px !important;
    box-shadow: inset 0px 4px 12px 0px rgba(27,165,186,0.63), 0px 2px 6px 0px rgba(44,134,183,0.25);
    background: rgba(0, 49, 72, 0.81) !important;
    border-radius: 4px;
    
}

.el-dialog.is-fullscreen {
    display: flex;
    flex-direction: column;
    
    .el-dialog__body {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
    }
    .pre-view-con {
        height: 100%;
    }
}

//弹窗头部icon
.el-dialog .el-dialog__header {
    display: flex;
    align-items: center;
    color: #191919;
}

.el-overlay-dialog {
    pointer-events: all;
}

.el-dialog .el-dialog__header {
    padding-bottom: 0px;
    height: 46px;
    border-bottom: 1px solid #64C1E2;
}

// table 样式
.el-table {
    --el-table-bg-color: #fff0 --el-table-tr-bg-color: #fff0;
}
.el-table--fit {
    margin: 10px;
    width: calc(100% - 20px);
    --el-table-border-color: #ff000000;
}
.el-table .cell {
    // color: #33fff7;
}
.el-table thead {
    color: #33fff7;
}
.el-table tr {
    background-color: #fff0;
}
.el-table th.el-table__cell {
    background-color: #fff0;
}
.el-table__body {
    width: inherit !important;
    border: 1px solid #ffffff78;
}
.el-table td.el-table__cell {
    border: none;
}

.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: #665d5d6b;
}
// 时间picker
.el-picker-panel{
    margin-left: 0%;
    --el-bg-color-overlay: #000000 !important;
    color: #ffffff !important;
    --el-datepicker-hover-text-color: #33fff7 !important;
    --el-datepicker-active-color: #33fff7 !important;
    --el-disabled-border-color: #33fff7;
    --el-text-color-regular: #ffffff;
    --el-datepicker-icon-color: #ffffff !important;
    --el-color-primary: #33fff7;

  }
//问号提示样式
.el-icon-question {
    color: #ccc;
}

//tooltip样式
.el-tooltip__popper.is-dark {
    color: #555 !important;
    background: #f5f5f5 !important;

    &[x-placement^="top"] {
        .popper__arrow {
            border-top-color: #f5f5f5 !important;
        }

        .popper__arrow::after {
            border-top-color: #f5f5f5 !important;
        }
    }

    &[x-placement^="right"] {
        .popper__arrow {
            border-right-color: #f5f5f5 !important;
        }

        .popper__arrow::after {
            border-right-color: #f5f5f5 !important;
        }
    }

    &[x-placement^="bottom"] {
        .popper__arrow {
            border-bottom-color: #f5f5f5 !important;
        }

        .popper__arrow::after {
            border-bottom-color: #f5f5f5 !important;
        }
    }

    &[x-placement^="left"] {
        .popper__arrow {
            border-left-color: #f5f5f5 !important;
        }

        .popper__arrow::after {
            border-left-color: #f5f5f5 !important;
        }
    }
}

.main-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;
}

.page-container {
    padding: 20px;
}

.page-title {
    color: #191919;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
}

input:disabled::-webkit-input-placeholder {
    -webkit-text-fill-color: rgba(255, 255, 255, 0) !important;
}

textarea:disabled::-webkit-input-placeholder {
    -webkit-text-fill-color: rgba(255, 255, 255, 0) !important;
}

// 单行文本超出部分省略号
.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
/*多行文本超出部分省略号*/
$num_list: 2, 3, 4, 5, 6;
@each $num in $num_list {
    .ellipsis-#{$num} {
        overflow: hidden !important;
        display: -webkit-box !important;
        -webkit-line-clamp: $num !important;
        -webkit-box-orient: vertical !important;
    }
}

// 富文本回显样式
.content-html {
    overflow: hidden;
    display: inline-block;
    img {
        max-width: 100%;
        height: auto;
    }
}

// 只读表单，隐藏placeholder
.form-readonly {
    .el-input__inner::placeholder {
        opacity: 0 !important;
    }
}

/*表格滚动条样式*/
.el-table__body-wrapper {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4);
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.6) !important;
  }
}

// 目录树图标防止缩小
.el-tree {
    .el-tree-node__content {
        .el-svg-icon.el-tree-svg {
            flex-shrink: 0;
        }
    }
}

// 表格

// 文件上传新样式
.file-card .el-upload-list--card {
    .el-upload-list__item {
        max-width: 260px !important;
        background: #ffffff !important;
        border-radius: 4px !important;
        border: 1px solid #dddddd !important;
    }
    .el-upload-list__item-name {
        padding: 12px !important;
    }
}

// 新tab切换样式
.new-tabs {
    .fks-tabs__item.is-active {
        color: var(--theme-color);
    }
    .fks-tabs__active-bar {
        background-color: var(--theme-color);
        &.is-right {
            width: 3px;
        }
    }
    .fks-tabs__item:hover {
        color: var(--theme-color);
    }
}

/*优化的全局滚动条样式*/
::-webkit-scrollbar {
    width: 6px;  /* 纵向滚动条宽度 */
    height: 20px; /* 横向滚动条高度 */
    background-color: transparent;
}

/*滚动条轨道*/
::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 3px;
}

/*滚动条滑块 - 默认隐藏*/
::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

/*鼠标悬停时显示滚动条*/
:hover::-webkit-scrollbar-thumb {
    background-color: rgba(51, 255, 248, 0.336);
}

/*滚动条滑块悬停效果*/
::-webkit-scrollbar-thumb:hover {
    background-color: rgba(51, 255, 248, 0.336) !important;
}

// 定义字体文件
@font-face {
    font-family: \7c97\9ed1;
    src: url('@assets/fonts/SourceHanSansCN-Bold.otf');
    font-weight: 400;
    font-style: auto
}

@font-face {
    font-family: Source-Bold;
    src: url('@assets/fonts/SourceHanSansCN-Bold.ttf');
    font-weight: 400;
    font-style: auto
}

@font-face {
    font-family: Source-Medium;
    src: url('@assets/fonts/SourceHanSansCN-Medium.ttf');
    font-weight: 400;
    font-style: auto
}

@font-face {
    font-family: SIYUANHEITI;
    src: url('@assets/fonts/SourceHanSansCN-Regular.otf');
    src: url('@assets/fonts/SourceHanSansCN-Medium.ttf');
    src: url('@assets/fonts/SourceHanSansCN-Bold.ttf');
}

@font-face {
    font-family: DINAlternate;
    src: url('@assets/fonts/DINAlternateBold.ttf');
    font-weight: 400;
    font-style: auto
}

@font-face {
    font-family: PangMenZhengdao;
    src: url('@assets/fonts/PangMenZhengdaoTitle.TTF');
}

body {
    font-family: Source-Medium;
    color: #fff
}
.el-loading-mask{
    background-color: transparent !important;
}

.hover-white:hover{
    background-color: #ffffff20;
    cursor: pointer;
}
