.tableDialogClass {
  background-color: transparent;
  font-size: 16px;
  ::v-deep(tr),
  ::v-deep(th),
  ::v-deep(td),
  ::v-deep(th.el-table__cell) {
    background-color: transparent;
    color: #CFD3DC
  }
  ::v-deep(thead tr th.is-center.is-leaf.el-table__cell) {
    color: #A3A6AD;
    font-weight: 600;
  }
  ::v-deep(.el-table__body) {
    border: none;
  }
  ::v-deep(.el-table__body tr:hover>td.el-table__cell) {
    background-color: #141414;
  }
  ::v-deep(td.el-table__cell), 
  ::v-deep(th.el-table__cell.is-leaf) {
    border-bottom: 1px solid rgba(38, 213, 206, .3)
  }
  ::v-deep(.el-table--border .el-table__inner-wrapper:after), 
  ::v-deep(.el-table--border:after), 
  ::v-deep(.el-table--border:before), 
  ::v-deep(.el-table__inner-wrapper:before) {
    background-color: rgba(38, 213, 206, .3)
  }
}
.dialogPagination {
  background-color: transparent;
  ::v-deep(.btn-next), ::v-deep(.btn-prev), ::v-deep(.el-pager li), ::v-deep(.el-pager li:disabled) {
    background-color: #303030;
    color: #CFD3DC
  }
  ::v-deep(.btn-next.is-disabled),
  ::v-deep(.btn-next:disabled),
  ::v-deep(.btn-prev.is-disabled),
  ::v-deep(.btn-prev:disabled),
  ::v-deep(.el-pager li.is-disabled),
  ::v-deep(.el-pager li:disabled) {
    background-color: #262727;
    color: #8D9095
  }
  ::v-deep(.el-input__wrapper) {
    background-color: transparent;
    color: #CFD3DC;
    box-shadow: none;
  }
  ::v-deep(.el-pagination__goto),
  ::v-deep(.el-pagination__classifier){
    color: #CFD3DC
  }
  ::v-deep(.el-input) {
    border: 1px solid #4C4D4F;
    border-radius: 4px;
  }
  ::v-deep(.el-input__inner) {
    color: #CFD3DC;
  }
}