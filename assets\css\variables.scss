// 主题
$theme: #32D1ED;///#409EFF;
// sidebar
$menuBg:#fff;
$menuHover: rgba($theme, .6);
$menuActive:#dbe2ef;

$subMenuBg:#fff;
$subMenuHover:rgba($theme, .6);

$menuItemHeight: 40px;

$sideBarWidth: 200px !default;
$collapseBarWith: 54px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menubg: $menuBg;
  menuhover: $menuHover;
  menuactive: $menuActive;
  submenubg: $subMenuBg;
  submenuhover: $subMenuHover;
  sidebarwidth: $sideBarWidth;
  collapsebarwith: $collapseBarWith;
  menuitemheight: $menuItemHeight;
}
