<svg width="480" height="556" viewBox="0 0 480 556" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#153;&#187;&#229;&#189;&#149;&#230;&#161;&#134;">
<g id="Rectangle 34624402" filter="url(#filter0_di_394_1041)">
<path d="M10 13C10 11.3432 11.3431 10 13 10H125H181.184C182.023 10 182.823 10.3514 183.391 10.9688L193.109 21.5312C193.677 22.1486 194.477 22.5 195.316 22.5H225.625H240H286.512C287.449 22.5 288.333 22.0617 288.9 21.3152L296.6 11.1848C297.167 10.4383 298.051 10 298.988 10H355H467C468.657 10 470 11.3431 470 13V528C470 529.657 468.657 531 467 531H310.575C309.589 531 308.666 531.485 308.106 532.297L299.894 544.203C299.334 545.015 298.411 545.5 297.425 545.5H239.75H181.621C180.609 545.5 179.666 544.99 179.112 544.144L171.388 532.356C170.834 531.51 169.891 531 168.879 531H13C11.3431 531 10 529.657 10 528V13Z" fill="url(#paint0_linear_394_1041)" fill-opacity="0.7" shape-rendering="crispEdges"/>
<path d="M13 10.5H181.184C181.795 10.5 182.383 10.7243 182.837 11.125L183.023 11.3076L192.74 21.8701C193.403 22.5905 194.338 23 195.316 23H286.512C287.537 23 288.507 22.5502 289.17 21.7764L289.299 21.6182L296.998 11.4873C297.441 10.9043 298.116 10.5464 298.842 10.5039L298.988 10.5H467C468.381 10.5 469.5 11.6193 469.5 13V528C469.5 529.381 468.381 530.5 467 530.5H310.575C309.496 530.5 308.482 530.997 307.821 531.84L307.694 532.013L299.482 543.919C299.016 544.596 298.247 545 297.425 545H181.621C180.831 545 180.09 544.627 179.62 543.999L179.53 543.87L171.807 532.082C171.16 531.095 170.059 530.5 168.879 530.5H13C11.6193 530.5 10.5 529.381 10.5 528V13C10.5 11.6193 11.6193 10.5 13 10.5Z" stroke="url(#paint1_linear_394_1041)" shape-rendering="crispEdges"/>
</g>
<g id="Mask group">
<mask id="mask0_394_1041" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="98" y="11" width="286" height="141">
<path id="Rectangle 34625563" d="M98 11.0001L183 11L194 23.0001H241H288.75L298 11.0001H384V152H98V11.0001Z" fill="url(#paint2_linear_394_1041)"/>
</mask>
<g mask="url(#mask0_394_1041)">
<g id="Group 1321315061">
<path id="Union" d="M104.476 244.39V260.831L90.2383 269.051L76 260.831V244.39L90.2383 236.17L104.476 244.39ZM141.259 244.39V260.831L127.021 269.051L112.782 260.831V244.39L127.021 236.17L141.259 244.39ZM178.041 244.39V260.831L163.803 269.051L149.565 260.831V244.39L163.803 236.17L178.041 244.39ZM214.824 244.39V260.831L200.586 269.051L186.348 260.831V244.39L200.586 236.17L214.824 244.39ZM251.606 244.39V260.831L237.368 269.051L223.13 260.831V244.39L237.368 236.17L251.606 244.39ZM288.389 244.39V260.831L274.15 269.051L259.913 260.831V244.39L274.15 236.17L288.389 244.39ZM325.172 244.39V260.831L310.934 269.051L296.695 260.831V244.39L310.934 236.17L325.172 244.39ZM361.954 244.39V260.831L347.716 269.051L333.478 260.831V244.39L347.716 236.17L361.954 244.39ZM122.867 211.509V227.949L108.629 236.17L94.3906 227.949V211.509L108.629 203.288L122.867 211.509ZM159.649 211.509V227.949L145.412 236.17L131.174 227.949V211.509L145.412 203.288L159.649 211.509ZM196.433 211.509V227.949L182.194 236.17L167.956 227.949V211.509L182.194 203.288L196.433 211.509ZM233.215 211.509V227.949L218.977 236.17L204.739 227.949V211.509L218.977 203.288L233.215 211.509ZM269.998 211.509V227.949L255.76 236.17L241.521 227.949V211.509L255.76 203.288L269.998 211.509ZM306.78 211.509V227.949L292.542 236.17L278.304 227.949V211.509L292.542 203.288L306.78 211.509ZM343.562 211.509V227.949L329.325 236.17L315.087 227.949V211.509L329.325 203.288L343.562 211.509ZM380.346 211.509V227.949L366.107 236.17L351.869 227.949V211.509L366.107 203.288L380.346 211.509ZM104.476 178.627V195.067L90.2383 203.288L76 195.067V178.627L90.2383 170.407L104.476 178.627ZM141.259 178.627V195.067L127.021 203.288L112.782 195.067V178.627L127.021 170.407L141.259 178.627ZM178.041 178.627V195.067L163.803 203.288L149.565 195.067V178.627L163.803 170.407L178.041 178.627ZM214.824 178.627V195.067L200.586 203.288L186.348 195.067V178.627L200.586 170.407L214.824 178.627ZM251.606 178.627V195.067L237.368 203.288L223.13 195.067V178.627L237.368 170.407L251.606 178.627ZM288.389 178.627V195.067L274.15 203.288L259.913 195.067V178.627L274.15 170.407L288.389 178.627ZM325.172 178.627V195.067L310.934 203.288L296.695 195.067V178.627L310.934 170.407L325.172 178.627ZM361.954 178.627V195.067L347.716 203.288L333.478 195.067V178.627L347.716 170.407L361.954 178.627ZM122.867 145.746V162.187L108.629 170.407L94.3906 162.187V145.746L108.629 137.525L122.867 145.746ZM159.649 145.746V162.187L145.412 170.407L131.174 162.187V145.746L145.412 137.525L159.649 145.746ZM196.433 145.746V162.187L182.194 170.407L167.956 162.187V145.746L182.194 137.525L196.433 145.746ZM233.215 145.746V162.187L218.977 170.407L204.739 162.187V145.746L218.977 137.525L233.215 145.746ZM269.998 145.746V162.187L255.76 170.407L241.521 162.187V145.746L255.76 137.525L269.998 145.746ZM306.78 145.746V162.187L292.542 170.407L278.304 162.187V145.746L292.542 137.525L306.78 145.746ZM343.562 145.746V162.187L329.325 170.407L315.087 162.187V145.746L329.325 137.525L343.562 145.746ZM380.346 145.746V162.187L366.107 170.407L351.869 162.187V145.746L366.107 137.525L380.346 145.746ZM104.476 112.864V129.305L90.2383 137.525L76 129.305V112.864L90.2383 104.644L104.476 112.864ZM141.259 112.864V129.305L127.021 137.525L112.782 129.305V112.864L127.021 104.644L141.259 112.864ZM178.041 112.864V129.305L163.803 137.525L149.565 129.305V112.864L163.803 104.644L178.041 112.864ZM214.824 112.864V129.305L200.586 137.525L186.348 129.305V112.864L200.586 104.644L214.824 112.864ZM251.606 112.864V129.305L237.368 137.525L223.13 129.305V112.864L237.368 104.644L251.606 112.864ZM288.389 112.864V129.305L274.15 137.525L259.913 129.305V112.864L274.15 104.644L288.389 112.864ZM325.172 112.864V129.305L310.934 137.525L296.695 129.305V112.864L310.934 104.644L325.172 112.864ZM361.954 112.864V129.305L347.716 137.525L333.478 129.305V112.864L347.716 104.644L361.954 112.864ZM122.867 79.9824V96.4238L108.629 104.644L94.3906 96.4238V79.9824L108.629 71.7627L122.867 79.9824ZM159.649 79.9824V96.4238L145.412 104.644L131.174 96.4238V79.9824L145.412 71.7627L159.649 79.9824ZM196.433 79.9824V96.4238L182.194 104.644L167.956 96.4238V79.9824L182.194 71.7627L196.433 79.9824ZM233.215 79.9824V96.4238L218.977 104.644L204.739 96.4238V79.9824L218.977 71.7627L233.215 79.9824ZM269.998 79.9824V96.4238L255.76 104.644L241.521 96.4238V79.9824L255.76 71.7627L269.998 79.9824ZM306.78 79.9824V96.4238L292.542 104.644L278.304 96.4238V79.9824L292.542 71.7627L306.78 79.9824ZM343.562 79.9824V96.4238L329.325 104.644L315.087 96.4238V79.9824L329.325 71.7627L343.562 79.9824ZM380.346 79.9824V96.4238L366.107 104.644L351.869 96.4238V79.9824L366.107 71.7627L380.346 79.9824ZM104.476 47.1016V63.542L90.2383 71.7627L76 63.542V47.1016L90.2383 38.8809L104.476 47.1016ZM141.259 47.1016V63.542L127.021 71.7627L112.782 63.542V47.1016L127.021 38.8809L141.259 47.1016ZM178.041 47.1016V63.542L163.803 71.7627L149.565 63.542V47.1016L163.803 38.8809L178.041 47.1016ZM214.824 47.1016V63.542L200.586 71.7627L186.348 63.542V47.1016L200.586 38.8809L214.824 47.1016ZM251.606 47.1016V63.542L237.368 71.7627L223.13 63.542V47.1016L237.368 38.8809L251.606 47.1016ZM288.389 47.1016V63.542L274.15 71.7627L259.913 63.542V47.1016L274.15 38.8809L288.389 47.1016ZM325.172 47.1016V63.542L310.934 71.7627L296.695 63.542V47.1016L310.934 38.8809L325.172 47.1016ZM361.954 47.1016V63.542L347.716 71.7627L333.478 63.542V47.1016L347.716 38.8809L361.954 47.1016ZM122.867 14.2197V30.6611L108.629 38.8809L94.3906 30.6611V14.2197L108.629 6L122.867 14.2197ZM159.649 14.2197V30.6611L145.412 38.8809L131.174 30.6611V14.2197L145.412 6L159.649 14.2197ZM196.433 14.2197V30.6611L182.194 38.8809L167.956 30.6611V14.2197L182.194 6L196.433 14.2197ZM233.215 14.2197V30.6611L218.977 38.8809L204.739 30.6611V14.2197L218.977 6L233.215 14.2197ZM269.998 14.2197V30.6611L255.76 38.8809L241.521 30.6611V14.2197L255.76 6L269.998 14.2197ZM306.78 14.2197V30.6611L292.542 38.8809L278.304 30.6611V14.2197L292.542 6L306.78 14.2197ZM343.562 14.2197V30.6611L329.325 38.8809L315.087 30.6611V14.2197L329.325 6L343.562 14.2197ZM380.346 14.2197V30.6611L366.107 38.8809L351.869 30.6611V14.2197L366.107 6L380.346 14.2197Z" fill="url(#paint3_linear_394_1041)"/>
</g>
</g>
</g>
<g id="&#230;&#149;&#176;&#229;&#173;&#151;&#229;&#173;&#170;&#231;&#148;&#159;&#229;&#167;&#154;&#229;&#174;&#182;&#229;&#185;&#179;" filter="url(#filter1_d_394_1041)">
<path d="M141.84 83.464C142.651 82.824 143.397 82.1733 144.08 81.512C143.227 80.2533 142.555 78.92 142.064 77.512C141.573 76.104 141.275 74.5467 141.168 72.84H144.816C144.901 74.056 145.093 75.1333 145.392 76.072C145.691 76.9893 146.096 77.864 146.608 78.696C147.227 77.7573 147.696 76.7333 148.016 75.624C148.336 74.5147 148.496 73.2347 148.496 71.784H141.2L143.12 65.928H147.6L146.736 68.648H152.048V71.784C152.048 75.4747 151.067 78.7173 149.104 81.512C149.787 82.1733 150.533 82.824 151.344 83.464L152.048 84.072V88.872C149.659 87.1867 147.835 85.7147 146.576 84.456C145.253 85.7787 143.44 87.2507 141.136 88.872V84.072L141.84 83.464ZM123.952 86.248C124.741 86.1627 125.659 86.0347 126.704 85.864L125.008 85.16V81.736H123.952V78.824H125.008V77.832H128.88V78.824H135.696V77.832H139.568V78.824H140.624V81.736H139.568V85.16L137.872 85.864C138.917 86.0347 139.835 86.1627 140.624 86.248V89C138.853 88.8507 137.339 88.6907 136.08 88.52C134.821 88.328 133.563 88.0613 132.304 87.72C131.024 88.0613 129.755 88.328 128.496 88.52C127.237 88.6907 125.723 88.8507 123.952 89V86.248ZM125.04 72.84H128.72L127.632 77.032H123.952L125.04 72.84ZM123.952 69.128H125.296L124.624 66.504H128.688L129.36 69.128H130.096V65.928H134.48V69.128H135.216L135.888 66.504H139.952L139.28 69.128H140.624V72.04H134.48V77.032H130.096V72.04H123.952V69.128ZM128.88 83.528C129.648 83.8267 130.224 84.0293 130.608 84.136L132.304 84.648L134 84.136C134.363 84.0293 134.928 83.8267 135.696 83.528V81.736H128.88V83.528ZM139.536 72.84L140.624 77.032H136.944L135.856 72.84H139.536ZM165.712 86.28H170.256C170.533 86.28 170.768 86.184 170.96 85.992C171.152 85.8 171.248 85.5653 171.248 85.288V82.568H158.704V79.4H172.624L175.152 76.04H163.28V72.872H180.72V75.016L177.648 79.4H184.912V82.568H175.696V85.544C175.696 86.5467 175.355 87.3893 174.672 88.072C173.989 88.7333 173.157 89.064 172.176 89.064H164.464L165.712 86.28ZM157.744 67.688H169.616L168.816 65.896H173.648L174.448 67.688H182.576C183.579 67.688 184.411 68.0293 185.072 68.712C185.755 69.3947 186.096 70.2267 186.096 71.208V73.576H181.68V71.848C181.68 71.5707 181.584 71.336 181.392 71.144C181.2 70.952 180.955 70.856 180.656 70.856H162.192V73.576H157.744V67.688ZM202.32 86.152H203.664C203.856 86.152 204.016 86.0773 204.144 85.928C204.293 85.7787 204.368 85.608 204.368 85.416V84.36H191.664V81.48H204.368V80.776H204.432L211.12 78.824H193.104V75.944H218.544V78.824H218.448L209.072 81.48H219.984V84.36H208.08V87.176C208.08 87.7093 207.899 88.1573 207.536 88.52C207.173 88.8827 206.736 89.064 206.224 89.064H202.32V86.152ZM191.728 67.304H203.344L202.736 65.864H207.568L208.176 67.304H219.952V70.184H212.112V75.592H208.4V70.184H203.216V75.592H199.504V70.184H191.728V67.304ZM193.968 70.6H197.68L195.472 75.496H191.76L193.968 70.6ZM216.944 70.6L219.152 75.496H215.44L213.232 70.6H216.944ZM226.256 85.8H237.488V80.52H226.96V77.384H237.488V72.584H226L227.6 66.6H231.984L230.992 69.448H237.488V65.992H241.936V69.448H253.488V72.584H241.936V77.384H252.464V80.52H241.936V85.8H253.168V88.936H226.256V85.8ZM259.632 85.768C260.379 85.4267 261.285 84.936 262.352 84.296C261.477 83.3573 260.901 82.504 260.624 81.736V71.784H259.632V68.648H260.624V65.928H263.984V68.648H265.84V65.928H269.2V68.648H270.192V71.784H269.2V81.736C268.944 82.504 268.379 83.3573 267.504 84.296C268.336 84.8293 269.232 85.32 270.192 85.768V89.032C268.528 88.648 266.768 87.816 264.912 86.536C263.035 87.816 261.275 88.648 259.632 89.032V85.768ZM263.984 81.448C264.453 81.9173 264.763 82.2053 264.912 82.312C265.296 81.992 265.605 81.704 265.84 81.448V71.784H263.984V81.448ZM272.176 76.552H275.184L273.808 84.424H270.8L272.176 76.552ZM273.84 68.36L275.184 75.528H272.176L270.8 68.36H273.84ZM275.312 82.152V65.928H278.704V82.824L275.6 89.032H271.952L275.312 82.152ZM279.856 65.928H283.248V84.872C283.248 85.1493 283.344 85.384 283.536 85.576C283.728 85.768 283.963 85.864 284.24 85.864H287.728L286.576 89.032H283.376C282.373 89.032 281.531 88.6907 280.848 88.008C280.187 87.3253 279.856 86.4933 279.856 85.512V65.928ZM286.384 76.552L287.728 84.424H284.72L283.344 76.552H286.384ZM284.72 68.36H287.728L286.352 75.528H283.344L284.72 68.36ZM304.752 86.184H305.68C305.979 86.184 306.224 86.088 306.416 85.896C306.608 85.704 306.704 85.4587 306.704 85.16V85.032L293.584 88.264V85.576L306.704 82.216V81.384L293.584 84.616V81.928L306.608 78.728L306.128 77.928L293.584 81V77.96L304.72 75.464H296.24V73.096H293.488V67.08H304.656L304.176 65.96H308.976L309.456 67.08H318.192C319.173 67.08 319.995 67.432 320.656 68.136C321.339 68.8187 321.68 69.6293 321.68 70.568V73.096H318.256V75.464H309.488L311.12 78.312V80.232L313.84 76.072H318.256L315.344 80.52H316.912L321.04 89H316.752L312.944 81H311.12V85.48C311.12 86.4613 310.768 87.2933 310.064 87.976C309.381 88.6587 308.571 89 307.632 89H303.376L304.752 86.184ZM317.584 72.648V71.016C317.584 70.7387 317.488 70.504 317.296 70.312C317.104 70.12 316.869 70.024 316.592 70.024H297.584V72.648H317.584ZM339.312 83.048H327.472V79.912H339.312V69.16H328.528V66.024H354.512V69.16H343.76V79.912H355.568V83.048H343.76V88.936H339.312V83.048ZM334.448 70.664L336.176 78.664H332.336L330.608 70.664H334.448ZM349.04 70.664H352.88L351.024 78.664H347.184L349.04 70.664Z" fill="url(#paint4_linear_394_1041)"/>
</g>
<path id="Rectangle 34625564" d="M298.732 545.5L180.268 545.5L150.935 501.5L328.066 501.5L298.732 545.5Z" stroke="url(#paint5_linear_394_1041)"/>
<foreignObject x="142" y="485" width="195" height="65"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_394_1041_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 34625565" opacity="0.54" filter="url(#filter2_d_394_1041)" data-figma-bg-blur-radius="4">
<path d="M329 501L299 546L180 546L150 501L329 501Z" fill="url(#paint6_linear_394_1041)" shape-rendering="crispEdges"/>
<path d="M298.732 545.5L180.268 545.5L150.935 501.5L328.066 501.5L298.732 545.5Z" stroke="url(#paint7_linear_394_1041)" shape-rendering="crispEdges"/>
</g>
<path id="Vector 33" d="M232 523L240 531L248 523" stroke="#43DEFD" stroke-width="2"/>
<g id="Rectangle 34625566" opacity="0.5" filter="url(#filter3_d_394_1041)">
<path d="M11 233L21 243V343L11 353V233Z" fill="url(#paint8_linear_394_1041)" shape-rendering="crispEdges"/>
</g>
<g id="Rectangle 34625567" opacity="0.5" filter="url(#filter4_d_394_1041)">
<path d="M469 233L459 243V343L469 353V233Z" fill="url(#paint9_linear_394_1041)" shape-rendering="crispEdges"/>
</g>
<path id="Rectangle 34625568" d="M195.699 10.25H286.301C286.837 10.2502 287.188 10.7902 287.001 11.2695L286.956 11.3643L283.067 18.3643C282.935 18.6024 282.684 18.75 282.411 18.75H199.589C199.35 18.75 199.128 18.6371 198.987 18.4492L198.933 18.3643L195.044 11.3643C194.766 10.8644 195.128 10.2502 195.699 10.25Z" fill="url(#paint10_linear_394_1041)" stroke="url(#paint11_linear_394_1041)" stroke-width="0.5"/>
</g>
<defs>
<filter id="filter0_di_394_1041" x="0" y="0" width="480" height="555.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.174833 0 0 0 0 0.560891 0 0 0 0 0.879808 0 0 0 0.38 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_1041"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_1041" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0258412 0 0 0 0 0.53104 0 0 0 0 0.620192 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_394_1041"/>
</filter>
<filter id="filter1_d_394_1041" x="119.953" y="63.8643" width="239.613" height="31.2002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0410503 0 0 0 0 0.408264 0 0 0 0 0.533654 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_1041"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_1041" result="shape"/>
</filter>
<filter id="filter2_d_394_1041" x="142" y="485" width="195" height="65" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0189841 0 0 0 0 0.311658 0 0 0 0 0.370192 0 0 0 0.64 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_1041"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_1041" result="shape"/>
</filter>
<clipPath id="bgblur_0_394_1041_clip_path" transform="translate(-142 -485)"><path d="M329 501L299 546L180 546L150 501L329 501Z"/>
</clipPath><filter id="filter3_d_394_1041" x="7" y="229" width="18" height="128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0374033 0 0 0 0 0.396247 0 0 0 0 0.530449 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_1041"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_1041" result="shape"/>
</filter>
<filter id="filter4_d_394_1041" x="455" y="229" width="18" height="128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0374033 0 0 0 0 0.396247 0 0 0 0 0.530449 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_1041"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_1041" result="shape"/>
</filter>
<linearGradient id="paint0_linear_394_1041" x1="240" y1="10" x2="240" y2="531" gradientUnits="userSpaceOnUse">
<stop stop-color="#2196CC"/>
<stop offset="0.519231" stop-color="#6CD0FE" stop-opacity="0.4"/>
<stop offset="1" stop-color="#1075A4" stop-opacity="0.46"/>
</linearGradient>
<linearGradient id="paint1_linear_394_1041" x1="240" y1="10" x2="240" y2="531" gradientUnits="userSpaceOnUse">
<stop stop-color="#6AB5F3"/>
<stop offset="0.5" stop-color="#C3EAFF"/>
<stop offset="1" stop-color="#6AB5F3"/>
</linearGradient>
<linearGradient id="paint2_linear_394_1041" x1="384" y1="81.5" x2="98" y2="81.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9D9D9" stop-opacity="0"/>
<stop offset="0.505393" stop-color="#A6A6A6" stop-opacity="0.5"/>
<stop offset="1" stop-color="#737373" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_394_1041" x1="229.008" y1="-11.2766" x2="229.008" y2="131.86" gradientUnits="userSpaceOnUse">
<stop stop-color="#B2F1FF"/>
<stop offset="1" stop-color="#59E2FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint4_linear_394_1041" x1="122" y1="74.5" x2="358" y2="74.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AE1FF"/>
<stop offset="0.289041" stop-color="#DCF5FF"/>
<stop offset="0.671752" stop-color="#DCF5FF"/>
<stop offset="1" stop-color="#9AE1FF"/>
</linearGradient>
<linearGradient id="paint5_linear_394_1041" x1="208" y1="501" x2="208" y2="546" gradientUnits="userSpaceOnUse">
<stop offset="0.2" stop-color="#47C2EA" stop-opacity="0"/>
<stop offset="1" stop-color="#86DBFF"/>
</linearGradient>
<linearGradient id="paint6_linear_394_1041" x1="208" y1="501" x2="208" y2="532.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#2BA3DB" stop-opacity="0"/>
<stop offset="1" stop-color="#2196CC" stop-opacity="0.59"/>
</linearGradient>
<linearGradient id="paint7_linear_394_1041" x1="208" y1="501" x2="208" y2="546" gradientUnits="userSpaceOnUse">
<stop offset="0.2" stop-color="#47C2EA" stop-opacity="0"/>
<stop offset="1" stop-color="#52CBFF"/>
</linearGradient>
<linearGradient id="paint8_linear_394_1041" x1="21" y1="293" x2="11" y2="293" gradientUnits="userSpaceOnUse">
<stop stop-color="#92CCFF" stop-opacity="0"/>
<stop offset="1" stop-color="#91CBFF"/>
</linearGradient>
<linearGradient id="paint9_linear_394_1041" x1="459" y1="293" x2="469" y2="293" gradientUnits="userSpaceOnUse">
<stop stop-color="#92CCFF" stop-opacity="0"/>
<stop offset="1" stop-color="#91CBFF"/>
</linearGradient>
<linearGradient id="paint10_linear_394_1041" x1="241" y1="10" x2="241" y2="19" gradientUnits="userSpaceOnUse">
<stop stop-color="#67DCFF"/>
<stop offset="1" stop-color="#1783B1" stop-opacity="0.43"/>
</linearGradient>
<linearGradient id="paint11_linear_394_1041" x1="241" y1="10" x2="241" y2="19" gradientUnits="userSpaceOnUse">
<stop stop-color="#90CEFF"/>
<stop offset="1" stop-color="#90CEFF" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
