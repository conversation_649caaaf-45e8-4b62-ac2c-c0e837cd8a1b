<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1321316041">
<g id="Ellipse 486" opacity="0.8">
<g filter="url(#filter0_i_438_452)">
<circle cx="22" cy="22" r="22" fill="#278FE4" fill-opacity="0.33"/>
</g>
<circle cx="22" cy="22" r="21.9" stroke="#B4E5F6" stroke-opacity="0.07" stroke-width="0.2"/>
</g>
<g id="Ellipse 485" opacity="0.5">
<g filter="url(#filter1_i_438_452)">
<circle cx="22" cy="22" r="16" fill="url(#paint0_linear_438_452)"/>
</g>
<circle cx="22" cy="22" r="15.8" stroke="#B4E5F6" stroke-opacity="0.48" stroke-width="0.4"/>
</g>
<g id="Frame" opacity="0.8" clip-path="url(#clip0_438_452)">
<g id="Vector" filter="url(#filter2_d_438_452)">
<path d="M19.9375 22.7755V23.3595H20.7695V22.5275H19.9375V22.7755ZM23.2175 23.3515H24.0495V22.5195H23.2175V23.3515Z" fill="white"/>
</g>
<g id="Vector_2" filter="url(#filter3_d_438_452)">
<path d="M29.3751 22.64C29.3991 22.232 29.3991 21.8 29.3671 21.368L29.9511 20.536C29.8791 20.12 29.7591 19.704 29.6151 19.296L28.6951 18.864C28.5191 18.472 28.2951 18.112 28.0471 17.752L28.1271 16.744C27.9911 16.584 27.8471 16.416 27.6871 16.272C27.5271 16.112 27.3751 15.976 27.2071 15.84L26.1911 15.92C25.8311 15.672 25.4551 15.456 25.0711 15.28L24.6391 14.368C24.2311 14.224 23.8071 14.12 23.3831 14.04L22.5431 14.616C22.4071 14.608 21.6231 14.608 21.4711 14.616L20.6151 14.048C20.1991 14.12 19.7831 14.232 19.3591 14.368L18.9271 15.28C18.5351 15.456 18.1591 15.68 17.8071 15.92L16.7911 15.84C16.6236 15.9755 16.4634 16.1197 16.3111 16.272C16.1511 16.416 16.0151 16.584 15.8711 16.744L15.9511 17.752C15.7031 18.112 15.4791 18.48 15.3031 18.864L14.3751 19.296C14.2311 19.704 14.1271 20.112 14.0391 20.536L14.6231 21.36C14.5911 21.792 14.5911 22.224 14.6231 22.64L14.0471 23.472C14.1191 23.888 14.2391 24.304 14.3831 24.712L15.3031 25.144C15.4791 25.536 15.7031 25.896 15.9511 26.256L15.8711 27.264C16.0071 27.424 16.1511 27.592 16.3111 27.736C16.4711 27.896 16.6231 28.032 16.7911 28.168L17.8071 28.088C18.1671 28.336 18.5431 28.552 18.9271 28.728L19.3591 29.64C19.7671 29.784 20.1911 29.888 20.6151 29.968L21.4551 29.392C21.5911 29.4 22.3751 29.4 22.5271 29.392L23.3671 29.968C23.7991 29.896 24.2071 29.776 24.6231 29.64L25.0551 28.728C25.4471 28.552 25.8231 28.328 26.1751 28.088L27.1911 28.168C27.3585 28.0325 27.5187 27.8883 27.6711 27.736C27.8311 27.592 27.9671 27.424 28.1111 27.264L28.0311 26.256C28.2791 25.896 28.5031 25.528 28.6791 25.144L29.5991 24.712C29.7431 24.304 29.8471 23.896 29.9351 23.472L29.3751 22.64ZM21.9911 27.648C18.8711 27.648 16.3591 25.128 16.3591 22.016C16.3591 18.904 18.8871 16.384 21.9911 16.384C25.0951 16.384 27.6231 18.904 27.6231 22.016C27.6231 25.128 25.1111 27.648 21.9911 27.648Z" fill="white"/>
</g>
<g id="Vector_3" filter="url(#filter4_d_438_452)">
<path d="M21.5742 22.5283H22.4062V23.3603H21.5742V22.5283Z" fill="white"/>
</g>
<path id="Vector_4" d="M22 17C19.2377 17 17 19.2377 17 22C17 24.7623 19.2377 27 22 27C24.7623 27 27 24.7623 27 22C27 19.2377 24.7623 17 22 17ZM24.8318 24.4383H19.1528V20.5725L20.642 21.429V20.5725L22.1003 21.4444V20.5725L23.5586 21.4367V19.5694H24.8241V24.4383H24.8318Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_i_438_452" x="0" y="0" width="44" height="45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.625 0 0 0 0 0.88125 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_438_452"/>
</filter>
<filter id="filter1_i_438_452" x="6" y="6" width="32" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.625 0 0 0 0 0.88125 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_438_452"/>
</filter>
<filter id="filter2_d_438_452" x="13.9375" y="16.5195" width="16.1133" height="12.8398" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0265449 0 0 0 0 0.295251 0 0 0 0 0.517628 0 0 0 0.53 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_438_452"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_438_452" result="shape"/>
</filter>
<filter id="filter3_d_438_452" x="8.03906" y="8.04004" width="27.9102" height="27.9277" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0265449 0 0 0 0 0.295251 0 0 0 0 0.517628 0 0 0 0.53 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_438_452"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_438_452" result="shape"/>
</filter>
<filter id="filter4_d_438_452" x="15.5742" y="16.5283" width="12.832" height="12.832" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0265449 0 0 0 0 0.295251 0 0 0 0 0.517628 0 0 0 0.53 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_438_452"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_438_452" result="shape"/>
</filter>
<linearGradient id="paint0_linear_438_452" x1="22" y1="6" x2="22" y2="38" gradientUnits="userSpaceOnUse">
<stop stop-color="#27B8E4"/>
<stop offset="1" stop-color="#1772D9"/>
</linearGradient>
<clipPath id="clip0_438_452">
<rect width="16" height="16" fill="white" transform="translate(14 14)"/>
</clipPath>
</defs>
</svg>
