<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1321316043">
<g id="Ellipse 486" opacity="0.8">
<g filter="url(#filter0_i_438_483)">
<circle cx="22" cy="22" r="22" fill="#278FE4" fill-opacity="0.33"/>
</g>
<circle cx="22" cy="22" r="21.9" stroke="#B4E5F6" stroke-opacity="0.07" stroke-width="0.2"/>
</g>
<g id="Ellipse 485" opacity="0.5">
<g filter="url(#filter1_i_438_483)">
<circle cx="22" cy="22" r="16" fill="url(#paint0_linear_438_483)"/>
</g>
<circle cx="22" cy="22" r="15.8" stroke="#B4E5F6" stroke-opacity="0.48" stroke-width="0.4"/>
</g>
<g id="Frame" opacity="0.8" clip-path="url(#clip0_438_483)">
<path id="Vector" d="M28.4366 15.5583C28.1984 15.3204 27.7926 15.1641 27.4705 15.1076C24.9667 14.6626 22.7449 16.0586 21.5754 17.0657C20.932 17.0224 18.4694 17.3301 18.4694 17.3301C17.1703 17.4962 17.7413 18.0588 17.7413 18.0588L19.5139 19.8298C18.594 22.8508 19.1509 23.4072 18.9964 24.1378C18.842 24.8686 17.6176 24.5699 16.2555 24.656C14.8935 24.7423 14.8178 25.2297 15.2906 25.6218C15.6484 25.918 16.6819 26.3161 17.1661 26.4938C17.284 26.555 17.4507 26.6875 17.5647 26.9805C17.7593 27.5013 18.1156 28.3899 18.3867 28.7152C18.7786 29.1871 19.2665 29.1115 19.3516 27.7494C19.4354 26.3862 19.1368 25.1632 19.8674 25.0081C20.598 24.8531 21.1548 25.4094 24.1749 24.4869L25.9464 26.2569C25.9464 26.2569 26.5106 26.8285 26.6745 25.5282C26.6745 25.5282 26.9807 23.0658 26.9373 22.4231C27.9429 21.2522 29.338 19.0291 28.8904 16.5262C28.8324 16.2032 28.677 15.7985 28.4377 15.5595L28.4365 15.5584L28.4366 15.5583ZM27.054 18.1827C26.7203 18.5168 26.1716 18.5102 25.8303 18.1692C25.4877 17.8269 25.4819 17.2794 25.8156 16.9454C26.1493 16.6114 26.6969 16.6168 27.0394 16.959C27.3807 17.3 27.3878 17.8487 27.054 18.1827Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_i_438_483" x="0" y="0" width="44" height="45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.625 0 0 0 0 0.88125 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_438_483"/>
</filter>
<filter id="filter1_i_438_483" x="6" y="6" width="32" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.625 0 0 0 0 0.88125 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_438_483"/>
</filter>
<linearGradient id="paint0_linear_438_483" x1="22" y1="6" x2="22" y2="38" gradientUnits="userSpaceOnUse">
<stop stop-color="#27B8E4"/>
<stop offset="1" stop-color="#1772D9"/>
</linearGradient>
<clipPath id="clip0_438_483">
<rect width="14" height="14" fill="white" transform="translate(15 15)"/>
</clipPath>
</defs>
</svg>
