<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#191;&#148;&#229;&#155;&#158;">
<g id="Group 1321315050">
<g id="Rectangle 34624357">
<g filter="url(#filter0_i_289_461)">
<rect width="36" height="36" rx="3" transform="matrix(1 0 0 -1 0 36)" fill="url(#paint0_linear_289_461)" fill-opacity="0.7"/>
</g>
<rect x="0.3" y="-0.3" width="35.4" height="35.4" rx="2.7" transform="matrix(1 0 0 -1 0 35.4)" stroke="url(#paint1_linear_289_461)" stroke-width="0.6"/>
</g>
<rect id="Rectangle 34624358" x="12" y="34" width="12" height="2" fill="#D4FFF7"/>
</g>
<g id="Frame">
<path id="Vector" d="M17.1324 12.4685V9.3138C16.8488 7.99427 15.7684 8.79817 15.7684 8.79817L8.26368 15.2786C6.61368 16.4247 8.15118 17.2872 8.15118 17.2872L15.5434 23.7115C17.0223 24.8013 17.1348 23.1372 17.1348 23.1372V20.2122C24.6395 17.8591 27.7098 27.2693 27.7098 27.2693C27.9934 27.7849 28.1645 27.2693 28.1645 27.2693C31.0637 13.1575 17.1324 12.4685 17.1324 12.4685Z" fill="url(#paint2_linear_289_461)"/>
</g>
</g>
<defs>
<filter id="filter0_i_289_461" x="0" y="0" width="36" height="38" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0330014 0 0 0 0 0.26663 0 0 0 0 0.411859 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_289_461"/>
</filter>
<linearGradient id="paint0_linear_289_461" x1="18" y1="36" x2="18" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint1_linear_289_461" x1="18" y1="-3.78947" x2="18" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="#E1F7FF"/>
<stop offset="1" stop-color="#D7ECFF"/>
</linearGradient>
<linearGradient id="paint2_linear_289_461" x1="18.0307" y1="8.55566" x2="18.0307" y2="27.4984" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#B9E7FF"/>
</linearGradient>
</defs>
</svg>
