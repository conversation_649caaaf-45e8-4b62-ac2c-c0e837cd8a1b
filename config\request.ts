/**
 *  httpMethod:post、get、delete、*
 *  scope:req(传参不加密，返回加密)、res(传参加密，返回不加密)、*(全不加密)
 */
export const ignore = [
  {
    url: '/sys-auth/oauth/token',
    httpMethod: '*',
    scope: '*',
  },
  {
    url: '/sys-gateway/sign/ts',
    httpMethod: '*',
    scope: '*',
  },
]
//需要加签名访问的接口
export const sign = [
  '/sys-auth/oauth/token',
  '/sys-auth/oauth/sms_captcha',
  '/sys-auth/oauth/client/exit',
  '/sys-auth/oauth/client/all/exit',
  '/sys-auth/oauth/user/lock/num',
  '/sys-auth/oauth/img_captcha',
  '/sys-auth/oauth/behaviour_captcha',
  '/sys-auth/oauth/check_behaviour_captcha',
  '/sys-user/oauth/user/bind',
  '/sys-user/user/sms_register_captcha',
  '/sys-user/user/register',
  '/sys-user/users/cache/sync',
  '/sys-user/user/sms_captcha',
  '/sys-user/user/retrieve/pwd',
  '/sys-system/clients/cache/sync',
  '/sys-system/dictionary/cache/sync',
  '/sys-system/lang/detail/name',
  '/sys-system/clientInfo',
  '/sys-system/dictionary/detail/list',
  '/sys-sso/oauth2/login',
]
//需要不提示返回信息的接口
export const cMsg = [
  '/sys-auth/oauth/token',
  '/sys-auth/oauth/user_info',
  '/sys-auth/oauth/img_captcha',
  '/sys-auth/oauth/sms_captcha',
  '/sys-auth/oauth/behaviour_captcha',
  '/sys-auth/oauth/check_behaviour_captcha',
  '/sys-auth/oauth/render_url/*',
  '/sys-user/oauth/user/bind',
  '/sys-user/users/cache/sync',
  '/sys-user/user/portals',
  '/sys-user/user/sms_captcha',
  '/sys-gateway/sign/ts',
  '/sys-system/clients/cache/sync',
  '/sys-sso/oauth2/user_info',
  '/sys-sso/oauth2/login',
  '/sys-signature/license',
  '/sys-signature/file',
  '/sys-monitor/analysis',
  '/system/info',
  '/licenseInfo',
]
