{"name": "yjp-dashboard", "private": true, "version": "0.1.8", "type": "module", "scripts": {"serve": "vite", "dev": "vite --mode development", "pro": "vite --mode production", "build": "vite build", "build:prod": "vite build --mode production", "build:dev": "vite build --mode development", "build:localtest": "vite build --mode localtest", "build:stage": "vite build --mode stage", "stylelint:css": "stylelint \"router/**/*.(scss|css|sass)\" --customSyntax postcss-scss --fix", "stylelint:vue": "stylelint \"router/**/*.vue\"  --customSyntax postcss-html  --fix", "preview": "vite preview", "docker": "vite build --mode production && docker buildx build --platform linux/amd64 -t yjp/project-vc:0.0.1 . && docker push reg.hdec.com/yjp/project-vc:0.0.1"}, "dependencies": {"@jugar/svg-icon": "^0.0.4", "@vue-office/excel": "^1.7.11", "axios": "^1.7.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.8.1", "ezuikit-js": "^8.1.10", "marked": "^15.0.6", "pinia": "^2.2.6", "qs": "^6.13.0", "sass": "^1.77.8", "sm-crypto": "^0.3.13", "ts-md5": "^1.3.1", "typescript": "^5.6.2", "video.js": "^8.21.0", "vue": "^3.4.38", "vue-demi": "^0.14.10", "vue-echarts": "^7.0.3", "vue-router": "^4.4.3", "vue-tsc": "^2.0.29", "xgplayer": "^3.0.20"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^22.5.0", "@types/qs": "^6.9.15", "@vitejs/plugin-vue": "^5.2.1", "braces": "^3.0.3", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "cz-emoji": "^1.3.2-canary.2", "deepmerge": "^4.3.1", "postcss": "^8.4.41", "rollup-plugin-visualizer": "^5.12.0", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.4", "vite": "^6.0.1", "vite-plugin-svg-icons": "^2.0.1", "vue3-seamless-scroll": "^2.0.1"}, "config": {"commitizen": {"path": "./node_modules/cz-emoji"}}}