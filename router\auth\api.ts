import { Md5 } from 'ts-md5'
import Qs from 'qs'
import request from '~/router/request'
import storage from '~/utils/storage'

interface LoginResponse {
  id?: string
  access_token: string
}

const md5 = new Md5()

// 刷新token
export function getToken(username: string, password: string) {
  md5.appendAsciiStr(password)
  return request({
    url: '/sys-auth/oauth/token',
    method: 'post',
    data: {
      scope: 'all',
      grant_type: 'password',
      username: username,
      password: md5.end(),
    },
    // ctM: true,
    // sign: true,
    transformRequest: [
      //序列化参数
      (data) => Qs.stringify(data),
    ],
  }).then((res) => {
    const { id, access_token } = res as any as LoginResponse
    if (id) {
      storage.set('Fawkes-Auth', access_token)
      // 每一小时更新一次token
      window.setInterval(() => {
        getToken(username, password)
      }, 1000 * 60 * 60)
    }
  })
}

// 获取登录token
export function login (data:any) {
  return request({
    url: '/sys-auth/oauth/token',
    method: 'post',
    data: {
      scope: 'all',
      grant_type: 'password',
      ...data
    },
    transformRequest: [function (data) {
      data = Qs.stringify(data) // 序列化参数
      return data
    }]
  })
}

// 退出登录
export function loginOut() {
  return request({
    url: '/sys-auth/oauth/exit',
    method: 'delete'
  })
}

// 获取手机号
export function getSMSCaptcha(data: any) {
  console.log(data);
  return request({
    url: '/sys-auth/oauth/sms_captcha',
    params: data,
    method: 'get'
  });
}

// 获取用户信息
export function getUserInfo(params:any){
  return request({
    url: '/sys-user/userInfo',
    method: 'get',
    params,
  })
}

// 获取所有门户
export function getPortals() {
  return request({
    url: '/sys-user/user/portals',
    method: 'get',
  })
}

// 子工程树
export function getSubProjectTree(params:any={}) {
  return request({
    url: '/construction-package/wbs/node/tree',
    method: 'get',
    params: {
      ...params
    }
  })
}