<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { pwdEncrypt } from '~/utils/encryption/pwd'
import { login, getPortals, getUserInfo, getSMSCaptcha } from './api'
import storage from '~/utils/storage'
import { getSign } from '~/utils/sign'
import { usePortalStore, useFreedoStore } from '~/stores'
import { verifyPhone } from '~/utils/verify'

// 使用门户状态管理
const portalStore = usePortalStore()
// 使用飞渡状态管理
const freedoStore = useFreedoStore()

const videoPlayer1 = ref<HTMLVideoElement | null>(null)
// const videoPlayer2 = ref<HTMLVideoElement | null>(null)
const isPlaying = ref(false)

const router = useRouter()
const route = useRoute()
const activeName = ref('first')
const loading = ref(false)
const password = ref('')
const username: any = ref('')
const phone = ref('')
const captcha_code = ref('')
const captcha_key = ref('')
const countDown = ref(-2)
const smsLoading = ref(false)

// 先引入文件
import useInstance from '~/router/useInstance'
const { globalProperties } = useInstance()
// 检查URL中的token参数并处理免登录逻辑
const checkUrlToken = async () => {
  // 获取当前完整URL
  const fullUrl = window.location.href
  console.log('当前完整URL:', fullUrl)
  
  // 从路由查询参数中获取token
  let urlToken = route.query.token as string
  
  // 如果在通常的查询参数中没有找到token，尝试从完整URL中解析
  if (!urlToken) {
    // 创建一个正则表达式来匹配token参数
    const tokenRegex = /[\?\&]token=([^\&\#]+)/
    const match = fullUrl.match(tokenRegex)
    
    if (match && match[1]) {
      urlToken = match[1]
      console.log('从完整URL中还原的token:', urlToken)
    }
  }
  
  if (urlToken) {
    console.log('检测到URL中的token参数:', urlToken)
    // 将URL中的token设置为access_token
    storage.set('access_token', urlToken)
    loading.value = true
    
    try {
      // 加载所有门户
      let result = await getPortals()
      portalStore.setPortals(result.data)
      
      // 通过token知道用户已登录，直接继续加载用户信息
      if (result.data && result.data.length > 0) {
        // 先清空之前的数据
        portalStore.updatePortal({})
        // portalStore.setSubjectList([])
        // portalStore.setSiteList([])
        // portalStore.updateWbsTree([])
        // portalStore.updateMicroPortal({})
        
        // 解析token获取用户名
        let userName = ''
        try {
          // 尝试从 token 中解析用户信息
          const tokenParts = urlToken.split('.')
          if (tokenParts.length >= 2) {
            const payload = JSON.parse(atob(tokenParts[1]))
            console.log('Token payload:', payload)
            userName = payload.user_name || payload.userName || ''
          }
        } catch (e) {
          console.error('解析token失败:', e)
        }
        
        // 加载用户信息
        let userResult = await getUserInfo({
          userName: userName,
          portalId: result.data[0].id
        })
        portalStore.updatePortal(userResult.data)

        // 重置飞渡导览播放状态，确保每次登录都播放导览动画
        freedoStore.setHasPlayedIntro(false)

        // 加载子工程树
        // let treeResult = await getSubProjectTree()
        // portalStore.setSubjectList(treeResult.data)

        // 显示登录成功消息
        globalProperties.$message({
          message: '免登录成功',
          type: 'success',
          duration: 3000
        })

        // 跳转到首页
        router.push({ path: '/construction/overview' })
      }
    } catch (error) {
      console.error('免登录处理错误:', error)
      globalProperties.$message({
        message: '免登录失败，请手动登录',
        type: 'error',
        duration: 3000
      })
      loading.value = false
    }
    return true
  }
  return false
}

// 创建视频切换方法
const switchVideo = async (nextVideo: number) => {
  // if (!videoPlayer1.value || !videoPlayer2.value) return

  // // 确定当前活动的视频和下一个视频
  // const currentVideoEl = currentVideo.value === 1 ? videoPlayer1.value : videoPlayer2.value
  // const nextVideoEl = currentVideo.value === 1 ? videoPlayer2.value : videoPlayer1.value

  // // 设置视频属性
  // nextVideoEl.src = nextVideo === 1 
  //   ? new URL(`@assets/login1.mp4`, import.meta.url).href
  //   : new URL(`@assets/login2.mp4`, import.meta.url).href

  // // 预加载视频
  // try {
  //   await new Promise((resolve) => {
  //     nextVideoEl.onloadeddata = resolve
  //     nextVideoEl.load()
  //   })

  //   // 使用 CSS 过渡实现平滑切换
  //   currentVideoEl.style.opacity = '0'
  //   nextVideoEl.style.opacity = '1'
  //   nextVideoEl.play()

  //   // 设置循环状态
  //   nextVideoEl.loop = nextVideo === 2

  //   // 等待过渡完成
  //   await new Promise((resolve) => {
  //     setTimeout(resolve, 300) // 等待过渡动画完成
  //   })

  //   // 更新当前视频状态
  //   currentVideo.value = nextVideo
  // } catch (error) {
  //   console.error('视频切换失败:', error)
  // }
}

onMounted(async () => {
  if (!videoPlayer1.value) return

  // 设置视频属性
  [videoPlayer1.value].forEach(video => {
    video.muted = true
    video.playsInline = true
    video.autoplay = true
  })

  // 初始化第一个视频
  videoPlayer1.value.src = new URL(`@assets/login1.mp4`, import.meta.url).href
  videoPlayer1.value.addEventListener('ended', () => switchVideo(2))
  videoPlayer1.value.addEventListener('error', (e) => {
    console.error('视频播放错误:', e)
  })

  // 初始化第二个视频（预加载）
  // videoPlayer2.value.src = new URL(`@assets/login2.mp4`, import.meta.url).href
  // videoPlayer2.value.addEventListener('error', (e) => {
  //   console.error('视频预加载错误:', e)
  // })

  try {
    await videoPlayer1.value.play()
    isPlaying.value = true
  } catch (error) {
    console.error('视频播放失败:', error)
  }
  
  // 首先检查URL中的token参数
  const hasUrlToken = await checkUrlToken()
  
  // 如果没有URL token，则检查本地存储的token
  if (!hasUrlToken) {
    let fawkes_auth = storage.get('access_token')
    if (fawkes_auth) {
      console.warn('authed',route)
    }
    const un = storage.get('username')
    if (un) {
      username.value = un
    }
    const pw = storage.get('password')
    if (pw) {
      password.value = decodeURIComponent(pw)
    }
  }
})

watch(
  () => username.value,
  (newValue) => {
    getSign(newValue)
  },
  { deep: true }
) // 开启深度监听

const submit = async ()=> {
  if (activeName.value === 'first') {
    if (!username.value) {
      return globalProperties.$message({
        message: '请输入用户名',
        type: 'warning',
      })
    }
    if (!password.value) {
      return globalProperties.$message({
        message: '请输入密码',
        type: 'warning',
      })
    }
  } else {
    if (!phone.value) {
      return globalProperties.$message({
        message: '请输入手机号',
        type: 'warning',
      })
    }
    if (!captcha_code.value) {
      return globalProperties.$message({
        message: '请输入验证码',
        type: 'warning',
      })
    }
  }
  

  loading.value = true

  setTimeout(() => {
    loading.value = false
  }, 1000 * 5)

  let loginData = {}
  if (activeName.value === 'first') {
    loginData = {
      username: username.value,
      password: pwdEncrypt(password.value)
    }
  } else {
    loginData = {
      scope: 'all',
      grant_type: 'sms_captcha',
      username: phone.value,
      captcha_code: captcha_code.value,
      captcha_key: captcha_key.value
    }
  }

   login(loginData)
    .then(async (res: any) => {
      const { id, access_token, userName } = res
      if (id) {
         // 大屏没有退出，先清空之前的数据
         portalStore.setPortals([]) 
        portalStore.updatePortal({})
        // portalStore.setSubjectList([])
        // portalStore.setSiteList([])
        // portalStore.updateWbsTree([])
        // portalStore.updateMicroPortal({})
        // globalProperties.$message.success("登录成功.");

        globalProperties.$message({
          message: '登录成功.',
          type: 'success',
          // duration: 5 * 1000,
        })
        // 重置飞渡导览播放状态，确保每次登录都播放导览动画
        freedoStore.setHasPlayedIntro(false)
        storage.set('access_token', access_token)
        storage.set('username', username.value)
        storage.set('password', encodeURIComponent(password.value))

         //加载所有门户
         let result = await getPortals()
        portalStore.setPortals(result.data)  // portals

        // 加载用户信息
        let userResult = await getUserInfo({ userName:userName, portalId: result.data?.[0]?.id })

        portalStore.updatePortal(userResult.data)  // portal

        // 加载子工程树
        // let treeResult = await getSubProjectTree()
        // portalStore.setSubjectList(treeResult.data)  // subProjectList

        router.push({ path: '/construction/overview' })
      }
    })
    .catch((e) => {
      if (e.response.data) {
        return globalProperties.$message({
          message: e.response.data,
          type: 'warning',
          duration: 5 * 1000,
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
}

//获取短信验证码
const getVerificationCode = async() => {
  //判断手机号是否输入
  if (phone.value === '' || !verifyPhone(phone.value)) {
    globalProperties.$message.warning('请输入正确的手机号码!')
    return
  }
  smsLoading.value = true
  captcha_code.value = ''
  try {
    const res:any = await getSMSCaptcha({ phone: phone.value })
    console.log('res===>', res)
    if (res && res.status && res.data) {
      captcha_key.value = res.data
      updataCountDown()
    }
  } catch (error) {
    globalProperties.$message.error('手机号错误或不存在!')
    smsLoading.value = false
  }
}
// 更新获取短信验证码的时间
const updataCountDown = () => {
  countDown.value = 60
  const count = setInterval(() => {
    if (countDown.value == 0) {
      clearInterval(count)
      smsLoading.value = false
      countDown.value = -1
    } else {
      countDown.value--
    }
  }, 1000)
}
</script>

<template>
  <div class="auth-login">
    <div class="video_frame">
      <video
        ref="videoPlayer1"
        playsinline
        muted
        autoplay
        :controls="false"
        disablePictureInPicture
        style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0;"
      ></video>
      <!-- <video
        ref="videoPlayer2"
        playsinline
        muted
        autoplay
        style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0; opacity: 0;"
      ></video> -->
    </div>
    <div class="auth-bg"></div>
    <div class=auth-content>
      <el-tabs v-model="activeName" class="auth-tabs">
        <el-tab-pane label="账号登录" name="first">
          <el-form class="form-panel" name="login">
            <div class="form-group">
              <label for="username" class="username-bg"></label>
              <el-input v-model="username" name="username" type="text" placeholder="请输入账号" />
            </div>
            <div class="form-group">
              <label for="password" class="password-bg"></label>
              <el-input v-model="password" name="password" type="password" show-password placeholder="请输入密码" />
            </div>
            <el-button class="submit-btn" v-loading="loading" type="primary"  @click.prevent="submit">
              登 录
            </el-button>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="手机号登录" name="second">
          <el-form class="form-panel" name="login">
            <div class="form-group">
              <label for="phone" class="phone-bg"></label>
              <el-input v-model="phone" name="phone" type="text" placeholder="请输入手机号" />
            </div>
            <div class="form-group">
              <label for="password" class="password-bg"></label>
              <div class="captcha"> 
                <el-input v-model="captcha_code" name="captcha_code" type="text" placeholder="请输入验证码" />
                <el-button class="captcha-btn" :disabled="smsLoading" type="primary" @click="getVerificationCode">
                  <span v-if="countDown != -1 && countDown != -2" class="word">{{
                      countDown + 's'
                    }}</span>
                  <span v-if="countDown != -1 || countDown != -2" class="word">{{
                    countDown == -2 ? '获取验证码' : '重新获取'
                  }}</span>
                </el-button>
              </div>
            </div>
            <el-button class="submit-btn" v-loading="loading" type="primary"  @click.prevent="submit">
              登 录
            </el-button>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 0.54;
  }
}

@keyframes contentFadeIn {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.auth-login {
  background: #fff;
  height: 100vh;
  position: relative;
  width: 100vw;
}

.video_frame {
   height: 100%;
  ::v-deep(#controller) {
    display: none !important;
  }
}

.auth-bg {
  width: 823px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  background-image: url('/assets/images/auth/login-bg.svg'), linear-gradient(270deg, #000000 0%, #000000 63%, rgba(0,0,0,0) 100%);
  background-position: center, 0 0;
  background-size: 460px 535px, 100% 100%;
  background-repeat: no-repeat, repeat;
  overflow: hidden;
  opacity: 0.54;
  z-index: 0;
  animation: fadeIn 2s ease-out;
}
.auth-content {
  width: 460px;
  height: 535px;
  position: absolute;
  top: calc((100% - 535px) / 2);
  right: calc((823px - 460px) / 2);
  padding: 120px 44px 90px 44px;
  overflow: hidden;
  display: flex;
  box-sizing: border-box;
  animation: contentFadeIn 2s ease-out;
  animation-fill-mode: both;
}

.auth-tabs {
  flex: 1;
  position: relative;
  overflow: hidden;
}

::v-deep(.el-tabs__nav-wrap:after) {
  background-color: #FFFFFF;
}
::v-deep(.el-tabs__nav-scroll) {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-tabs__nav {
    width: 100%;
    display: flex;
    justify-content: center;
    
  }
  .el-tabs__item {
    flex: 1;
    font-size: 16px;
    color: #FFFFFF;
    &:hover {
      color: #46E3FF;
    }
  }
  .is-active {
    color: #46E3FF;
  }
  .el-tabs__active-bar {
    background-color: #46E3FF;
  }
}
::v-deep(.el-tab-pane) {
  width: 100%;
  height: 100%;
}

::v-deep(.el-tabs__header) {
  margin-bottom: 0;
}

.form-panel {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  background-size: cover;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  // justify-content: space-evenly;
  margin: 0 auto;
  .submit-btn  {
    height: 56px;
    width: 100%;
    background: linear-gradient( 180deg, #249FF2 0%, #5DDEF4 100%);
    box-shadow: 0px 2px 6px 0px rgba(10,75,110,0.25);
    border-radius: 2px 2px 2px 2px;
    border: none;
    font-weight: 600;
    font-size: 24px;
    color: #FFFFFF;
    letter-spacing: 2px;
    margin-top: 62px;
  }
  ::v-deep(.el-loading-spinner .path) {
    stroke: #deebf3;
  }
}

.form-group {
  position: relative;
  width: 100%;
  text-align: center;
  margin-top: 32px;

  ::v-deep(.el-input) {
    height: 56px;
    .el-input__wrapper {
      padding-left: 46px;
      border-radius: 2px;
      background-color: #EEFBFF;
      box-shadow: 0px 2px 10px 0px rgba(4,165,197,0.25);
    }
    .el-input__wrapper.is-focus {
          box-shadow: 0 0 0 1px #46E3FF inset;
    }

    input {
      font-weight: 500;
      font-size: 18px;
      color: #1D2129;
    }

    .el-input__password {
      font-size: 18px;
    }
  }

  label {
    height: 100%;
    width: 20px;
    position: absolute;
    top: 0;
    left: 15px;
    z-index: 10;
  }

  .username-bg {
    background-image: url('/svg/auth/username.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .password-bg {
    background-image: url('/svg/auth/password.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .phone-bg {
    background-image: url('/svg/auth/phone.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}

.captcha {
  display: flex;
  height: 56px;
  .captcha-btn {
    width: 100px;
    height: 100%;
    margin-left: 6px;
    flex-shrink: 0;
    border-radius: 2px;
    background-color: #3ED6F1;
    border: none;
    span {
      font-weight: 600;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 23px;
    }
  }
}
</style>
