<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import type { ChartProps } from './types'
import { initChart, mountContainer, setOption } from './render'

const chart = initChart()
const props = defineProps<ChartProps>()
const wrapper = ref()

onMounted(render)
onUnmounted(destroy)
watch(props, update)

function destroy() {
  chart.dispose()
}
function render() {
  mountContainer(wrapper.value, chart, props)
}
function update() {
  setOption(chart, props)
}
</script>

<template>
  <div class="chart-wrapper" ref="wrapper"></div>
</template>

<style scoped>
.chart-wrapper {
  min-height: 200px;
  width: 100%;
}
</style>
