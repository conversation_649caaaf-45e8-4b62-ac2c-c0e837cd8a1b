import * as echarts from 'echarts/core'
import type { ECharts } from 'echarts/core'
// 项目未使用的类型无需导入
import { Bar<PERSON>hart, LineChart } from 'echarts/charts'
import {
  // 数据集组件
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import type { ChartProps, ECOption } from './types'

const HEIGHT = 360
const WIDTH = 500

// 注册必须的组件
echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
])

export function initChart() {
  const canvas = document.createElement('canvas')
  canvas.height = HEIGHT
  canvas.width = WIDTH
  const chart = echarts.init(canvas)
  return chart
}

export function mountContainer(
  wrapper: HTMLElement,
  chart: ECharts,
  props: ChartProps
) {
  const canvas = chart.getDom() as HTMLCanvasElement
  const { clientHeight, clientWidth } = wrapper
  const { height, reactive, width } = props
  canvas.height = (reactive ? clientHeight : height) ?? HEIGHT
  canvas.width = (reactive ? clientWidth : width) ?? WIDTH
  wrapper.appendChild(canvas)
  setOption(chart, props)
}

export function setOption(chart: ECharts, props: ChartProps) {
  chart.clear()

  const option: ECOption = {
    animationEasing: 'elasticOut',
    backgroundColor: 'transparent',
    color: props.color,
    dataset: {
      dimensions: props.dimensions,
      source: props.source,
    },
    grid: {
      bottom: 36,
      right: 24,
    },
    legend: {
      textStyle: {
        color: 'white',
      },
      top: 16,
    },
    title: {},
    xAxis: {
      axisLabel: {
        color: 'white',
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      type: 'category',
    },
    yAxis: {
      axisLabel: {
        color: 'white',
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: 'white',
        },
      },
    },
    series: props.series.map((type) => ({ type })),
  }

  chart.setOption(option)
}
