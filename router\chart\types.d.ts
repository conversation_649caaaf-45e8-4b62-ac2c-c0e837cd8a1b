// 系列类型的定义后缀都为 SeriesOption
import type { BarSeriesOption, LineSeriesOption } from 'echarts/charts'
// 组件类型的定义后缀都为 ComponentOption
import type {
  TitleComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  DatasetComponentOption,
} from 'echarts/components'
import type { ComposeOption, ZREasing } from 'echarts/core'

export interface ChartProps {
  color?: string[],
  dimensions: string[]
  height?: number
  reactive?: boolean
  series: ChartType[]
  source: ChartSourceRecord[]
  width?: number
}
export interface ChartSeries {
  type: ChartType
}
export type ChartSourceRecord = Record<string, string | number>
export type ChartType = 'bar' | 'line'

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
export type ECOption = ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | DatasetComponentOption
>
