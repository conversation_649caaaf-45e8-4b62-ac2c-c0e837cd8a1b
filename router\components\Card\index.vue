<template>
  <div class="card-container">
    <div class="card-header">
      <div class="header-left">
        <slot name="header-left">
          <div class="header-title">{{ title }}</div>
        </slot>
      </div>
      <div class="header-right">
        <slot name="header-right"></slot>
      </div>
    </div>
    <div class="card-content">
      <div class="card-content-inner">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    required: true,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.card-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  
  .card-header {
    height: 48px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-image: url('~/assets/images/common/card-header.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    cursor: default;
    
    .header-left {
      height: 100%;
      display: flex;
      align-items: center;
      
      .header-title {
        font-family: PangMenZhengdao;
        letter-spacing: 3px;
        text-align: left;
        font-size: 20px;
        color: #ffffff;
        font-style: normal;
        text-transform: none;
        padding-left: 20px;
      }
    }
  }
  
  .card-content {
    flex: 1;
    padding: 12px 12px;
    height: calc(100% - 48px); /* 减去header的高度 */
    box-sizing: border-box;
    
    .card-content-inner {
      height: 100%;
      overflow-y: auto;
    }
  }
}
</style>
