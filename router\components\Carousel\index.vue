<template>
  <div class="carousel-wrapper">
    <div class="carousel-container" ref="carouselContainerRef">
      <div
        class="carousel-track"
        ref="trackRef"
        :style="{ transform: `translateX(-${trackIndex * 100}%)` }"
        @transitionend="handleTransitionEnd"
      >
        <div
          v-for="(page, index) in displayItems"
          :key="index" 
          class="carousel-page"
        >
          <slot :item="page.originalItem || page" :index="page.originalIndex"></slot>
        </div>
      </div>
    </div>

    <div class="carousel-nav" v-if="showControls && totalPages > 1">
      <div class="nav-arrow prev" @click="prevPage">
        <span>&#10094;</span>
      </div>
      <div class="carousel-indicators">
        <div
          v-for="index in totalPages"
          :key="index - 1"
          class="indicator"
          :class="{ active: currentPageIndex === index - 1 }"
          @click="goToPage(index - 1)"
        ></div>
      </div>
      <div class="nav-arrow next" @click="nextPage">
        <span>&#10095;</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount, defineProps, defineExpose, defineOptions, nextTick, watch } from 'vue';

// 轮播页面数据接口
interface CarouselPageData {
  [key: string]: any; // 每个页面可以是任意对象或数组
}

const props = defineProps({
  pageItems: {
    type: Array as () => CarouselPageData[],
    required: true,
  },
  autoplay: {
    type: Boolean,
    default: true,
  },
  interval: {
    type: Number,
    default: 3000,
  },
  showControls: {
    type: Boolean,
    default: true,
  },
});

// 轮播容器和轨道元素的引用
const carouselContainerRef = ref<HTMLElement | null>(null);
const trackRef = ref<HTMLElement | null>(null);

// 当前页面索引和轨道索引
const currentPageIndex = ref(0); // 用户看到的当前页索引 (0 到 originalTotalPages - 1)
const trackIndex = ref(0); // 实际用于 transform 的轨道索引，包含克隆的页面

// 自动播放定时器
let autoplayTimer: number | null = null;

// 过渡状态管理
const isTransitioning = ref(false); // 管理过渡状态，用于实现无缝循环

// 原始页面总数 (不含克隆页)
const originalTotalPages = computed(() => props.pageItems.length);

// 用于轮播轨道实际渲染的数组，首尾各添加一个克隆页以实现无缝循环
const displayItems = computed(() => {
  if (originalTotalPages.value <= 1) {
    return props.pageItems.map((item, index) => ({ originalItem: item, originalIndex: index, type: 'original' }));
  }

  // 深拷贝页面数组作为克隆项的数据
  const clonedFirstPageData = JSON.parse(JSON.stringify(props.pageItems[0]));
  const clonedLastPageData = JSON.parse(JSON.stringify(props.pageItems[originalTotalPages.value - 1]));

  const firstItemWrapper = { originalItem: clonedFirstPageData, originalIndex: 0, type: 'clone-first' };
  const lastItemWrapper = { originalItem: clonedLastPageData, originalIndex: originalTotalPages.value - 1, type: 'clone-last' };
  
  const originals = props.pageItems.map((item, index) => ({ originalItem: item, originalIndex: index, type: 'original' }));
  return [lastItemWrapper, ...originals, firstItemWrapper];
});

// 用于分页指示器等UI显示的总页数，基于原始页面数量
const totalPages = computed(() => originalTotalPages.value);

// 添加ready状态，控制初始化时机
const isReady = ref(false);

// 前一页
const prevPage = () => {
  if (originalTotalPages.value === 0 || isTransitioning.value) return;
  
  isTransitioning.value = true;
  trackIndex.value--;
  currentPageIndex.value = (currentPageIndex.value - 1 + originalTotalPages.value) % originalTotalPages.value;
  stopAutoplay(); // 切换时停止自动播放
};

// 后一页
const nextPage = () => {
  if (originalTotalPages.value === 0 || isTransitioning.value) return;
  
  isTransitioning.value = true;
  trackIndex.value++;
  currentPageIndex.value = (currentPageIndex.value + 1) % originalTotalPages.value;
  stopAutoplay(); // 切换时停止自动播放
};

// 跳转到指定页面
const goToPage = (index: number) => {
  if (index < 0 || index >= originalTotalPages.value || isTransitioning.value) return;
  isTransitioning.value = true;
  trackIndex.value = index + (originalTotalPages.value > 1 ? 1 : 0);
  currentPageIndex.value = index;
  stopAutoplay(); // 切换时停止自动播放
};

// CSS过渡结束后的处理函数
const handleTransitionEnd = async () => {
  if (originalTotalPages.value <= 1) {
    isTransitioning.value = false;
    if (props.autoplay) startAutoplay();
    return;
  }

  let jumped = false;
  const currentTrackIdx = trackIndex.value; 
  const numDisplayItems = displayItems.value.length;

  if (currentTrackIdx === 0) {
    if (trackRef.value) trackRef.value.style.transitionDuration = '0s';
    trackIndex.value = originalTotalPages.value; 
    jumped = true;
  } else if (currentTrackIdx === numDisplayItems - 1) { 
    if (trackRef.value) trackRef.value.style.transitionDuration = '0s';
    trackIndex.value = 1; 
    jumped = true;
  }

  if (jumped) {
    await nextTick(); 
    setTimeout(() => {
      if (trackRef.value) {
        trackRef.value.style.transitionDuration = '0.5s';
      }
      isTransitioning.value = false;
      if (props.autoplay) {
        startAutoplay();
      }
    }, 50);
  } else {
    isTransitioning.value = false;
    if (props.autoplay) {
      startAutoplay();
    }
  }
};

// 启动自动播放
const startAutoplay = () => {
  stopAutoplay(); 
  if (props.autoplay && originalTotalPages.value > 1 && !isTransitioning.value) {
    autoplayTimer = window.setInterval(() => {
      if (!isTransitioning.value) { 
        nextPage();
      }
    }, props.interval);
  }
};

// 停止自动播放
const stopAutoplay = () => {
  if (autoplayTimer !== null) {
    clearInterval(autoplayTimer);
    autoplayTimer = null;
  }
};

// 设置初始轨道索引，如果只有一页或没有克隆，则为0，否则为1（跳过头部的克隆页）
const setInitialTrackIndex = () => {
  if (!isReady.value) {
    return;
  }

  if (originalTotalPages.value > 1) {
    trackIndex.value = 1;
    currentPageIndex.value = 0;
  } else {
    trackIndex.value = 0;
    currentPageIndex.value = 0;
  }
};

// 监听pageItems变化，确保数据加载完成后初始化
watch(() => props.pageItems, (newItems) => {
  if (newItems && newItems.length > 0) {
    isReady.value = true;
    setInitialTrackIndex();
    if (props.autoplay && originalTotalPages.value > 1) {
      startAutoplay();
    }
  }
}, { immediate: true });

onMounted(() => {
  // 不在挂载时立即初始化，等待数据加载完成
  if (props.pageItems && props.pageItems.length > 0) {
    isReady.value = true;
    setInitialTrackIndex();
    if (props.autoplay && originalTotalPages.value > 1) {
      startAutoplay();
    }
  }
});

onBeforeUnmount(() => {
  stopAutoplay();
});

defineOptions({
  name: 'Carousel',
});

defineExpose({
  currentPageIndex,
  goToPage,
  nextPage,
  prevPage,
  startAutoplay,
  stopAutoplay,
});
</script>

<style lang="scss" scoped>
.carousel-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .carousel-container {
    width: 100%;
    flex-grow: 1;
    min-height: 0;
    overflow: hidden;
  }

  .carousel-track {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.5s ease;
  }

  .carousel-page { 
    min-width: 100%; 
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
  }

  .carousel-nav {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 10px; 
    flex-shrink: 0; 

    .nav-arrow {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      cursor: pointer;
      color: #ffffff;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      opacity: 0.6;
      transition: opacity 0.3s ease, background-color 0.3s ease;
      user-select: none;

      &:hover {
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.5);
      }
    }

    .carousel-indicators {
      display: flex;
      justify-content: center;
      margin: 4px 20px 0 20px;

      .indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.52), rgba(255,255,255,0.52));
        margin: 0 5px;
        cursor: pointer;
        transition: background 0.3s ease, box-shadow 0.3s ease;

        &.active {
          width: 8px;
          background: linear-gradient( 180deg, #C9ECFF 0%, #40B8F9 100%);
          box-shadow: 0px 1px 2px 0px rgba(7,117,185,0.39);
        }
      }
    }
  }
}
</style>
