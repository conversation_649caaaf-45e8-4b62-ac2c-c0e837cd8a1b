<script lang="ts" setup>
import { computed, ref } from 'vue';
import { Close, FullScreen, CopyDocument } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '提示',
  },
  width: {
    type: String,
    default: '540px',
  },
  height: {
    type: String,
    default: 'auto',
  },
  customClass: {
    type: String,
    default: '',
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  showFullscreen: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue', 'closed', 'fullscreen-change']);

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
});

const isFullscreen = ref(false);

const dialogBodyStyle = computed(() => ({
  height: props.height,
}));

const handleClose = () => {
  emit('update:modelValue', false);
  emit('closed');
  // 退出时重置全屏状态
  isFullscreen.value = false;
};

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  emit('fullscreen-change', isFullscreen.value);
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :width="width || '580px'"
    :class="['common-dialog', customClass]"
    :close-on-click-modal="false"
    :show-close="false"
    :fullscreen="isFullscreen"
    destroy-on-close
    append-to-body
    draggable
    @closed="handleClose"
  >
    <template #header="{ close }">
      <div class="common-dialog-header">
        <div class="header-left">
          <slot name="header-left">
            <span class="header-title">{{ title }}</span>
          </slot>
        </div>
        <div class="header-middle">
          <slot name="header-middle"></slot>
        </div>
        <div class="header-right">
          <slot name="header-right">
            <el-icon v-if="showFullscreen" class="fullscreen-btn" @click="toggleFullscreen">
              <FullScreen v-if="!isFullscreen" />
              <CopyDocument v-else />
            </el-icon>
            <el-icon v-if="showClose" class="close-btn" @click="close"><Close /></el-icon>
          </slot>
        </div>
      </div>
    </template>
    <div class="common-dialog-body" :style="dialogBodyStyle">
      <slot></slot>
    </div>
    <template v-if="$slots.footer" #footer>
      <slot name="footer"></slot>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.common-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: #fff;
  user-select: none;
  
  .header-left {
    display: flex;
    align-items: center;
    .header-title {
      font-size: 20px;
      font-family: PangMenZhengdao;
      letter-spacing: 3px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    
    .fullscreen-btn {
      cursor: pointer;
      font-size: 18px;
      margin-right: 16px;
      transition: color 0.3s ease;
      &:hover {
        color: #1d90ef;
      }
    }
    
    .close-btn {
      cursor: pointer;
      font-size: 18px;
      margin-right: 0;
      transition: transform 0.3s ease, color 0.3s ease;
      &:hover {
        color: #1d90ef;
        transform: rotate(90deg);
      }
    }
  }
}

.common-dialog-body {
  width: 100%;
  height: 100% !important;
  color: #fff;
  overflow-y: auto;
  padding: 16px 0;
  
}
</style>