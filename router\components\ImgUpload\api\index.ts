

import request from '~/router/request'

// 获取文件列表
export function getFileList(g9s:any) {
  return request({
    method: 'post',
    url: '/sys-storage/file',
    data: {
      g9s
    }
  }).catch((e) => {
    if (e) console.log(e.toString())
  })
}

export function getFile(fileToken:any) {
  return request({
    url: '/sys-storage/download',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      f8s: fileToken
    }
  })
}
// 根据文件token和grouptoken删除文件，token删除单文件，grouptoken删除文件组
export function deleteFile(data:any) {
  return request({
    url: '/sys-storage/file',
    method: 'delete',
    data,
  })
}
