<!-- VideoPreview -->
<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, onMounted, ref, toRefs, watchEffect } from 'vue';
import Player from 'xgplayer';
import 'xgplayer/dist/index.min.css'
// import HlsPlugin from 'xgplayer-hls';
import { getUrl } from '~/utils/sign';

const preViewData: any = ref({

  player: null,// 视频播放器实例
  showDialog: false, // 视频预览
  playerThumb: null, // 首帧视频播放器实例
})
let {

  player,
  showDialog,
  playerThumb,
} = toRefs(preViewData.value)
const preViewProps: any = defineProps({
  file: {
    type: Object, // 视频
    default: () => {
      return {};
    },
  },
  dialogTitle: {}
})
const getTitle = computed(() => {
  return `班前会议${preViewProps.dialogTitle}`
})
const destory = () => {
  showDialog.value = false;
  try {
    if (player.value) {
      player.value.destroy();
    }
  } catch (e) {
    console.error("Failed to destroy video player:", e);
  } finally {
    player.value = null;
  }

  try {
    if (playerThumb.value) {
      playerThumb.value.destroy();
    }
  } catch (e) {
    console.error("Failed to destroy video player thumbnail:", e);
  } finally {
    playerThumb.value = null;
  }
}

const videoPlayerRef = ref<any>(null)
const playerThumbRef = ref<any>(null)

const init = () => {
  if (!preViewProps.file?.fileToken) {
    return;
  }
  if (!videoPlayerRef) {
    return;
  }
  const {
    // name, 
    fileToken } = preViewProps.file;

  //   const type = `video/mp4`;
  const signUrl = getUrl({ f8s: fileToken }); 
  const src = `${import.meta.env.CVE_VUE_APP_DOWNLOAD_API}?${signUrl}`;
  // const src = 'http://www.w3school.com.cn/example/html5/mov_bbb.mp4';
  console.log(src);

  if (!player.value) {
    const playerDom = new Player({
      el: videoPlayerRef.value,
      url: src,
      lang: 'zh-cn',
      height: '100%',
      width: '100%',
      fluid: true,
      autoplayMuted: true,
      autoplay: true,
    });
    player.value = playerDom;
  } else {
    player.value.play();
  }
}

// 初始化首帧视频
const initVideo = async () => {
  if (!preViewProps.file?.fileToken) {
    return;
  }
  if (!playerThumbRef.value) {
    return;
  }

  const {
    fileToken } = preViewProps.file;
    const signUrl = await getUrl({ f8s: fileToken });
  const src = `${import.meta.env.CVE_VUE_APP_DOWNLOAD_API}?${signUrl}`;
  // const src = 'http://www.w3school.com.cn/example/html5/mov_bbb.mp4'
  try {
    // 如果实例已存在，切换 URL
    if (playerThumb.value) {
      console.log('Player exists, updating URL.');
      playerThumb.value.switchURL(src);
      playerThumb.value.reload();
    } else {
      // 初始化新的播放器实例
      console.log('Initializing new player.');
      playerThumb.value = new Player({
        el: playerThumbRef.value,
        url: src,
        lang: 'zh-cn',
        height: '100%',
        width: '100%',
        fluid: true,
        autoplayMuted: true,
        videoInit: true,
        controls: false,
        ignores: ['play', 'replay'],
      });
    }
  } catch (error) {
    console.error('Error updating player:', error);
    // 销毁现有实例（如果有）
    if (playerThumb.value) {
      playerThumb.value.destroy();
      playerThumb.value = null;
    }
  }
}
const show = () => {
  showDialog.value = true;
  nextTick(() => {
    init();
  })
}
const close = () => {
  showDialog.value = false;
  nextTick(() => {
    player.value && player.value.pause();
  });
}


watchEffect(() => {
  if (preViewProps.file) {

  }
})

onMounted(() => {
  initVideo();
})
onBeforeUnmount(() => {
  destory();
})

</script>
<template>
  <div class="video-preview-wp">
    <div class="video-thumb">
      <div class="thumb" ref="playerThumbRef"></div>
    </div>
    <div class="video-thumb-point" @click.stop.prevent="showDialog = true"></div>
    <el-dialog v-model="showDialog" :show-fullscreen="false" @close="close" @opened="show">

      <template #header="{ titleId, titleClass }">
        <div class="my-header lx-work-area-title">
          <div :id="titleId" :class="{ titleClass, 'title-content': true }">
            {{ getTitle }}
          </div>
        </div>
      </template>
      <div class="video-preview">
        <div class="player-dom" ref="videoPlayerRef"></div>
        <!-- <video
      id="videoPlayerRef"
      class="video-js vjs-default-skin"
      controls
      :poster="'http://hangjiahu.oss-cn-hangzhou.aliyuncs.com/Preview/Snipaste_2023-10-10_10-15-53.jpg'"
    >
      <source
        src="https://devimages.apple.com/iphone/samples/bipbop/bipbopall.m3u8"
        type="application/x-mpegURL"
      />
    </video> -->
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.lx-work-area-title {
  margin-top: 0px !important;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  // background-image: url('~/assets/images/constructorSecure/card-header.png');
  background-size: contain;
  background-repeat: no-repeat;
  cursor: default;
}

.title-content {
  font-family: Source-Bold;
  font-weight: 700;
  font-size: 24px;
  color: #ffffff;
  line-height: 24px;
  letter-spacing: 1px;
  text-shadow: 0px 2px 2px #000;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding: 4px 35px 8px 32px;
}

.video-preview-wp {
  position: relative;
  width: 100%;
  height: 100%;

  ::v-deep(.el-dialog__title) {
    color: #33fff7;
    padding-left: 15px;
  }

  ::v-deep(.el-dialog__body) {
    padding: 10px;
    height: 550px;
  }

  .video-thumb {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 0;

    .thumb {
      width: 100%;
      height: 100% !important;
      padding-top: 0px !important;



      .xgplayer-poster {
        display: block;
        opacity: 1;
        visibility: visible;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-position: center center;
        background-size: 100% auto;
        background-repeat: no-repeat;
        transition: opacity 0.3s ease, visibility 0.3s ease;
        pointer-events: none;
      }

    }
  }

  .video-thumb-point {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: '';
    z-index: 1;
    cursor: pointer;
  }
}

.video-preview {
  position: relative;
  min-width: 400px;
  min-height: 300px;
  height: auto;

  .player-dom {
    width: 100%;
    height: 100%;
  }
}
</style>