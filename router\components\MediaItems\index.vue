<!-- MediaItems -->
<script setup lang="ts">
import { computed, ref, toRefs, watchEffect } from 'vue';
import { getFile } from '~/utils/api/file'
import { isImage, isVideo } from '~/utils/file';
import VideoPreview from './VideoPreview.vue';

const preViewData:any = ref({
    loading: false,
    // fileList: [],
})
let {
    loading,
    // fileList
} = toRefs(preViewData.value) 
const preViewProps: any = defineProps({
    g9s: {},
    fileList: {
        type: Array,
        default: []
    },
    sliceNum: {
        type: Number,
        default: 3, // 传0 则不分割
    },
    showSliceRemainNum: {
        type: Boolean,
        default: true
    },
    dialogTitle:{

    }
})
// 分割数量
const sliceList: any = computed(() => {
    return preViewProps.sliceNum
        ? [...preViewProps.fileList].slice(0, preViewProps.sliceNum)
        : [...preViewProps.fileList];
})
// 分割后剩余数量
const sliceRemainList: any = computed(() => {
    return preViewProps.sliceNum ? [...preViewProps.fileList].slice(preViewProps.sliceNum) : [];
})
// 初始化文件列表
const initFiles = async () => {
    try {
        loading.value = true;
        const List: any = [];
        if (preViewProps.g9s) {
            const res = await getFile({ g9s: [preViewProps.g9s] });
            // const res = {"status":true,"data":[
            //     {"link":null,"fileToken":"845fce779ab5806386af8a95e2b6eae8","fileName":"aec89fcd7ab4fa848ad8ec9a730819d.jpg","extName":"jpg","groupToken":"09DE94EC-CC7C-41E4-8029-C5CB199F2E5B","dir":"2024-12/","size":"1208430","version":"0","objectName":"2024-12/377969c2a1fd0e5aae4df0d74b9e29b2.jpg","tag":null,"resource":null},
            //     {"link":null,"fileToken":"f54ad430c6fe932028e4f1da5a9ea338","fileName":"b23c3b13a449546829153ddb6e18ae6.jpg","extName":"jpg","groupToken":"09DE94EC-CC7C-41E4-8029-C5CB199F2E5B","dir":"2024-12/","size":"1262637","version":"0","objectName":"2024-12/4558bf8bce63655ed750e29ffca1f341.jpg","tag":null,"resource":null},
            //     {"link":null,"fileToken":"f0f2f2a231c7c92832bcc50f0c16c89b","fileName":"c3a0332c68030a678c58ef54836cfb09.mp4","extName":"mp4","groupToken":"09DE94EC-CC7C-41E4-8029-C5CB199F2E5B","dir":"2024-12/","size":"33619669","version":"0","objectName":"2024-12/52601d7fecde6eed2518d3b9e1123c91.mp4","tag":null,"resource":null},{"link":null,"fileToken":"70594890b7e4edb6850d0e0eab70a03f","fileName":"c46086cdb2f6b56b22c812a07b5e41f.jpg","extName":"jpg","groupToken":"09DE94EC-CC7C-41E4-8029-C5CB199F2E5B","dir":"2024-12/","size":"1367540","version":"0","objectName":"2024-12/883ecc60170ad061c7682f166b2d4680.jpg","tag":null,"resource":null},{"link":null,"fileToken":"e568ca1dc52edc2ed45e09f065ff1d77","fileName":"e834b36e10200aabb079a8b7ba70ff3.jpg","extName":"jpg","groupToken":"09DE94EC-CC7C-41E4-8029-C5CB199F2E5B","dir":"2024-12/","size":"1472110","version":"0","objectName":"2024-12/bb02765d5c806b4da6c0d80d3e81e7d0.jpg","tag":null,"resource":null},{"link":null,"fileToken":"f7fea6923dcab44529c6ece880eaa706","fileName":"8970c72ded56d910c32ba1491a0dd388.mp4","extName":"mp4","groupToken":"09DE94EC-CC7C-41E4-8029-C5CB199F2E5B","dir":"2024-12/","size":"28918574","version":"0","objectName":"2024-12/06ac6eafeed0d6abdb277a0fa6931e16.mp4","tag":null,"resource":null},],"code":8000000,"message":"成功","txId":"561b024897d99044736a95348bba1924"};
            [...res?.data].forEach(async ({ fileToken, size, fileName }) => {
                const isImg = isImage(fileName);
                const isVio = isVideo(fileName);
                const file = {
                    name: fileName,
                    size: size,
                    isImage: isImg,
                    isVideo: isVio,
                    url: isImg
                        ? `${import.meta.env.CVE_VUE_APP_DOWNLOAD_IMG_API}?f8s=${fileToken}` 
                        // ? `https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg`
                        : undefined,
                    thumbnail: isImg
                    ? `${import.meta.env.CVE_VUE_APP_DOWNLOAD_IMG_API}?f8s=${fileToken}&thumbnail=true&width=${200}&height=${200}` 
                        // ? `https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg`  
                        : undefined,
                    fileToken: fileToken,
                };
                List.push(file);
            });
        }
        fileList.value = List;
    } catch (e) {
        console.log(e);
        fileList.value = [];
    } finally {
        loading.value = false;
    }
}
watchEffect(() => {
    if (preViewProps.g9s) {
        // initFiles();
        console.log(preViewProps.fileList)
    }
})

</script>
<template>
    <div class="simple-preview-list">
        <template v-if="preViewProps.fileList && preViewProps.fileList.length">
            <!-- 显示3个就行了 -->
            <template v-for="(file, ) in sliceList">
                <div class="simple-preview">
                    <!-- 图片 -->
                    <template v-if="file.isImage">
                        <el-image class="preview-image" :src="file.thumbnail" lazy
                            :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[file.url]"
                            :initial-index="0" fit="cover" />
                    </template>
                    <!-- 视频 -->
                    <template v-else-if="file.isVideo">
                        <div class="preview-image preview-video">
                            <VideoPreview :file="file" :dialogTitle="preViewProps.dialogTitle" />
                        </div>
                    </template>
                    <template v-else>
                        <div class="preview-image preview-file">文件{{ file.name }}</div>
                    </template>
                </div>
            </template>
            <!-- 显示剩余的数量 -->
            <div v-if="sliceRemainList.length && showSliceRemainNum" class="preview-remain">
                + {{ sliceRemainList.length }}
            </div>
        </template>
        <template v-else>
            <div>/</div>
        </template>
    </div>
</template>
<style lang="scss" scoped>
.simple-preview-list {
    display: flex;
    flex-direction: row;
    align-items: center;
    ::v-deep(.el-image__wrapper){
        display: none;
    }
::v-deep(.el-image__inner.is-loading){
    opacity: 1;
}
    .simple-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 96px;
        height: 72px;

        +.simple-preview {
            margin-left: 10px;
        }
    }

    .preview-remain {
        padding-left: 10px;
        color: #9a9a9a;
    }

    .preview-image {
        width: 100%;
        height: 100%;
    }

    .preview-video {
        background-color: #000;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .preview-file {
        background-color: #f1f1f1;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .file-name {
            font-size: 12px;
        }
    }
}

.dialog-review {}
</style>