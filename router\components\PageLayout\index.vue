<template>
  <div class="page-layout">
    <!-- 前置插槽 -->
    <slot name="prepend"></slot>

    <!-- 左侧面板 -->
    <div class="layout-left" @contextmenu.prevent @selectstart.prevent @dragstart.prevent>
      <slot name="left"></slot>
    </div>

    <!-- 左侧边框 -->
    <div class="layout-left-border"></div>

    <!-- 中间区域，默认为返回按钮 -->
    <slot name="middle">
      <div class="go-back" v-if="showBackButton" @click="onBack">
        <!-- {{ backTitle }} -->
      </div>
    </slot>

    <!-- 中间区域图例 -->
    <slot name="legend">
      <el-popover
        placement="top-start"
        :width="200"
        trigger="click"
        popper-class="legend-popper"
      >
        <template #reference>
          <div
            class="legend"
            v-if="showLegendButton"
            @click="onLegendClick"
            @contextmenu.prevent
            @selectstart.prevent
            @dragstart.prevent
          ></div>
        </template>
        <div class="legend-content" @contextmenu.prevent @selectstart.prevent @dragstart.prevent>
          <div v-for="(item, index) in props.legend" :key="index">
            <div class="title">{{ item.title }}</div>
            <div class="legend—box">
              <div v-for="(child, index1) in item.children" :key="index1">
                <svg-icon :name="`legend:Frame`" class="else_icon" />
                <span>{{ child.title }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-popover>
    </slot>

    <!-- 右侧面板 -->
    <div class="layout-right" @contextmenu.prevent @selectstart.prevent @dragstart.prevent>
      <slot name="right"></slot>
    </div>

    <!-- 右侧边框 -->
    <div class="layout-right-border"></div>

    <div class="layout-bottom" @contextmenu.prevent @selectstart.prevent @dragstart.prevent></div>

    <!-- 底部边框 -->
    <div class="layout-bottom-border"></div>

    <!-- 后置插槽 -->
    <slot name="append"></slot>

    <!-- 对话框和其他全局组件 -->
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  // 是否显示返回按钮
  showBackButton: {
    type: Boolean,
    default: false,
  },
  // 返回按钮显示的文本
  backTitle: {
    type: String,
    default: '',
  },
  // 是否显示图例按钮
  showLegendButton: {
    type: Boolean,
    default: false,
  },
  legend: {
    type: Array,
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['back', 'legendClick'])

// 处理返回按钮点击事件
const onBack = (): void => {
  emit('back')
}

const onLegendClick = () => {
  emit('legendClick')
}
</script>

<style lang="scss" scoped>
// SCSS Mixins - 公共样式混入
@mixin disable-user-interaction {
  /* 禁用文本选择和拖拽 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

@mixin layout-panel-base {
  width: 418px;
  position: absolute;
  top: 88px;
  bottom: 0;
  overflow: auto;
  pointer-events: all;
  animation-duration: 0.5s;
  display: flex;
  flex-direction: column;
  @include disable-user-interaction;
}

@mixin layout-border-base {
  position: absolute;
  top: 128px;
  bottom: 40px;
  width: 12px;
  z-index: 10;
  background-size: 100% 100%;
  background-repeat: repeat-y;
  pointer-events: none;
}

@mixin action-button-base {
  width: 36px;
  height: 36px;
  position: absolute;
  left: 512px;
  pointer-events: all;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  cursor: pointer;
  animation-duration: 0.5s;
  z-index: 100;
  @include disable-user-interaction;
}

.page-layout {
  font-family: Source-Medium;
  height: 100%;
  width: 100%;
  position: relative;

  .layout-left-border {
    @include layout-border-base;
    left: 12px;
    background-image: url('~/assets/images/common/page-left-bg.png');
  }

  .layout-right-border {
    @include layout-border-base;
    right: 12px;
    background-image: url('~/assets/images/common/page-right-bg.png');
  }

  .layout-bottom-border {
    position: absolute;
    left: 32px;
    right: 32px;
    bottom: 0;
    height: 34px;
    z-index: 10;
    background-image: url('~/assets/images/common/page-bottom-bg.png');
    background-size: 100% 100%;
    background-repeat: repeat-x;
    pointer-events: none;
  }

  .go-back {
    @include action-button-base;
    top: 122px;
    background: url('~/assets/images/common/back.svg');
  }

  .legend {
    @include action-button-base;
    bottom: 72px;
    background: url('~/assets/images/common/legend.svg');
  }

  @keyframes slideInFromLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInFromRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .layout-left {
    @include layout-panel-base;
    padding: 16px 34px 46px 48px;
    left: 0;
    background: linear-gradient(
      90deg,
      rgba(0, 0, 0, 0.8) 15%,
      rgba(0, 0, 0, 0.1) 90%,
      rgba(0, 0, 0, 0.01) 100%
    );
    animation: slideInFromLeft 0.8s ease-out;
  }

  .layout-right {
    @include layout-panel-base;
    padding: 16px 48px 46px 34px;
    right: 0;
    background: linear-gradient(
      270deg,
      rgba(0, 0, 0, 0.8) 15%,
      rgba(0, 0, 0, 0.1) 90%,
      rgba(0, 0, 0, 0.01) 100%
    );
    animation: slideInFromRight 0.8s ease-out;
  }

  .layout-bottom {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 138px;
    background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.8) 20%,
      rgba(0, 0, 0, 0) 100%
    );
    @include disable-user-interaction;
  }
}
</style>
<style lang="scss">
.legend-popper {
  background-image: url('~/assets/images/common/legend_bg.png') !important;
  background-size: 100% 100% !important;
  background-color: transparent !important;
  padding: 12px !important;
  border: none !important;

  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;

  .el-popper__arrow {
    &::before {
      display: none;
    }
  }
  .legend-content {
    > div {
      margin-bottom: 8px;
      .title {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        font-size: 12px;
        color: #c8c8c8;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .legend—box {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 3px;
        > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          .svg-icon {
            width: 20px;
            height: 20px;
            cursor: pointer;
          }
          span {
            font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
            font-size: 12px;
            color: #ffffff;
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>