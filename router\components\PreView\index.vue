<!-- 文件预览 -->
<script setup lang="ts" name="PreView">
import {
  computed,
  watch,
  ref,
  toRefs,
  onMounted,
  onUnmounted,
  //   nextTick,
} from 'vue'

import { getFileIconByExtName } from '~/utils/file'
import { downloadImage, getFile } from '~/utils/api/file'
import { getFileAsPdf } from './api'
import { getUrl } from '~/utils/sign'
import { getFile as downLoadFile } from '~/router/components/ImgUpload/api'
import Player from 'xgplayer'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'

const preViewProps: any = defineProps({
  fileToken: {},
})

const preViewData = ref<Record<string, any>>({
  token: '',
  file: {},
  loading: false,
  extName: '',
  pdfSrc: '',
  picSrc: '',
  fileName: '',
  fileType: -1,
  downloadUrl: import.meta.env.CVE_VUE_APP_BASE_API + '/sys-storage/download?',

  musicPlayer: null,
  videoPlayer: null,

  extTypePdf: ['pdf', 'doc', 'docx', 'xls', 'ppt', 'pptx'],
  extTypeExcel: ['xlsx'],
  extTypePic: ['jpg', 'jpeg', 'png', 'gif'],
  extTypeVideo: ['mp4', 'mkv', 'mp3', 'mov'],
  NO_FILE: -1,
  PIC_FILE: 0,
  PDF_FILE: 1,
  ELSE_FILE: 2,
  VIDEO_FILE: 3,
  MUSIC_FILE: 4,
  TXT_FILE: 5,

  videoUrl: '',
  txtPre: '',

  EXCEL_FILE: 6, // excel
  excelURL: '',
})
let {
  token,
  loading,
  extName,
  pdfSrc,
  picSrc,
  fileName,
  fileType,
  downloadUrl,
  //   musicPlayer,
  videoPlayer,

  extTypePdf,
  extTypeExcel,
  extTypePic,
  extTypeVideo,
  // extTypeMusic,
  NO_FILE,
  PIC_FILE,
  PDF_FILE,
  ELSE_FILE,
  VIDEO_FILE,
  //   MUSIC_FILE,
  EXCEL_FILE, // excel
  excelURL,
  videoUrl,
  file,
  TXT_FILE,
  txtPre,
} = toRefs(preViewData.value)

const pdfURL = computed(() => {
  return `/yjp/pdfjs/webs/viewer.html?file=${encodeURIComponent(pdfSrc.value)}`
})
const picURL = computed(() => {
  return picSrc.value
})
// const current_fileType = computed(() => {
//   return fileType.value
// })

watch(() => preViewProps.fileToken,(newValue, oldValue) => {
    getFileData()
  },
  {
    immediate: true,
    // deep: true,
  }
)

function getFileData() {
  if (!preViewProps.fileToken) {
    return
  }
  videoUrl.value = ''
  loading.value = true
  getFile({ f8s: [preViewProps.fileToken] })
    .then((res: any) => {
      if (res.data[0]) {
        file.value = res.data[0]
        initData()
      }
    })
    .catch(() => {
      loading.value = false
    })
}
function getFileIconExtName(extName: any) {
  return getFileIconByExtName(extName)
}

/**
 * @description: 判断后缀类型
 */
function getFileType() {
  extName.value = ''
  //传入的文件可能没有extName,手动获取后缀
  if (!file.value.extName) {
    fileName.value = file.value.name
    let first = file.value.name.lastIndexOf('.')
    let namelength = file.value.name.length
    extName.value = file.value.name
      .substring(first + 1, namelength)
      ?.toLowerCase()
  } else {
    extName.value = file.value.extName?.toLowerCase()
    fileName.value = file.value.fileName
  }
  if (extTypePdf.value.indexOf(extName.value) >= 0) {
    return PDF_FILE.value
  }
  if (extTypeExcel.value.indexOf(extName.value) >= 0) {
    return EXCEL_FILE.value
  }
  if (extTypePic.value.indexOf(extName.value) >= 0) {
    return PIC_FILE.value
  }
  if (extTypeVideo.value.indexOf(extName.value.toLocaleLowerCase()) >= 0) {
    return VIDEO_FILE.value
  }
  if (extName.value === 'txt') {
    return TXT_FILE.value
  }
  return ELSE_FILE.value
}
/**
 * @description: 处理pdf/可转化为pdf的文件
 * @param {String}token  文件的token
 */
function getPdfFile(token: any) {
  loading.value = true
  getFileAsPdf(token).then((res: any) => {
    if (res.status) {
      let objecturl = window.URL.createObjectURL(res.data)
      console.log(objecturl, '@@@ objecturl objecturl')
      pdfSrc.value = objecturl
      loading.value = false
    }
  })
}
/*
 * 处理xlsx
 * */
function getExcelFile(token: any) {
  console.log('excelURL', excelURL.value)
  loading.value = true
  excelURL.value = mediaUrl(token)
  loading.value = false
}
/**
 * @description: 处理图片
 * @param {String}token  图片的token
 */
function getPicFile(token: any) {
  //   getPic(token).then((res: any) => {

  loading.value = true
  //获得图片的接口,更改picURL
  downloadImage(token) // /sys-storage/download_image  f8s
    .then((res: any) => {
      // if (res.status) {
      let objecturl = window.URL.createObjectURL(res)
      picSrc.value = objecturl
      loading.value = false
      // }
    })
    .catch(() => {
      loading.value = false
    })
}
/**
 * @description: 处理视频和音频
 * @param {String}token  token
 */
function getVideoFile(token: any) {
  loading.value = true
  downLoadFile(token)
    .then((r: any) => {
      videoUrl.value = window.URL.createObjectURL(r.data)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
function mediaUrl(token: any) {
  let url = getUrl({ f8s: token })
  return downloadUrl.value + url
}
/**
 * @description: 初始化播放器，已存在时改变资源地址
 * @param {*} token
 */
// const videoPlayerRef= ref<HTMLVideoElement | null>(null)
const videoPlayerRef: any = ref<HTMLVideoElement | null>(null)
function initVideoPlayer(token: any) {
  if (videoPlayer.value) {
    videoPlayer.value.src = mediaUrl(token)
    return
  }
  videoPlayer.value.src = new Player({
    el: videoPlayerRef,
    url: mediaUrl(token),
    lang: 'zh-cn',
    height: '100%',
    width: '100%',
    playbackRate: [0.5, 0.75, 1, 1.5, 2],
  })
}
function clearPlayer() {
  if (videoPlayer.value) {
    videoPlayer.value.destroy()
    videoPlayer.value.videoPlayer = null
  }
}
defineExpose({
  clearPlayer,
  initVideoPlayer,
})
/**
 * @description: txt文件预览。先获取blob流再用FileReader
 * @param {String}token  token
 */
function getTxtFile(token: any) {
  loading.value = true
  downLoadFile(token)
    .then((r: any) => {
      const reader = new FileReader()
      reader.onload = () => {
        txtPre.value = reader.result //获取的数据data
      }
      reader.readAsText(r.data)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
function initState() {
  fileType.value = NO_FILE.value
}

function initData() {
  initState()
  if (file.value.fileToken) {
    let ftoken = file.value.fileToken

    token.value =ftoken
    console.log(token.value, '@@token') // 6c93b90964ed43857dc677c79d978962

    fileType.value = getFileType()
    if (fileType.value == PIC_FILE.value) {
      getPicFile(ftoken)
    }
    if (fileType.value == PDF_FILE.value) {
      getPdfFile(ftoken)
    }
    if (fileType.value == EXCEL_FILE.value) {
      getExcelFile(ftoken)
    }
    if (fileType.value == VIDEO_FILE.value) {
      getVideoFile(ftoken)
      /*nextTick(() => {
            initVideoPlayer(ftoken)
          })*/
    }
    if (fileType.value == TXT_FILE.value) {
      getTxtFile(ftoken)
    }
    if (fileType.value == ELSE_FILE.value) {
      loading.value = false
    }
  }
}

// end
onMounted(() => {
  initData()
})

onUnmounted(() => {})
</script>
<template>
  <div v-loading="loading" class="owa_container">
    <div v-if="fileType == PIC_FILE" ref="pic_frame" class="pic_frame">
      <img ref="pic_img" :src="picURL" class="pic_img" AutoSize:true />
    </div>
    <div v-if="fileType == PDF_FILE" class="pdf_frame" style="height: 100%">
      <iframe id="pdf_frame" name="pdf_frame" :src="pdfURL" />
    </div>
    <div class="pdf_frame" v-if="fileType == EXCEL_FILE" style="height: 100%">
      <vue-office-excel :src="excelURL" style="height: 100%;" />
    </div>
    <div v-if="fileType == VIDEO_FILE" class="video_frame" style="height: 100%">
      <!--      <div v-if="fileType == VIDEO_FILE" id="videoPlayer" ref="player" />-->
      <video :src="videoUrl" controls="true" width="100%"></video>
    </div>
    <div v-if="fileType == TXT_FILE">
      <pre>{{ txtPre }}</pre>
    </div>
    <div v-if="fileType == ELSE_FILE" class="else_frame" style="height: 100%">
      <svg-icon :icon-class="getFileIconExtName(extName)" class="else_icon" />
      <section style="text-align: center; min-height: 40px; line-height: 40px">
        {{ fileName }}
      </section>
    </div>
  </div>
</template>
<style lang="css" scoped>
.owa_container {
  color: #fff;
}
.owa_container {
  display: block;
  width: 100%;
  margin: 0;
  height: calc(100%);
  border: none;
}
#pdf_frame {
  display: block;
  width: 100%;
  height: 100%;
  border: none;
  margin: 0;
}
.pic_frame {
  display: block;
  width: 100%;
  height: 100%;
  border: none;
  margin: 0;
  text-align: center;
}
.pic_img {
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
}
.else_icon {
  width: 100%;
  height: calc(100% - 40px);
}
</style>
