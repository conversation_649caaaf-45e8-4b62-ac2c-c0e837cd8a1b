<script setup lang="ts">
import { ref, computed } from 'vue'
/* 文件预览 */
import PreView from '~/router/components/PreView/index.vue'
import CommonDialog from '~/router/components/CommonDialog/index.vue'

// 透传所有 CommonDialog 的属性，并设置一些默认值
const props = defineProps({
  title: {
    type: String,
    default: '预览',
  },
  width: {
    type: String,
    default: '1200px',
  },
  height: {
    type: String,
    default: 'auto',
  },
  customClass: {
    type: String,
    default: '',
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  showFullscreen: {
    type: Boolean,
    default: true,
  },
})

// 合并自定义类名
const dialogClass = computed(() => {
  return ['previewDialog', props.customClass].filter(Boolean).join(' ');
})

const dialogData = ref<Record<string, any>>({
  previewVisible: false,
  currentFileToken: '',
  fileTokens: [], // 存储多个文件token
  currentIndex: 0, // 当前查看的文件索引
})

// 定义所有 CommonDialog 可能触发的事件
const emit = defineEmits(['fullscreen-change'])

// 处理文件token输入，支持字符串或数组
function processFileTokens(fileTokenInput: string | string[]): string[] {
  if (!fileTokenInput) return [];
  
  // 如果是字符串，按逗号分割
  if (typeof fileTokenInput === 'string') {
    return fileTokenInput.split(',').filter(token => token.trim() !== '');
  }
  
  // 如果是数组，直接返回
  return fileTokenInput;
}

// 打开文件预览（支持单个或多个文件token）
function openPreView(fileTokenInput: string | string[]): void {
  const fileTokens = processFileTokens(fileTokenInput);
  
  if (fileTokens.length === 0) {
    console.warn('文件token不能为空');
    ElMessage.warning('文件token不能为空');
    return;
  }
  
  dialogData.value.fileTokens = fileTokens;
  dialogData.value.currentIndex = 0;
  dialogData.value.currentFileToken = fileTokens[0];
  dialogData.value.previewVisible = true;
}

// 打开多个文件预览（兼容旧接口）
function openMultiPreView(fileTokens: string | string[], startIndex: number = 0): void {
  const tokens = processFileTokens(fileTokens);
  
  if (tokens.length === 0) {
    console.warn('文件token列表不能为空');
    ElMessage.warning('文件token列表不能为空');
    return;
  }
  
  dialogData.value.fileTokens = tokens;
  dialogData.value.currentIndex = Math.min(startIndex, tokens.length - 1);
  dialogData.value.currentFileToken = tokens[dialogData.value.currentIndex];
  dialogData.value.previewVisible = true;
}

// 切换到上一个文件
function prevFile(): void {
  if (dialogData.value.fileTokens.length <= 1) return;
  
  dialogData.value.currentIndex = (dialogData.value.currentIndex - 1 + dialogData.value.fileTokens.length) % dialogData.value.fileTokens.length;
  dialogData.value.currentFileToken = dialogData.value.fileTokens[dialogData.value.currentIndex];
}

// 切换到下一个文件
function nextFile(): void {
  if (dialogData.value.fileTokens.length <= 1) return;
  
  dialogData.value.currentIndex = (dialogData.value.currentIndex + 1) % dialogData.value.fileTokens.length;
  dialogData.value.currentFileToken = dialogData.value.fileTokens[dialogData.value.currentIndex];
}

// 暴露方法给父组件
defineExpose({
  openPreView,
  openMultiPreView,
  prevFile,
  nextFile,
})

// 关闭预览弹框
function closePreview() {
  dialogData.value.previewVisible = false
  dialogData.value.currentFileToken = ''
  dialogData.value.fileTokens = []
  dialogData.value.currentIndex = 0
}

// 处理全屏状态变化
function handleFullscreenChange(isFullscreen: boolean) {
  emit('fullscreen-change', isFullscreen)
}

// 计算是否显示导航按钮
const showNavigation = computed(() => {
  return dialogData.value.fileTokens.length > 1;
})
</script>

<template>
  <div>
    <CommonDialog
      v-model="dialogData.previewVisible"
      :title="props.title"
      :width="props.width"
      :height="props.height"
      :showFullscreen="props.showFullscreen"
      :showClose="props.showClose"
      :customClass="dialogClass"
      @closed="closePreview"
      @fullscreen-change="handleFullscreenChange"
    >
      <!-- 使用插槽透传 -->
      <template v-if="$slots['header-left']" #header-left>
        <slot name="header-left"></slot>
      </template>
      
      <template v-if="$slots['header-right']" #header-right>
        <slot name="header-right"></slot>
      </template>
      
      <div class="preview-container">
        <!-- 左侧导航按钮 -->
        <div v-if="showNavigation" class="nav-button prev-button" @click="prevFile">
          <el-icon><arrow-left /></el-icon>
        </div>
        
        <!-- 文件预览 -->
        <pre-view :file-token="dialogData.currentFileToken" class="pre-view-con" />
        
        <!-- 右侧导航按钮 -->
        <div v-if="showNavigation" class="nav-button next-button" @click="nextFile">
          <el-icon><arrow-right /></el-icon>
        </div>
      </div>
      
      <slot name="bottom"></slot>
      
      <!-- 文件导航指示器 -->
      <div v-if="showNavigation" class="file-indicator">
        {{ dialogData.currentIndex + 1 }} / {{ dialogData.fileTokens.length }}
      </div>
      
      <template v-if="$slots.footer" #footer>
        <slot name="footer"></slot>
      </template>
    </CommonDialog>
  </div>
</template>

<style lang="scss" scoped>
.pre-view-con {
  height: calc(75vh - 96px);
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.prev-button {
  left: 10px;
}

.next-button {
  right: 10px;
}

.file-indicator {
  text-align: center;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}
</style>