<!--建设总览 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  computed,
  watch // 导入 watch
} from 'vue'
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import Carousel from '~/router/components/Carousel/index.vue'
import PreviewDialog from '~/router/components/PreviewDialog/index.vue'
import PageButton from '~/router/layout/PageButton.vue'

import { getFiles } from '~/utils/api/file'
import { queryProjectNews } from './api'
import { getDict } from '~/utils/app'
import { usePortalStore } from '~/stores/portal'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { useFreedo } from '~/router/useFreedo'
import { useFreedoStore } from '~/stores/freedoStore'

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return dayjs(dateString).format('YYYY-MM-DD');
};

interface DictItem {
  code: string;
  'zh-CN': string;
  en: string;
}

interface DictCategory {
  code: string;
  'zh-CN': string;
  en: string;
  child: DictItem[];
}

interface GetDictResponse {
  data: {
    VisualCockpit_ConstructionOverview: DictCategory[];
  };
}

// 使用 Composable
const { isFreedoReady, freedoPlayer, freedoApi } = useFreedo()
const freedoStore = useFreedoStore()

// 添加漫游相关状态变量
const isPlaying = ref(false)

// 使用 portal store
const portalStore = usePortalStore()

const overviewSections = ref<Record<string, { title: string; items: Array<{ label: string; value: string }> }>>({});

const getOverviewDictionary = async () => {
  try {
    const res: GetDictResponse = await getDict({ code: 'VisualCockpit_ConstructionOverview' });

    if (res && res.data && res.data.VisualCockpit_ConstructionOverview) {
      const sectionsData = res.data.VisualCockpit_ConstructionOverview;
      const sections: Record<string, { title: string; items: Array<{ label: string; value: string }> }> = {};

      sectionsData.forEach((category) => {
        if (typeof category.code === 'string' && Array.isArray(category.child)) {
          const sectionTitle = category.code;

          const items = category.child.map((item) => {
            if (typeof item.code === 'string' && typeof item['zh-CN'] === 'string') {
              return {
                label: item.code,
                value: item['zh-CN'],
              };
            }
            return null;
          }).filter((item): item is { label: string; value: string } => item !== null);

          if (items.length > 0) {
            sections[sectionTitle] = {
              title: sectionTitle,
              items: items,
            };
          }
        }
      });
      overviewSections.value = sections;
    }
  } catch (error) {
    console.error('Failed to get overview dictionary:', error);
    ElMessage.error('获取概览信息失败');
  }
};

// 播放姚家平漫游
const playYJPAnimation = async () => {
  if (!isFreedoReady.value || !freedoApi.value?.camera) {
    ElMessage.warning('飞渡API尚未就绪，无法播放漫游');
    return;
  }
  
  try {
    // 使用store中的动画列表
    const animations = freedoStore.animationList;
    
    // 检查是否有动画可播放
    if (animations.length > 0) {
      const playTarget = animations.find(item => item.name === '场景漫游');
      
      
      if (playTarget && freedoApi.value?.camera?.playAnimation) {
        freedoPlayer.value.fullscreen = !freedoPlayer.value.fullscreen;
        await freedoStore.freedoApi?.camera?.stop();
        await freedoApi.value.camera.playAnimation(playTarget.id);
        isPlaying.value = true;
        isShowBack.value = true;
      }
    } else {
      ElMessage.warning('没有可播放的漫游动画');
    }
  } catch (error) {
    console.error('播放姚家平漫游失败:', error);
  }
};

// 暂停漫游
const pauseAnimation = async () => {
  if (!isFreedoReady.value) {
    ElMessage.warning('飞渡API尚未就绪，无法暂停漫游');
    return;
  }
  
  try {
    if (freedoApi.value.camera.pauseAnimation) {
      await freedoApi.value.camera.pauseAnimation();
      await freedoApi.value?.tileLayer?.stopHighlightAllActors();
      await freedoApi.value?.reset(1 | 2 | 4);
      isPlaying.value = false;
    } else {
      ElMessage.warning('当前飞渡API不支持 pauseAnimation 方法');
    }
  } catch (error) {
    ElMessage.error('暂停漫游失败:' + error);
  }
};

// ClickItem 接口定义应在此处，确保只有一个
interface ClickItem {
  subProjectId: string;
  portalId: string;
  portalTitle: string;
  siteId: string;
  position?: number[];
  target?: number[];
  duration?: number;
}

const isShowBack = ref(false) // 初始不显示返回，当进入子场景或飞到点位时设为 true
const isClickItem = ref<ClickItem | null>(null); // 存储当前点击/激活的3D对象信息

const ITEMS_PER_PAGE = 3;

// newsItems 包含新闻信息，从API获取
const newsItems = ref<NewsItem[]>([]);
const loading = ref(false);
const queryParams = ref({
  page: 1,
  size: 9,
  portals: ''
});

// PreviewDialog组件引用
const previewDialogRef = ref<{
  openPreView: (token: string) => void
}>();

// 从API获取新闻数据
const fetchNewsData = async () => {
  try {
    loading.value = true;
    
    // 从 portal store 获取门户数据
    const storePortals = portalStore.portals || [];
    if (storePortals.length > 0) {
      // 获取门户ID列表
      const portalIds = storePortals.map((portal: any) => portal.id).join(',');
      queryParams.value.portals = portalIds;
    } else {
      console.warn('未找到门户数据');
    }
    
    const response = await queryProjectNews(queryParams.value);
    if (response && response.data && response.data.records) {
      // 先创建初始新闻数据
      const initialNewsItems = response.data.records.map((item: any) => ({
        id: item.id,
        name: item.name || '无标题',
        thumbnailFileUpload: item.thumbnailFileUpload, // 保存缩略图文件token
        attFileUpload: item.attFileUpload, // 保存详情文件token
        image: '/img/default-news.png',
        newsTime: item.newsTime || new Date().toISOString()
      }));
      
      // 收集所有需要获取图片和详情文件的文件token
      const thumbnailTokens = initialNewsItems
        .filter((item: any) => item.thumbnailFileUpload)
        .map((item: any) => item.thumbnailFileUpload);
      
      const detailTokens = initialNewsItems
        .filter((item: any) => item.attFileUpload)
        .map((item: any) => item.attFileUpload);
      
      const g9sAry = [...thumbnailTokens, ...detailTokens].filter(Boolean);
      
      // 如果有文件token，通过getFiles获取真实图片URL和详情文件token
      if (g9sAry.length > 0) {
        try {
          const filesResponse = await getFiles({
            g9s: g9sAry
          });
          // 如果成功获取文件信息，更新新闻项的图片URL和详情文件token
          if (filesResponse && filesResponse.data) {
            initialNewsItems.forEach(newsItem => {
              // 处理缩略图文件
              if (newsItem.thumbnailFileUpload) {
                // 查找对应的文件信息
                const fileInfo = filesResponse.data.find(
                  (file: any) => file.groupToken === newsItem.thumbnailFileUpload
                );
                
                // 如果找到文件信息，更新图片URL
                if (fileInfo) {
                  newsItem.image = '/api/sys-storage/download_image?f8s=' + fileInfo.fileToken;
                }
              }
              
              // 处理详情文件
              if (newsItem.attFileUpload) {
                // 查找对应的文件信息
                const fileInfo = filesResponse.data.find(
                  (file: any) => file.groupToken === newsItem.attFileUpload
                );
                
                // 如果找到文件信息，设置详情文件token
                if (fileInfo) {
                  newsItem.detailFileToken = fileInfo.fileToken;
                }
              }
            });
          }
        } catch (fileError) {
          console.error('获取文件信息失败:', fileError);
        }
      }
      
      // 更新新闻列表
      newsItems.value = initialNewsItems;
    }
  } catch (error) {
    console.error('获取新闻数据失败:', error);
    newsItems.value = []; // 出错时设置为空数组
  } finally {
    loading.value = false;
  }
};

// 定义新闻项接口
interface NewsItem {
  id: string;
  name: string;
  image: string;
  newsTime: string;
  thumbnailFileUpload?: string;
  attFileUpload?: string;
  detailFileToken?: string;
}

// 显示新闻详情
const showNewsDetail = (newsItem: NewsItem) => {
  if (newsItem && newsItem.detailFileToken) {
    previewDialogRef.value?.openPreView(newsItem.detailFileToken);
  } else {
    console.warn('该新闻没有详情文件');
  }
};

// 将新闻数据分页
const pagedNewsItems = computed(() => {
  const pages = [];
  const currentNews = newsItems.value;
  if (!currentNews || currentNews.length === 0) return [];
  for (let i = 0; i < currentNews.length; i += ITEMS_PER_PAGE) {
    pages.push(currentNews.slice(i, i + ITEMS_PER_PAGE));
  }
  console.log('pages====>', pages)
  return pages;
});

const backFullScene = async () => {
  console.log('返回全景')
  if (isFreedoReady.value) {
    try {
      await freedoApi.value.camera.pauseAnimation();
      await freedoApi.value?.tileLayer?.stopHighlightAllActors();
      await freedoApi.value?.reset(1 | 2 | 4);
      isClickItem.value = null;
      isShowBack.value = false;
    } catch (error) {
      console.error('Failed to exit world / reset view:', error);
    }
  } else {
    console.warn('Freedo API not ready for backFullScene.');
    isClickItem.value = null;
    isShowBack.value = false;
  }
}

// 初始的 disableActions，现在依赖 Freedo API
const initialSetup = async () => {
  // 可以添加其他依赖 API 的初始设置
  try {
    await freedoApi.value?.camera?.enterWorld();
  } catch (error) {
    console.error('Failed to enter world during initial setup:', error);
  }
};


watch(isFreedoReady, async (ready) => {
  if (ready) {
    await initialSetup();
    
    // 监听点击事件
     watch(
      () => freedoStore.freedoClickedObject,
      (newClickedObject) => {
        if (newClickedObject && newClickedObject.eventtype === 'LeftMouseButtonClick') {
          console.log('原始点击事件数据:', newClickedObject);
        }
      },
      { deep: true }
    );
    console.log('freedoStore', freedoStore);
  } else {
    console.log('Freedo API (isFreedoReady) not yet true in overview/index.vue, waiting...');
  }
}, { immediate: true });

const handleClick = async(params) => {
  console.log(params);
  if (freedoStore.layerTree.length > 0) {
    isShowBack.value = true;
    const target = freedoStore.layerTree.find(item => item.name === params.objectName);
    console.log('target', target.iD);

    await freedoStore.freedoApi?.camera?.stop();

    await freedoApi.value?.tileLayer?.stopHighlightAllActors();

    if (freedoStore.animationList.length > 0) {
      const playTarget = freedoStore.animationList.find(item => item.name === params.playName);
      if (playTarget) {
        await freedoApi.value.camera.playAnimation(playTarget.id);
      }
    }
    
    const ObjectIDsRes = await freedoApi.value.tileLayer.getObjectIDs(target.iD);
    const ObjectData = ObjectIDsRes.data || [];
    await freedoApi.value?.settings.highlightColor('#0000ff');
    
    await freedoApi.value?.tileLayer.highlightActors(ObjectData);
  } else {
    ElMessage.warning('没有可播放的漫游动画');
  }
}

// 页面加载时获取数据
onMounted(() => {
  getOverviewDictionary();
  fetchNewsData();
});
</script>

<template>
  <PageLayout 
    :showBackButton="!!isShowBack" 
    @back="backFullScene"
  >
    <template #left>
      <Card v-if="overviewSections['工程概况']" :title="overviewSections['工程概况'].title" style="height: 46%">
        <div class="overview-content">
          <p v-for="(item, index) in overviewSections['工程概况'].items" :key="index">
            {{ item.value }}
          </p>
        </div>
      </Card>
      <div v-else v-loading="!overviewSections['工程概况']" style="height: 46%">
      </div>

      <Card v-if="overviewSections['基本信息']" :title="overviewSections['基本信息'].title" style="flex:1;">
        <div class="basic-info">
          <div class="info-item" v-for="(item, index) in overviewSections['基本信息'].items" :key="index">
            <div class="item-text">
              <template v-if="item.label === '大坝坝型' && item.value.includes(' ')">
                <div class="text-content">
                  <span>{{ item.value.split(' ')[0] }}</span>
                  <span>{{ item.value.split(' ')[1] }}</span>
                </div>
              </template>
              <template v-else>
                <span>{{ item.value }}</span>
              </template>
            </div>
          </div>
        </div>
      </Card>
      <div v-else v-loading="!overviewSections['基本信息']" style="flex:1;">
      </div>
    </template>
    
    <template #right>
      <Card v-if="overviewSections['参建单位']" :title="overviewSections['参建单位'].title" style="height: 41%">
        <div class="unit-content">
          <div class="unit-item" v-for="(item, index) in overviewSections['参建单位'].items" :key="index">
            <div class="unit-label">{{ item.label }}</div>
            <div class="unit-text">{{ item.value }}</div>
          </div>
        </div>
      </Card>
      <div v-else v-loading="!overviewSections['参建单位']" style="height: 41%">
      </div>
      <Card title="新闻通知" style="min-height: 43%;">
        <div class="news-content" v-loading="loading">
          <Carousel 
            :pageItems="pagedNewsItems"
            :autoplay="true" 
            :interval="4000"
            :showControls="true"
            style="height: 100%;" 
          >
            <template v-slot="{ item: pageOfNews, index: pageIndex }"> 
              <div class="news-page-container" :key="pageIndex">
                <div v-if="!pageOfNews.length" class="empty-container">
                  暂无数据
                </div>
                <div v-else v-for="(singleNewsItem, newsIdx) in pageOfNews" :key="newsIdx" class="news-item" @click="showNewsDetail(singleNewsItem)">
                  <div class="news-image" :style="{ backgroundImage: `url(${singleNewsItem.image})` }"></div>
                  <div class="news-item-content">
                    <div class="news-title">{{ singleNewsItem.name }}</div>
                    <div class="news-time">{{ formatDate(singleNewsItem.newsTime) }}</div>
                  </div>
                </div>
              </div>
            </template>
          </Carousel>
        </div>
      </Card>
      <Card title="姚家平漫游" style="flex: 1;">
        <!-- 姚家平漫游按钮 -->
        <div class="roam-controls">
          <div 
            class="roam-content" 
            @click="!isPlaying ? playYJPAnimation() : pauseAnimation()"
          >
            <div class="video-img"></div>
            <div class="video-title">
              {{ isPlaying ? '姚家平漫游' : '姚家平漫游' }}
            </div>
          </div>
        </div>
      </Card>
    </template>

    <template #append>
      <PageButton @click="handleClick"/>
    </template>
    <PreviewDialog ref="previewDialogRef" />
  </PageLayout>
</template>

<style lang="scss" scoped>
.overview-content {
  font-size: 16px;
  color: #DDEDF4;
  line-height: 26px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  
  p {
    padding: 0;
    margin: 0 0 8px 0;
    text-indent: 2em;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.basic-info {
  padding: 0 10px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  row-gap: 10px;
  column-gap: 24px;
  width: 100%;
  height: 100%;

  .info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-size: 100% 100%;
    border-radius: 4px;
    padding: 10px;
    position: relative;
    color: #fff;
    opacity: 0.8;
    
    &:nth-child(1) {
      background-image: url('~/assets/images/overview/basic-1.png');
    }
    
    &:nth-child(2) {
      background-image: url('~/assets/images/overview/basic-2.png');
    }
    
    &:nth-child(3) {
      background-image: url('~/assets/images/overview/basic-3.png');
    }
    
    &:nth-child(4) {
      background-image: url('~/assets/images/overview/basic-4.png');
    }
    
    &:nth-child(5) {
      background-image: url('~/assets/images/overview/basic-5.png');
    }
    
    &:nth-child(6) {
      background-image: url('~/assets/images/overview/basic-6.png');
    }
    
    &:nth-child(7) {
      background-image: url('~/assets/images/overview/basic-7.png');
    }
    
    &:nth-child(8) {
      background-image: url('~/assets/images/overview/basic-8.png');
    }
    
    &:nth-child(9) {
      background-image: url('~/assets/images/overview/basic-9.png');
    }

    .item-text {
      margin-top: 16px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
      text-align: center;
    }
    .text-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}

.unit-content {
  text-align: left;
  font-style: normal;
  text-transform: none;
  .unit-item {
    width: 100%;
    display: flex;
    margin-bottom: 10px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  .unit-label {
    width: 68px;
    flex-shrink: 0;
    font-weight: normal;
    font-size: 16px;
    color: #DDEDF4;
    margin-right: 20px;
  }
  .unit-text {
    font-size: 16px;
    color: #DDEDF4;
    line-height: 24px;
    text-align: justify;
  }
}

.news-content {
  width: 100%;
  height: 100%;
  
  .news-item {
    width: 100%;
    height: 100%;
    display: flex;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 4px;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }
    
    &:active {
      background-color: rgba(255, 255, 255, 0.05);
      transform: translateY(0);
    }

    &:last-child {
      margin-bottom: 0;
    }
    
    .news-image {
      width: 112px;
      height: 90px;
      flex-shrink: 0;
      margin-right: 12px;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      background-color: #fff;
      border-radius: 4px;
    }
    
    .news-item-content {
      padding: 8px 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .news-title {
        font-size: 16px;
        color: #FFFFFF;
        line-height: 24px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .news-time {
        font-size: 12px;
        color: #CCCCCC;
        line-height: 18px;
        text-align: left;
        margin-top: 8px;
      }
    }
  }
}

.roam-content {
  width: 100%;
  height: 54px;
  background: linear-gradient(180deg, rgba(33,150,204,0.8) 0%, rgba(38,124,154,0.63) 100%);
  box-shadow: inset 0px 6px 14px 0px rgba(7,47,72,0.72), 0px 2px 6px 0px rgba(44,134,183,0.25);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(360deg, rgba(88, 211, 255, 1), rgba(53, 126, 153, 1)) 1 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
  &:hover {
    background-color: #e0e0e0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  &:active {
    background-color: #d0d0d0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .video-img {
    background-image: url('~/assets/images/overview/video-bg.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 24px;
    height: 24px;
    margin-right: 10px;
  }
  .video-title {
    font-weight: 400;
    font-size: 22px;
    color: #FFFFFF;
    letter-spacing: 1px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
} 
</style>