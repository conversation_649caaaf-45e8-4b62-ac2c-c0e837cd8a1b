<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watchEffect, watch, reactive } from "vue";
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { graphic } from "echarts";
import { usePortalStore } from "~/stores";
import { getFile, downloadImage } from "~/utils/api/file";
import dayjs from "dayjs";
const props = defineProps({});
const fetchData = () => {};

watchEffect(() => {});
// 生命周期钩子
onMounted(() => {
  fetchData();
});
onBeforeUnmount(() => {});
</script>
<template>
  <div class="LimitWarning">
    <div v-for="item in 30">
      <span>{{ item }}、监测点名称</span>
      <span>监测量</span>
      <span>报警内容</span>
      <span>2月13日</span>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.LimitWarning {
  width: 100%;
  height: 100%;
  overflow: auto;
  > div {
    margin-bottom: 13px;
    display: flex;
    gap: 16px;
    span {
      color: #bfe4ff;
      line-height: 22px;
      font-size: 14px;
      font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
      &:first-child {
      }
      &:nth-child(2) {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
      }
      &:nth-child(3) {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        color: #fd6727;
        flex: 1;
      }
      &:last-child {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
      }
    }
  }
}
</style>
