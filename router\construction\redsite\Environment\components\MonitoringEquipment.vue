<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { getFile, downloadImage } from '~/utils/api/file'
import dayjs from 'dayjs'
import { sysEnvTree } from '../api'
const props = defineProps({})
const treeData = ref([])
const dialogVisible = ref(false)
const fetchData = () => {
  sysEnvTree().then((res: any) => {
    treeData.value = res.fixNavigationDTOs
  })
}

watchEffect(() => {})
const filterText = ref('')
const treeRef = ref(null)
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.includes(value)
}
const handleNodeClick = (data) => {
  console.log('🚀 ~ handleNodeClick ~ data:', data)
  // dialogVisible.value = true;
}
watch(filterText, (newValue) => {
  treeRef.value!.filter(newValue)
})
// 生命周期钩子
onMounted(() => {
  fetchData()
})
onBeforeUnmount(() => {})
</script>
<template>
  <div class="MonitoringEquipment">
    <el-input placeholder="输入名称" v-model="filterText" clearable />
    <el-tree
      :data="treeData"
      :props="defaultProps"
      default-expand-all
      ref="treeRef"
      :highlight-current="true"
      :filter-node-method="filterNode"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <div class="el-tree-node__label">
          <img
            src="../img/tree_icon.png"
            v-if="data.children && data.children.length > 0"
          />
          <img src="../img/tree_icon_leaf.png" v-else />
          <span>{{ data.name }}</span>
        </div>
      </template>
    </el-tree>
    <el-dialog v-model="dialogVisible" title="测点名称测点详情" width="50%">
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.MonitoringEquipment {
  width: 100%;
  height: 100%;
  overflow: hidden;
  :deep(.el-input) {
    margin-bottom: 15px;
    .el-input__wrapper {
      background: linear-gradient(
        90deg,
        #1d9ed9 0%,
        rgba(19, 158, 223, 0.09) 100%
      );
      box-shadow: inset 0px 2px 5px 0px rgba(8, 68, 105, 0.59),
        0px 2px 6px 0px rgba(44, 134, 183, 0.25);
      border-radius: 3px;
      border: none;
    }
    .el-input__inner {
      color: #fff;
      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
        opacity: 1;
      }
    }
  }
  :deep(.el-tree) {
    height: calc(100% - 50px);
    overflow: auto;
    background-color: transparent;
    .el-tree-node.is-current > .el-tree-node__content {
      background-color: rgba(0, 123, 255, 0.1) !important;
    }
    .el-tree-node__content {
      background-color: transparent;
      height: 40px !important;
      &:hover {
        background-color: rgba(0, 123, 255, 0.1) !important;
      }
    }
    .el-tree-node__expand-icon {
      color: #fff;
      position: relative;
    }
    .el-tree-node__label {
      color: #fff;
      font-size: 16px;
      display: flex;
      align-items: center;
      span {
        flex: 1;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
  }
}
</style>
