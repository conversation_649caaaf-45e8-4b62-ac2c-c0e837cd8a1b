<!--智慧建设-》 安全 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onUnmounted,
  computed,
  watch // 导入 watch
} from 'vue'
// 假设 disableActions 是一个通用工具，如果它也依赖 Freedo，可能需要调整
// import { disableActions } from '~/utils/disableActions.ts'
// import { queryProjectNews } from './api'
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import MonitoringEquipment from './components/MonitoringEquipment.vue'
import LimitWarning from './components/LimitWarning.vue'
import MonitoringEquipmentStatistics from './components/MonitoringEquipmentStatistics.vue'
// 1. 导入 useFreedo
import { useFreedo } from '../../../useFreedo'
import { type MarkerObject, type ClickedObjectInfo } from '../../freedo' // 导入类型
// 导入 portal store
import { usePortalStore } from '~/stores/portal'

// 2. 使用 Composable
const { isApiReady, flyTo, enterWorld, exitWorld, api: freedoApiRef, clickedObject } = useFreedo()

// 使用 portal store
const portalStore = usePortalStore()


// ClickItem 接口定义应在此处，确保只有一个
interface ClickItem {
  subProjectId: string;
  portalId: string;
  portalTitle: string;
  siteId: string;
  position?: number[];
  target?: number[];
  duration?: number;
}

const isShowBack = ref(false) // 初始不显示返回，当进入子场景或飞到点位时设为 true
const isClickItem = ref<ClickItem | null>(null); // 存储当前点击/激活的3D对象信息
const newsItems = ref([]);
// 页面加载时获取数据
onMounted(() => {
});



const backFullScene = async () => {
  console.log('返回全景')
  if (isApiReady.value) {
    try {
      await exitWorld() // 假设 exitWorld() 会重置到主视角
      console.log('Exited world / Reset view successful')
      isClickItem.value = null // 清除点击项状态
      isShowBack.value = false
    } catch (error) {
      console.error('Failed to exit world / reset view:', error)
    }
  } else {
    console.warn('Freedo API not ready for backFullScene.')
    isClickItem.value = null // 即使API未就绪，也应清除状态
    isShowBack.value = false
  }
}

// 飞到与新闻项关联的3D点位
const flyToAssociatedLocation = async (item: typeof newsItems.value[0]) => {
  if (!item.freedoParams) {
    console.warn(`Item ${item.title} does not have Freedo parameters defined.`);
    return;
  }
  console.log(`Attempting to fly to location for: ${item.title}`);
  if (isApiReady.value) {
    try {
      await flyTo(item.freedoParams);
      console.log(`Successfully flew to location for ${item.title}`);
      // 更新 isClickItem 以反映当前关注的3D对象/点位
      isClickItem.value = {
        portalId: item.pointName, // 或者其他唯一标识
        portalTitle: item.title,  // 用于返回按钮标题
        subProjectId: '', // 根据需要填充
        siteId: '',       // 根据需要填充
        ...item.freedoParams // 将相机参数也存起来，如果需要
      };
      isShowBack.value = true; // 显示返回按钮
    } catch (error) {
      console.error(`Failed to fly to location for ${item.title}:`, error);
    }
  } else {
    console.warn('Freedo API not ready for flyToAssociatedLocation.');
  }
};


// 假设这是用于处理从3D场景接收到的点击事件的函数
// 这个函数需要由飞渡SDK的事件回调来触发
const handleSceneClick = async (clickedData: ClickItem) => {
  console.log('3D scene object clicked:', clickedData);
  isClickItem.value = clickedData;
  isShowBack.value = true;

  // 尝试根据点击数据中的 portalId (或其他标识) 找到匹配的 newsItem 并切换视频
  const foundItem = newsItems.value.find(videoItem => videoItem.pointName === clickedData.portalId || videoItem.title === clickedData.portalTitle);
  if (foundItem) {
    // currentVideo.value = foundItem;
    // 如果点击的3D对象有特定的相机参数，可以优先使用它们飞行
    if (clickedData.position) {
        await flyTo(clickedData);
    } else if (foundItem.freedoParams) {
        await flyTo(foundItem.freedoParams);
    }
  } else if (clickedData.position) {
    // 如果没有匹配的视频项，但有位置信息，也飞过去
    await flyTo(clickedData);
  }
};


// 初始的 disableActions，现在依赖 Freedo API
const initialSetup = async () => {
  // if (freedoApiRef.value && freedoApiRef.value.settings?.setPickMode) {
  //   // freedoApiRef.value.settings.setPickMode(1); // 尝试设置为 1 来启用拾取，具体值请参考SDK文档
  //   // console.log('Pick mode interaction hopefully ENABLED via Freedo API (set to 1).');
  //   console.warn('Security/index.vue: setPickMode is not available on this SDK version. Call skipped.');
  // }
  // 可以添加其他依赖 API 的初始设置
  // 例如，进入主世界
  try {
    await enterWorld();
    console.log('Entered world successfully during initial setup.');
  } catch (error) {
    console.error('Failed to enter world during initial setup:', error);
  }
};

onMounted(() => {
  console.log('Security/index.vue: Component Mounted. Initial isApiReady.value:', isApiReady.value);
  watch(isApiReady, async (ready) => {
      console.log(`Security/index.vue: watch(isApiReady) triggered. New 'ready' value: ${ready}. Current isApiReady.value: ${isApiReady.value}`);
    if (ready) {
      console.log('Security/index.vue: isApiReady is true. Performing initial setup and additional checks for marker/world API...');
      await initialSetup(); // 执行初始设置，例如禁用操作

            // 自动添加预定义标记的逻辑已被注释掉，以测试点击添加标记功能。
      // const checkInterval = 200;
      // const maxAttempts = 25;
      // let attempts = 0;
      // const attemptToAddMarkers = async () => { ... };
      // setTimeout(attemptToAddMarkers, 500);


      // 新增：监听场景点击以添加标记
      watch(clickedObject, (newClickedInfo) => {
        if (newClickedInfo?.point && freedoApiRef.value?.marker?.add) {
          console.log('Security/index.vue: Click detected at:', newClickedInfo.point);
          const clickMarker: MarkerObject = {
            id: `click-marker-${Date.now()}`,
            groupId: 'clickedMarkers',
            coordinate: newClickedInfo.point,
            coordinateType: 0, // 假设是世界坐标
            imagePath: '/images/video-bg.png', // 使用一个默认或显眼的图标
            imageSize: [30, 30],
            anchors: [-15, 30],
            fixedSize: true,
            text: '点击添加',
            fontSize: 14,
            fontColor: [1.0, 1.0, 0.0, 1.0], // 黄色
            textBackgroundColor: [0.1, 0.1, 0.1, 0.7],
            showLine: false,
            range: [1, 100000]
          };
          try {
            freedoApiRef.value.marker.add(clickMarker);
            console.log('Security/index.vue: Click marker added successfully at:', newClickedInfo.point);
          } catch (error) {
            console.error('Security/index.vue: Error adding click marker:', error, clickMarker);
          }
        } else if (newClickedInfo?.point) {
          console.warn('Security/index.vue: Click detected, but marker API not ready or add function missing.');
        }
      }, { deep: true }); // 使用 deep watch 以防 point 数组内部变化不被察觉（虽然通常 ref 的 .value 变化会触发）

    } else {
      console.log('Freedo API (isApiReady) not yet true in Security/index.vue, waiting...');
    }
  }, { immediate: true });
})
</script>
<template>
  <PageLayout
    :showBackButton="!!isShowBack"
    :backTitle="isClickItem ? isClickItem.portalTitle : '返回全景'"
    @back="backFullScene"
  >
    <template #left>
      <Card title="监测设备统计" style="height: 60%">
        <MonitoringEquipmentStatistics />
      </Card>
      <Card title="限值预警" style="height: 40%"><LimitWarning /> </Card>
    </template>
    <template #right>
      <Card title="监测设备" style="height: 100%"><MonitoringEquipment /> </Card>
    </template>
  </PageLayout>
</template>

<style lang="scss" scoped></style>
