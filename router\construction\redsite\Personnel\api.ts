import request from '~/router/request'
import { AxiosPromise } from 'axios'
let SCS_SERVICE_PREFIX = import.meta.env.CVE_VUE_APP_BASE_SCS_SERVICENAME

/**
 * 定位设备统计
 * @param
 * @returns
 */
export function facilityStatistics(): AxiosPromise<any> {
  return request({
    url: `${SCS_SERVICE_PREFIX}/big-screen/personnel/facility`,
    method: 'get',
  })
}

/**
 * 人员轨迹信息查询
 * @param
 * @returns
 */
export function facilityTrack(params: any): AxiosPromise<any> {
  return request({
    url: `${SCS_SERVICE_PREFIX}/big-screen/personnel/facility-track`,
    method: 'get',
    params
  })
}

/**
 * 近一周设备在线率
 * @param
 * @returns
 */
export function weekOnline(): AxiosPromise<any> {
  return request({
    url: `${SCS_SERVICE_PREFIX}/big-screen/personnel/week-online`,
    method: 'get',
  })
}

/**
 * 人员列表
 * @param
 * @returns
 */
export function personnelList(): AxiosPromise<any> {
  return request({
    url: `${SCS_SERVICE_PREFIX}/big-screen/personnel/personnel-list`,
    method: 'get',
  })
}