<template>
  <div
    v-loading="loading"
    ref="chartRef"
    class="online-rate-chart-container"
  ></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { weekOnline } from '../api'

const chartRef = ref<HTMLElement | null>(null)
let myChart: echarts.ECharts | null = null
const loading = ref(false)

const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value)
  }
}

const updateChart = (xAxisData: string[], seriesData: number[]) => {
  if (!myChart) return

  const option: echarts.EChartsOption = {
    grid: {
      left: '3%',
      right: '8%',
      top: '6%',
      bottom: '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.5)',
        },
      },
      axisLabel: {
        color: '#ffffff',
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%',
        color: '#ffffff',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
    series: [
      {
        name: '在线率',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#6BCAF6',
        },
        lineStyle: {
          color: '#6BCAF6',
          width: 3,
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(107, 202, 246, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(107, 202, 246, 0)',
            },
          ]),
        },
        data: seriesData,
      },
    ],
  }
  myChart.setOption(option, true)
}

const fetchData = async () => {
  loading.value = true
  try {
    const res = await weekOnline()
    if (res.data) {
      const xAxisData = res.data.xaxis || []
      const seriesData =
        res.data.data?.map((item: { y: string }) => Number(item.y)) || []
      updateChart(xAxisData, seriesData)
    } else {
      updateChart([], [])
    }
  } catch (error) {
    console.error('Failed to fetch week online rate:', error)
    updateChart([], [])
  } finally {
    loading.value = false
  }
}

const resizeChart = () => {
  myChart?.resize()
}

onMounted(() => {
  initChart()
  fetchData()
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart)
  myChart?.dispose()
})
</script>

<style lang="scss" scoped>
.online-rate-chart-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>

