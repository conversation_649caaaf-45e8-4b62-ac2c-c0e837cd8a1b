<script lang="ts" setup>
import { ref, onMounted, reactive, nextTick } from 'vue'
import * as echarts from 'echarts'
import { facilityStatistics } from '../api'

type ECharts = echarts.ECharts

const pieChartRef = ref<HTMLElement | null>(null)
const barLineChartRef = ref<HTMLElement | null>(null)
let gaugeChart: ECharts | null = null
let barLineChart: ECharts | null = null

const summaryData = reactive({
  partCount: 0,
  totalCount: 0,
  proportion: 0,
})

const chartDetailData = reactive<{
  names: string[]
  onlineCounts: number[]
  offlineCounts: number[]
  rates: number[]
}>({ names: [], onlineCounts: [], offlineCounts: [], rates: [] })

const initGaugeChart = () => {
  if (!pieChartRef.value) return
  gaugeChart = echarts.init(pieChartRef.value)
  const option: echarts.EChartsOption = {
    series: [
      {
        type: 'pie',
        radius: ['65%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: { show: false },
        emphasis: { label: { show: false } },
        labelLine: { show: false },
        data: [
          {
            value: summaryData.proportion,
            name: '已完成比例',
            itemStyle: { color: '#76e7ec' },
          },
          {
            value: 100 - summaryData.proportion,
            name: '剩余比例',
            itemStyle: { color: '#4668e4' },
          },
        ],
      },
      // {
      //   type: 'gauge',
      //   center: ['50%', '50%'],
      //   radius: '100%',
      //   startAngle: 90,
      //   endAngle: -270,
      //   pointer: { show: false },
      //   progress: {
      //     show: true,
      //     overlap: false,
      //     roundCap: true,
      //     clip: false,
      //     itemStyle: {
      //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //         { offset: 0, color: '#00BFFF' },
      //         { offset: 1, color: '#1E90FF' },
      //       ]),
      //     },
      //   },
      //   axisLine: {
      //     lineStyle: {
      //       width: 10,
      //       color: [[1, 'rgba(0, 123, 255, 0.2)']],
      //     },
      //   },
      //   splitLine: { show: false },
      //   axisTick: { show: false },
      //   axisLabel: { show: false },
      //   data: [{ value: summaryData.proportion }],
      //   detail: { show: false },
      // },
      // {
      //   type: 'gauge',
      //   center: ['50%', '50%'],
      //   radius: '75%',
      //   startAngle: 90,
      //   endAngle: -270,
      //   pointer: { show: false },
      //   axisLine: {
      //     lineStyle: {
      //       width: 2,
      //       color: [[1, '#007BFF']],
      //     },
      //   },
      //   progress: { show: false },
      //   splitLine: { show: false },
      //   axisTick: { show: false },
      //   axisLabel: { show: false },
      //   detail: { show: false },
      // },
    ],
  }
  gaugeChart.setOption(option)
}

const initBarLineChart = () => {
  if (!barLineChartRef.value) return
  barLineChart = echarts.init(barLineChartRef.value)
  const option: echarts.EChartsOption = {
    tooltip: { trigger: 'axis' },
    legend: {
      data: ['在线', '离线', '在线率'],
      textStyle: { color: '#ffffff' },
      top: '5%',
      // right: '5%',
      itemWidth: 15,
    },
    grid: { left: '5%', right: '4%', bottom: '2%', containLabel: true },
    xAxis: [
      {
        type: 'category',
        data: chartDetailData.names,
        axisPointer: { type: 'shadow' },
        axisLabel: { color: '#A2C2D4' },
        axisLine: { show: false },
        axisTick: { show: false },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '单位 (个)',
        min: 0,
        max: (value: { max: number }) => Math.ceil(value.max / 25) * 25 || 100,
        interval: 25,
        nameTextStyle: { color: '#A2C2D4', padding: [0, 30, 0, 0] },
        axisLabel: { formatter: '{value}', color: '#A2C2D4' },
        splitLine: {
          lineStyle: { type: 'dashed', color: 'rgba(255,255,255,0.2)' },
        },
      },
      {
        type: 'value',
        name: '在线率 (%)',
        min: 0,
        max: 100,
        show: false,
        axisLabel: { formatter: '{value} %' },
      },
    ],
    series: [
      {
        name: '在线',
        type: 'bar',
        stack: 'total',
        barWidth: '20%',
        itemStyle: { color: '#32D1ED' },
        data: chartDetailData.onlineCounts,
      },
      {
        name: '离线',
        type: 'bar',
        stack: 'total',
        itemStyle: { color: '#E5A152' },
        data: chartDetailData.offlineCounts,
      },
      {
        name: '在线率',
        type: 'line',
        yAxisIndex: 1,
        symbol: 'circle',
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          },
        },
        symbolSize: 8,
        itemStyle: { color: '#4EF17C' },
        lineStyle: { color: '#4EF17C' },
        data: chartDetailData.rates,
      },
    ],
  }
  barLineChart.setOption(option)
}

const fetchData = async () => {
  try {
    const res = await facilityStatistics()
    if (res.data) {
      const data = res.data
      summaryData.partCount = data.partCount
      summaryData.totalCount = data.totalCount
      summaryData.proportion = data.proportion

      if (data.detail) {
        const filteredDetail = data.detail.filter(
          (item: any) => item.name !== '信息化标'
        )
        chartDetailData.names = filteredDetail.map((item: any) => item.name)
        chartDetailData.onlineCounts = filteredDetail.map(
          (item: any) => item.partCount
        )
        chartDetailData.offlineCounts = filteredDetail.map(
          (item: any) => item.remainingCount
        )
        chartDetailData.rates = filteredDetail.map(
          (item: any) => item.proportion
        )
      }
      console.log('chartDetailData======>', chartDetailData)

      await nextTick()
      initGaugeChart()
      initBarLineChart()
    }
  } catch (error) {
    console.error('Failed to fetch device statistics:', error)
  }
}

onMounted(() => {
  fetchData()
  window.addEventListener('resize', () => {
    gaugeChart?.resize()
    barLineChart?.resize()
  })
})
</script>

<template>
  <div class="device-statistics-container">
    <div class="top-section">
      <div class="chart-wrapper">
        <div ref="pieChartRef" class="gauge-chart"></div>
      </div>
      <div class="info-wrapper">
        <div class="info-item accumulated">
          <span class="label">在线监测</span>
          <span class="value">{{ summaryData.partCount }}</span>
          <span class="unit">个</span>
        </div>
        <div class="info-item contract">
          <span class="label">设备监测</span>
          <span class="value">{{ summaryData.totalCount }}</span>
          <span class="unit">个</span>
        </div>
      </div>
    </div>
    <div ref="barLineChartRef" class="bar-line-chart"></div>
  </div>
</template>

<style lang="scss" scoped>
.device-statistics-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.top-section {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .chart-wrapper {
    width: 162px;
    height: 162px;
    margin-right: 16px;
    position: relative;
    background-image: url('../img/img1.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    .gauge-chart {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .info-wrapper {
    flex-grow: 1;
    width: 200px;
    height: 126px;
    padding-left: 24px;
    background-image: url('../img/img2.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .info-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      position: relative;
      padding-left: 22px;
      color: #fff;

      &.accumulated {
        margin-bottom: 24px;
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 18px;
          background: url('../img/img3.svg') no-repeat center center;
          background-size: contain;
        }
      }

      &.contract {
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 18px;
          background: url('../img/img4.svg') no-repeat center center;
          background-size: contain;
        }
      }

      .label {
        min-width: 68px;
        margin-right: 24px;
      }
      .value {
        font-size: 20px;
        font-weight: bold;
        margin-right: 3px;
      }
      .unit {
        color: #bcc5ce;
        font-size: 12px;
      }
    }
  }
}

.bar-line-chart {
  height: 60%;
  width: 100%;
}
</style>