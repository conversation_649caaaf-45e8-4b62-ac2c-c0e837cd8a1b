<template>
  <div class="personnel-list-container">
    <el-input
      v-model="filterText"
      placeholder="输入姓名进行搜索"
      class="filter-input"
      clearable
    />

    <el-tree
      ref="treeRef"
      class="personnel-tree"
      :data="treeData"
      :props="defaultProps"
      :filter-node-method="filterNode"
      default-expand-all
      :expand-on-click-node="false"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <!-- Parent node (section) -->
          <div v-if="!data.isLeaf" class="section-node">
            <img src="../img/img5.svg" class="node-icon" alt="" />
            <span>{{ node.label }}</span>
          </div>

          <!-- Child node (person) -->
          <div v-if="data.isLeaf" class="person-node" @click="showPersonDetail(data)">
            <img src="../img/img6.svg" class="node-icon" alt="" />
            <span class="person-name">{{ data.content.name }}</span>
          </div>
        </div>
      </template>
    </el-tree>
    <!-- 人员详情弹窗 -->
    <CommonDialog
      v-model="dialogVisible"
      :title="currentPerson?.name || '人员'"
      width="600px"
      height="auto"
      :showClose="true"
      :showFullscreen="false"
      customClass="personnel-detail-dialog"
    >
      <div class="personnel-detail">
        <div class="detail-row">
          <div class="detail-label">所属标段</div>
          <div class="detail-value">{{ currentPerson?.portalName || '-' }}</div>
          <div class="detail-label">所属部门</div>
          <div class="detail-value">{{ currentPerson?.orgName || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">岗位</div>
          <div class="detail-value">{{ currentPerson?.dutyName || '-' }}</div>
          <div class="detail-label">联系方式</div>
          <div class="detail-value">{{ currentPerson?.phone || '-' }}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">人员状态</div>
          <div class="detail-value status">
            <span :class="[getStatusClass(currentPerson?.onLineStatus)]">
               {{ getStatusText(currentPerson?.onLineStatus) }}
            </span>
          </div>
          <div class="detail-label"></div>
          <div class="detail-value"></div>
        </div>
        
        <div class="action-buttons">
          <el-button type="primary" class="history-button" @click="checkHistory">历史轨迹查询</el-button>
        </div>
      </div>
    </CommonDialog>

    <CommonDialog
      v-model="trackDialogVisible"
      :title="`${currentPerson?.name || '人员'}的历史轨迹`"
      width="700px"
      height="500px"
      :showClose="true"
      customClass="track-history-dialog"
    >
  
      <template #header-middle>
        <div class="track-controls">
          <div class="hour-input">
            <el-input-number 
              v-model="hourRange" 
              :min="1" 
              :max="72" 
              size="small" 
              @change="updateHourRange"
            />
            <span class="hour-label">小时</span>
          </div>
          <el-tooltip :content="isPlaying ? '暂停' : '播放'">
            <el-button 
              type="primary"
              circle 
              size="small"
              @click="togglePlay"
              :disabled="trackData.length === 0"
            >
              <img 
                :src="isPlaying ? pauseIcon : playIcon" 
                alt="播放控制"
                class="play-control-icon"
              />
          </el-button>
          </el-tooltip>
        </div>
      </template>

      <div class="track-history-container" v-loading="loadingTrackData">
        <div v-if="trackData.length === 0" class="no-data">
          暂无历史轨迹数据
        </div>
        <div v-else class="track-list">
          <div v-for="(item, index) in trackData" :key="item.id" class="track-item">
            <div class="track-time">上报时间: {{ item.locationTime || '-' }}</div>
            <div class="track-coordinates">坐标: {{ item.coordinates || '-' }}</div>
          </div>
        </div>
      </div>
    </CommonDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import type { ElTree } from 'element-plus';
import CommonDialog from '~/router/components/CommonDialog/index.vue';
import { personnelList, facilityTrack } from '../api';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus'
import playIcon from '~/assets/images/common/play.svg'
import pauseIcon from '~/assets/images/common/pause.svg'
import { useFreedo } from '~/router/useFreedo'
import { useFreedoStore } from '~/stores/freedoStore';

interface PersonContent {
  id: string;
  name: string;
  dutyName: string;
  onLineStatus: string;
  department?: string;
  position?: string;
  engineeringDept?: string;
  workType?: string;
  constructionWorker?: string;
  phone?: string;
  [key: string]: any;
}

interface RawPerson {
  id: string;
  name: string;
  content: PersonContent;
}

interface RawSection {
  id: string;
  name: string;
  children: RawPerson[] | null;
}

interface Tree {
  id: string;
  label: string;
  children?: Tree[];
  isLeaf?: boolean;
  content?: PersonContent;
}


// 使用 freedoStore
const freedoStore = useFreedoStore();
const { isFreedoReady } = useFreedo();

const filterText = ref('');
const treeRef = ref<InstanceType<typeof ElTree>>();
const treeData = ref<Tree[]>([]);
const dialogVisible = ref(false);
const currentPerson = ref<PersonContent | null>(null);

// 在组件顶部添加新的响应式变量
const trackDialogVisible = ref(false);
const trackData = ref([]);
const hourRange = ref(1);
const loadingTrackData = ref(false)

const isPlaying = ref(false); // 播放状态
const playInterval = ref(null); // 播放定时器
const currentPlayIndex = ref(0); // 当前播放索引

const defaultProps = {
  children: 'children',
  label: 'label',
};

// 显示人员详情弹窗
const showPersonDetail = (data: Tree) => {
  if (data.content) {
    currentPerson.value = data.content;
    dialogVisible.value = true;
  }
};

// 获取状态样式类
const getStatusClass = (status: string | undefined) => {
  if (!status) return 'status-offline';
  
  switch (status.toLowerCase()) {
    case '0':
    case '在线':
      return 'status-online';
    default:
      return 'status-offline';
  }
};

// 获取状态文本
const getStatusText = (status: string | undefined) => {
  if (!status) return '离线';
  
  switch (status.toLowerCase()) {
    case '0':
    case '在线':
      return '在线';
    default:
      return '离线';
  }
};

// 查看历史轨迹
const checkHistory = async() => {
  if (currentPerson.value) {
    loadingTrackData.value = true
    const params = {
      facilityCode: currentPerson.value.facilityCode,
      portalId: currentPerson.value.portalId,
      endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      startTime: dayjs().subtract(hourRange.value, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    }
    
    try {
      const res = await facilityTrack(params);
      console.log('查看历史轨迹:', res);
      
      if (res.status && res.data && res.data.length > 0) {
        trackData.value = res.data;
        const positionList = res.data.map((item: any) => {
          return item.position ? item.position : []
        });
        console.log('positionList', positionList)

        const coordinatesRes  = await freedoStore.freedoApi?.coord?.gcs2pcs(positionList[0])
        console.log('coordinatesRes', coordinatesRes)
        const coordinates = coordinatesRes?.coordinates || []
        console.log('coordinates', coordinates)

        dialogVisible.value = false; // 关闭人员详情弹窗
        trackDialogVisible.value = true; // 打开轨迹弹窗
        currentPlayIndex.value = 0; // 重置播放索引

        // 使用 freedoStore 绘制轨迹
        if (isFreedoReady.value) {
          await freedoStore.drawTrack(coordinates, {
            groupId: `personnel-track-${currentPerson.value.id}`,
            color: [0, 0.7, 1, 1], // 蓝色
            thickness: 5,
            style: 2, // 光流效果
            // startMarkerPath: '/assets/images/common/start-point.svg',
            // endMarkerPath: '/assets/images/common/end-point.svg',
            // pointMarkerPath: '/assets/images/common/track-point.svg'
          });
        }
      } else {
        ElMessage.info('暂无历史轨迹数据');
      }
      loadingTrackData.value = false
    } catch (error) {
      console.error('获取历史轨迹失败:', error);
      ElMessage.error('获取历史轨迹失败');
      loadingTrackData.value = false
    }
  }
};

// 播放/暂停轨迹
const togglePlay = () => {
  if (isPlaying.value) {
    pausePlay();
  } else {
    startPlay();
  }
};

// 开始播放
const startPlay = () => {
  if (trackData.value.length === 0) return;
  
  isPlaying.value = true;
  
  // 清除之前的定时器
  if (playInterval.value) {
    clearInterval(playInterval.value);
  }
  
  // 设置定时器，每2秒播放下一个点位
  playInterval.value = setInterval(() => {
    if (currentPlayIndex.value < trackData.value.length - 1) {
      currentPlayIndex.value++;
      
      // 使用 freedoStore 飞行到当前轨迹点
      if (isFreedoReady.value) {
        const currentTrack = trackData.value[currentPlayIndex.value];
        freedoStore.flyToTrackPoint(currentTrack);
      }
    } else {
      // 播放完毕，停止播放
      pausePlay();
    }
  }, 2000);
};

// 暂停播放
const pausePlay = () => {
  isPlaying.value = false;
  if (playInterval.value) {
    clearInterval(playInterval.value);
    playInterval.value = null;
  }
};


// 更新小时范围并重新查询
const updateHourRange = () => {
  if (currentPerson.value) {
    checkHistory();
  }
};

const fetchData = async () => {
  try {
    const res = await personnelList();
    if (res.data) {
      treeData.value = res.data.map((section: RawSection) => ({
        id: section.id,
        label: `${section.name} (${section.children?.length || 0})`,
        isLeaf: false,
        children: section.children?.map((person: RawPerson) => ({
          id: person.id,
          label: person.name, // Label for filtering
          isLeaf: true,
          content: person.content,
        })) || [],
      }));
    }
  } catch (error) {
    console.error('Failed to fetch personnel list:', error);
    treeData.value = []; // Clear data on error
  }
};

// 监听对话框关闭事件
watch(trackDialogVisible, (newVal) => {
  if (!newVal && isFreedoReady.value) {
    // 对话框关闭时清除轨迹
    pausePlay();
    freedoStore.clearTrack();
  }
});

watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.toLowerCase().includes(value.toLowerCase());
};

onMounted(() => {
  fetchData();
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (playInterval.value) {
    clearInterval(playInterval.value);
  }
  
  if (isFreedoReady.value) {
    freedoStore.clearTrack();
  }
});
</script>

<style lang="scss" scoped>
.personnel-list-container {
  padding: 10px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.filter-input {
  margin-bottom: 15px;
  :deep(.el-input__wrapper) {
    background: linear-gradient( 90deg, #1D9ED9 0%, rgba(19,158,223,0.09) 100%);
    box-shadow: inset 0px 2px 5px 0px rgba(8,68,105,0.59), 0px 2px 6px 0px rgba(44,134,183,0.25);
    border-radius: 3px;
    border: none;
  }
  :deep(.el-input__inner) {
    color: #fff;
    &::placeholder {
      color: rgba(255, 255, 255, 0.6);
      opacity: 1;
    }
  }
}

.personnel-tree {
  flex-grow: 1;
  background-color: transparent;
  color: #ffffff;
  overflow-y: auto;

  :deep(.el-tree-node__content) {
    background-color: transparent !important;
    height: 40px;
    &:hover {
      background-color: rgba(0, 123, 255, 0.1) !important;
    }
  }

  .custom-tree-node {
    width: 100%;
    display: flex;
    align-items: center;

    .section-node,
    .person-node {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .node-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .section-node {
      font-weight: normal;
    }
  }
}

// Scrollbar styles
.personnel-tree::-webkit-scrollbar {
  width: 6px;
}
.personnel-tree::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}
.personnel-tree::-webkit-scrollbar-track {
  background: transparent;
}

.personnel-detail {
  padding: 10px;
  
  .detail-row {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }
  
  .detail-label {
    width: 80px;
    text-align: right;
    padding-right: 12px;
    color: #A1CEE3;
  }
  .detail-value {
    width: 160px;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #ffffff;
    &:last-child {
      margin-right: 0;
    }
  }
  
  .status-online {
    color: #67c23a;
  }
  
  .status-offline {
    color: #f56c6c;
  }
  
  .action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .history-button {
    width: 50%;
  }
}

::v-deep(.el-button--primary) {
  --el-button-text-color: #FFFFFF;
  --el-button-border-color: #0772A4;
  border-color: #0772A4;
  --el-button-bg-color: #0772A4;
}

.track-history-container {
  max-height: 400px;
  min-height: 200px;
  height: 100%;
  overflow-y: auto;

  .no-data {
    text-align: center;
    color: #999;
    margin-top: 20px;
    font-size: 14px;
  }
  
  .track-list {
    .track-item {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border-radius: 4px;
      background: rgba(29, 158, 217, 0.1);
      border-left: 3px solid #1D9ED9;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:last-child {
        margin-bottom: 0;
      }

      .track-time {
        font-weight: bold;
        color: #0772A4;
      }
      
      .track-coordinates {
        color: #666;
      }
    }
  }
}

.track-controls {
  width: 240px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .hour-input {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .hour-label {
    font-size: 12px;
    margin-left: 4px;
  }
  .play-control-icon {
    width: 16px;
    height: 16px;
    color:#1D9ED9
  }
}
</style>
