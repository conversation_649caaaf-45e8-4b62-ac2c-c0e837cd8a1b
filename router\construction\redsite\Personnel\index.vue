<!--建设总览 -->
<script lang="ts" setup>
import {
  onMounted,
} from 'vue'
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import DeviceStatistics from './components/DeviceStatistics.vue';
import DeviceOnlineRateChart from './components/DeviceOnlineRateChart.vue';
import PersonnelList from './components/PersonnelList.vue';

onMounted(() => {

});

</script>

<template>
  <PageLayout>
    <template #left>
      <Card title="定位设备统计" style="height: 55%;">
        <DeviceStatistics />
      </Card>
      <Card title="近一周设备在线率" style="height: 45%;">
        <DeviceOnlineRateChart />
      </Card>
    </template>
    
    <template #right>
      <Card title="人员列表" style="height: 100%;">
        <PersonnelList />
      </Card>
    </template>
    
  </PageLayout>
</template>

<style lang="scss" scoped>


</style>
