
import request from '~/router/request'
import { AxiosPromise } from 'axios'

let SERVICE_PREFIX = import.meta.env.CVE_VUE_APP_BASE_SCS_SERVICENAME
/**
 * @description 树
 * @returns 
 */
export function treeList(params: any) {
    return request({
        url: SERVICE_PREFIX + "/device-node/tree",
        method: "get",
        params
    });
}
/**分页查询接口 */
export function getPage(params: any) {
    return request({
        url: SERVICE_PREFIX + '/device-info/page',
        method: 'get',
        params: params
    })
}

/**获取视频播放地址 */
export function getVideo(code: any, accessTypeCode: any) {
    return request({
        url: SERVICE_PREFIX + '/device-info/video/get?deviceIndexCode=' + code + '&accessTypeCode=' + accessTypeCode,
        method: 'get',
    })
}

/**获取视频播放地址 */
export function getVideoById(id: any, actionType: any) {
    return request({
        url: SERVICE_PREFIX + '/device-info/video/get/' + (id || 0) + '/' + (actionType || 0),
        method: 'get',
    })
}

export function addCollect(data) {
    return request({
        url: SERVICE_PREFIX + '/device-collect',
        method: 'post',
        data: data,
    })
}

/**删除接口 */
export function cancelCollect(data) {
    return request({
        url: SERVICE_PREFIX + '/device-collect',
        method: 'delete',
        data: data,
        params: data,
    })
}

