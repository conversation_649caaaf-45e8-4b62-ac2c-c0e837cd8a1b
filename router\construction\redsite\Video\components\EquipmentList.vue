<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
  nextTick,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { getFile, downloadImage } from '~/utils/api/file'
import dayjs from 'dayjs'
import { treeList, cameraPage } from '../api'
import { getDict } from '~/utils/app'
const props = defineProps({
  openVideoWall: {
    type: Boolean,
    default: false,
  },
})
watch(
  () => props.openVideoWall,
  (val) => {}
)
import tree_icon_leaf_0001 from '../img/tree_icon_leaf_0001.png'
import tree_icon_leaf_0002 from '../img/tree_icon_leaf_0002.png'
import tree_icon_leaf_0003 from '../img/tree_icon_leaf_0003.png'
import tree_icon_leaf_0004 from '../img/tree_icon_leaf_0004.png'
import LxMulitList from './LxMulitList.vue'
import { CircleClose } from '@element-plus/icons-vue'
const emit = defineEmits(['closeVideoWall'])
const closeVideoWall = (flag: Boolean) => {
  emit('closeVideoWall', flag)
}
const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}
const fetchData = () => {
  const obj = { portalId: Array.from(sectionMap.value.keys()).join(',') }
  treeList(obj).then((res) => {
    if (res.status) {
      let datas = []
      for (const [key, value] of sectionMap.value) {
        // 不加载信息化标
        if (value === '信息化标') {
          continue
        }
        let curdatas = res.data
          .filter((x) => x.portalId === key)
          .map((x) => {
            x.pid = key
            return x
          })
        datas.push({
          name: value,
          path: key,
          portalId: key,
          isPortal: true,
          nodeDepth: 0,
          children: curdatas.length == 0 ? null : curdatas,
        })
      }

      treeData.value = [
        {
          id: 0,
          name: '监控设备目录树',
          path: '.0.',
          isPortal: true,
          isRoot: true,
          nodeDepth: 0,
          children: datas,
        },
      ]
    }
  })
}

const clickLoading = ref(false)
const showLeafVideo = ref(false)
const accessTypeCode = ref('')
const checked = ref('')
const checkedData = ref({})
const currentNode = ref({})
const checkedNode = ref({})
// 点击树触发事件
const handleNodeClick = async (node: any, data: any) => {
  closeVideoWall(true)
  nextTick(() => {
    // 防止加载过程中多次点击
    if (clickLoading.value) {
      return
    }
    clickLoading.value = true
    accessTypeCode.value = node.accessMethod
    let params = { ...node, level: data.level }
    // 拿走给 checked用来查找收藏文件
    checkedData.value = data
    checkedNode.value = node

    currentNode.value = params
    // 判断当前是不是查看收藏
    if (checked) {
      // this.onFavorite()
    }
    const { id, level, isLeaf } = params || {}
    if (node.path) {
      showLeafVideo.value = true
    } else {
      showLeafVideo.value = false
    }
    // 区分是树节点还是请求数据生成的叶子（摄像头数据）
    data.data.children = data.data.children || []
    clickLoading.value = false
    MulitList.value.reload()
  })
}
watchEffect(() => {})
const treeData = ref([])
const MulitList = ref()
const splitNum = ref(1)
const defaultProps = {
  children: 'children',
  label: 'name',
}

// 生命周期钩子
onMounted(async () => {
  await initDict()
  fetchData()
})
onBeforeUnmount(() => {})
</script>
<template>
  <div class="MonitoringEquipment">
    <el-tree
      :data="treeData"
      node-key="id"
      :props="defaultProps"
      :expand-on-click-node="false"
      default-expand-all
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <div class="el-tree-node__label">
          <img
            src="../img/tree_icon.png"
            v-if="data.children && data.children.length > 0"
          />
          <img
            v-else-if="data.type"
            :src="
              data.type == '0001'
                ? tree_icon_leaf_0001
                : data.type == '0002'
                ? tree_icon_leaf_0002
                : data.type == '0003'
                ? tree_icon_leaf_0003
                : tree_icon_leaf_0004
            "
          />
          <img src="../img/tree_icon_leaf.png" v-else />
          <span>{{ data.name }}</span>
        </div>
      </template>
    </el-tree>
    <div class="videoWall" v-if="props.openVideoWall">
      <div class="top">
        <el-radio-group v-model="splitNum">
          <el-radio-button :label="1" :name="1">单屏</el-radio-button>
          <el-radio-button :label="6" :name="1">6屏</el-radio-button>
          <el-radio-button :label="9" :name="1">9屏</el-radio-button>
        </el-radio-group>
        <el-icon @click="closeVideoWall(false)"><CircleClose /></el-icon>
      </div>
      <LxMulitList
        ref="MulitList"
        :split="splitNum"
        :node="currentNode"
        :show-video="true"
        :action-type="'1'"
        :access-type-code="accessTypeCode"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.MonitoringEquipment {
  width: 100%;
  height: 100%;
  overflow: auto;
  :deep(.el-tree) {
    height: 100%;
    overflow: auto;
    background-color: transparent;
    .el-tree-node.is-current > .el-tree-node__content {
      background-color: rgba(0, 123, 255, 0.1) !important;
    }
    .el-tree-node__content {
      background-color: transparent;
      height: 40px !important;
      &:hover {
        background-color: rgba(0, 123, 255, 0.1) !important;
      }
    }
    .el-tree-node__expand-icon {
      color: #fff;
      position: relative;
    }
    .el-tree-node__label {
      color: #fff;
      font-size: 16px;
      display: flex;
      align-items: center;
      span {
        flex: 1;
      }
      img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
  }
  .videoWall {
    background: rgba(0, 49, 72, 0.81);
    box-shadow: inset 0px 4px 12px 0px rgba(27, 165, 186, 0.63),
      0px 2px 6px 0px rgba(44, 134, 183, 0.25);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid;
    border-image: linear-gradient(
        360deg,
        rgba(160, 229, 255, 0.76),
        rgba(120, 215, 250, 0.81)
      )
      1 1;
    position: fixed;
    top: 20%;
    bottom: 10%;
    left: 100px;
    right: 500px;
    display: flex;
    flex-direction: column;
    padding: 32px 16px;
    > .top {
      display: flex;
      gap: 16px;
      align-items: center;
      justify-content: flex-end;
      text-align: right;
      margin-bottom: 16px;
      .el-icon {
        font-size: 32px;
        cursor: pointer;
      }
    }
    .lx-mulit-list {
      flex: 1;
    }
  }
}
</style>
