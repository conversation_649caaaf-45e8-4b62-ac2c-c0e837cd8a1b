<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import dayjs from 'dayjs'

// 图表容器
const chartContainer = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance: ECharts | null = null
const fetchData = () => {
  initChart()
}
const initChart = () => {
  let xAxisData = []
  for (let i = 6; i >= 0; i--) {
    xAxisData.push(dayjs().subtract(i, 'day').format('YYYY/MM/DD'))
  }
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '0%',
      top: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'value',
      nameTextStyle: {
        color: '#E8E8E8',
        fontSize: 12,
        align: 'center',
      },
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
          type: 'dashed',
        },
      },
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
        },
      },
    },
    series: [
      {
        type: 'line',
        data: [18, 23, 90, 10, 31, 63, 50],
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          },
        },
        itemStyle: {
          color: '#6BCAF6',
        },
      },
    ],
  }
  if (chartContainer.value) {
    echarts.getInstanceByDom(chartContainer.value)?.dispose()
    chartInstance = echarts.init(chartContainer.value) // 初始化实例
    chartInstance.setOption(option) // 设置配置项
  }
}

watchEffect(() => {})

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 生命周期钩子
onMounted(() => {
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="EquipmentOnlineWeek">
    <div ref="chartContainer" class="chartContainer"></div>
  </div>
</template>
<style lang="scss" scoped>
.EquipmentOnlineWeek {
  width: 100%;
  height: 100%;
  .chartContainer {
    height: 100%;
    width: 100%;
  }
}
</style>
