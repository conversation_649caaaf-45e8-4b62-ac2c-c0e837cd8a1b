<template>
  <div v-loading="loading" class="lx-mulit-list">
    <div class="lx-mulit-videos">
      <template v-if="list && list.length">
        <div
          ref="videoListRef"
          class="video-grid"
          :class="
            pageSize == 1
              ? 'grid-one'
              : pageSize == 6
              ? 'grid-six'
              : 'grid-nine'
          "
        >
          <div v-for="(item, index) in list" :key="index" class="video-item">
            <div class="lx-grid-content">
              <ys7Player
                ref="ys7PlayerRef"
                :id="item.id"
                :node="item"
                :action-type="actionType"
                :access-type-code="accessTypeCode"
              />
              <div v-if="item.status == 0" class="lx-device-all-info">
                <img
                  :width="(60 / 2) * splitSpan"
                  src="../img/offLine_bg.png"
                  alt
                  class="lx-offline-bg"
                />
                <el-row class="lx-info-view">
                  <el-col :span="24">
                    设备名称：{{ item.name }}{{ item.id }}
                  </el-col>
                  <el-col :span="12">
                    离线时间：{{ item.offlineTime || '' }}
                  </el-col>
                  <el-col :span="12">
                    设备类型：{{ deviceTypeVideoMap.get(item.type) }}
                  </el-col>
                  <el-col :span="12">
                    离线原因：{{ deviceOffilneCauseMap.get(item.offlineCause) }}
                  </el-col>
                  <el-col :span="12"> 设备编号：{{ item.code || '' }} </el-col>
                  <el-col :span="12">
                    维护人员：{{ item.repairOfficerFullname || '' }}
                  </el-col>
                  <el-col :span="12">
                    维护电话：{{ item.repairOfficerPhone || '' }}
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <div style="padding-top: 100px">
          <el-empty
            v-if="!loading"
            :description="!node.id ? '请选择节点' : '暂无数据'"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
  computed,
  nextTick,
} from 'vue'
import * as Api from '../api'
import ys7Player from './Ys7Player.vue'
import { getDict } from '~/utils/app'
const props = defineProps({
  split: {
    type: [Number, String],
    default: 1,
  },
  node: {
    type: Object,
    default: () => ({}),
  },
  showVideo: {
    type: Boolean,
    default: true,
  },
  // 设备详情类型
  videoMsgType: {
    type: String,
    default: '1',
  },
  // 播放类型
  actionType: {
    type: String,
    default: '1',
    validator(val) {
      return ['1', '2', '3', '4'].indexOf(val) !== -1
    },
  },
  accessTypeCode: {
    type: String,
    default: '0',
  },
})
const deviceTypeVideoMap = ref<Map<string, string>>(new Map())
const deviceOffilneCauseMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'device_type_video' })
  if (dictRes.data && dictRes.data.device_type_video) {
    dictRes.data.device_type_video.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          deviceTypeVideoMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
  const dictRes1 = await getDict({ code: 'device_offilne_cause' })
  if (dictRes1.data && dictRes1.data.device_offilne_cause) {
    dictRes1.data.device_offilne_cause.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          deviceOffilneCauseMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}
onMounted(() => {
  initDict()
})

const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const oldlist = ref([])
const list = ref([])
const ys7PlayerRef = ref()
const splitSpan = ref()
const playerStyle = ref()

const pageSize = computed(() => {
  return Number(props.split)
})
const totalPages = computed(() => {
  return pageSize.value > 0 ? Math.ceil(total.value / pageSize.value) : 0
})
const portalId = computed(() => {
  // return this.$store.state.portal.id || "";
})

watch(
  () => props.actionType,
  () => {
    getData()
  }
)
watch(
  () => pageSize.value,
  () => {
    getData()
  }
)
watch(
  () => props.node.id,
  (v) => {
    if (v || v === 0) {
      reload()
    }
  }
)
watch(
  () => props.node.checked,
  (v) => {
    if (v !== undefined) {
      reload()
    }
  }
)

watch(
  () => props.split,
  (val, oldVal) => {
    if (val !== oldVal) {
      // 在切换布局前停止播放器，避免内存泄漏
      let refs = ys7PlayerRef.value
      if (refs && refs.length) {
        refs.forEach((player) => {
          if (player && player.stop) {
            try {
              player.stop()
            } catch (e) {
              // 忽略错误，确保程序继续执行
            }
          }
        })
      }

      // 重设当前页码
      currentPage.value = 1
      oldlist.value = []
      getData()
      // 延迟处理播放器初始化
      nextTick(() => {
        if (ys7PlayerRef.value && ys7PlayerRef.value.length) {
          ys7PlayerRef.value.forEach((player) => {
            if (player && typeof player.reinitPlayer === 'function') {
              player.reinitPlayer()
            }
          })
        }
      })
    }
  }
)

// ---
// 设备名称格式化
const getDeviceName = (item) => {
  // return item.name + "(" + getLabel("device_type_video", item.type) + ")";
}

// 重新加载数据
const reload = () => {
  currentPage.value = 1
  // 清理播放器实例，避免内存泄漏
  cleanupPlayers()
  // 节点切换，需要清空数据
  getData(true)
}
defineExpose({
  reload,
})
// 分页变化处理
const handleCurrentChange = (v) => {
  currentPage.value = v
  // 先清理旧的播放器，避免内存泄漏和高度累积问题
  cleanupPlayers()
  // 分页切换，不清空数据
  getData(false)
}

// 清理播放器实例
const cleanupPlayers = () => {
  let refs = ys7PlayerRef.value
  if (refs && refs.length) {
    refs.forEach((player) => {
      if (player && player.destroy) {
        try {
          player.destroy()
        } catch (e) {
          // 忽略stop操作的错误
        }
      }
    })
  }
}

// 重新调整播放器尺寸
const resizePlayers = () => {
  nextTick(() => {
    let refs = ys7PlayerRef.value
    console.log('🚀 ~ nextTick ~ refs:', refs)
    if (refs && refs.length && list.value.length) {
      // 在Grid布局下不需要手动计算宽高，让播放器自适应容器
      refs.forEach((player) => {
        if (player && player.resize) {
          try {
            // 让播放器自适应父容器大小
            const container = player.$el.parentElement
            if (container) {
              player.resize(container.clientWidth, container.clientHeight)
            }
          } catch (e) {
            // 静默处理播放器调整大小错误
          }
        }
      })
    }
  })
}

// 获取数据
const getData = async (clearData = true) => {
  // 保存旧数据用于播放器实例管理
  oldlist.value = JSON.parse(JSON.stringify(list.value || []))

  // 只有在节点切换时才清空数据，分页切换时不清空
  if (clearData) {
    list.value = []
    total.value = 0
  }

  try {
    loading.value = true
    // 从节点中获取必要信息
    const { id, isLeaf, checked, path, isPortal, portalId, nodeType } =
      props.node || {}
    let params = {
      page: currentPage.value,
      size: pageSize.value,
      portalId: portalId === 0 ? '' : portalId,
      showVideo: 1,
      actionType: props.actionType,
      isFilterHidden: 1,
    }
    const params2 = {
      page: currentPage.value,
      size: pageSize.value,
      portalId: portalId === 0 ? '' : portalId,
      showVideo: 1,
      collectStatus: 1,
      actionType: props.actionType,
      isFilterHidden: 1,
    }
    if (nodeType === 'device') {
      params.size = 9999
      params.id = props.node.id
    } else {
      if (!isPortal) {
        params.nodeId = id
      }
    }
    // 获取视频设备数据
    const res = await Api.getPage(checked ? params2 : params)
    if (res.status) {
      let data = res.data?.records || []
      if (!path) {
        list.value = data.filter((v) => v.id == props.node.id)
        total.value = list.value.length || 0
      } else {
        list.value = data
        total.value = res.data?.total || 0
      }
    } else {
      list.value = []
      total.value = 0
    }

    // 调整分屏相关值
    const splitValue = Number(props.split)
    splitSpan.value = splitValue === 1 ? 24 : 8
    playerStyle.value =
      splitValue === 1 ? { height: '100%' } : { height: 'calc(100% / 2)' }

    // 数据加载后调整播放器尺寸
    nextTick(() => {
      resizePlayers()
    })
  } catch (e) {
    // 安静处理API错误
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 收藏功能
const onCollection = async (item) => {
  try {
    const deviceId = item.id
    const collectStatus = item.collectStatus
    if (collectStatus == '0') {
      await Api.cancelCollect({ deviceId })
      this.$message.info('已取消收藏!')
    } else {
      await Api.addCollect({ deviceId })
      this.$message.success('收藏成功!')
    }
    this.getData()
  } catch (error) {
    this.$message.error('收藏操作失败')
  }
}

// 回放/预览功能
const onChangeMode = (item, mode) => {
  item.mode = mode
}
</script>

<style scoped lang="scss">
.mulit-list {
  padding: 0 10px;

  .player-row {
    height: calc(100vh - 326px);
  }

  .grid-content {
    width: 100%;
    height: calc(100% - 80px);
    //aspect-ratio: 16/9;
    padding-bottom: 5px;
    position: relative;
    color: #333;
  }

  .device-info {
    z-index: 1;
    width: 100%;
    // height: calc(100% - 30px);
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-content: center;
    justify-content: center;
    // background: url('../../../../../assets/img/userManagement/offLine_bg.png') no-repeat;
    // background-size:contain;
    background-color: #f3f6fe;
  }

  .info-view {
    position: absolute;
    left: 10%;
    bottom: 15%;
  }

  .offline-bg {
    position: absolute;
    width: 50%;
    height: 30%;
  }

  // 监控操作按钮
  .video-operation {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .video-btn {
      cursor: pointer;
      margin-left: 6px;
    }
  }

  .video-bottom,
  .collecting-view {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .video-bottom-item {
    max-width: 80%;
  }

  .video-bottom-item div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(0, 0, 0, 0.85);

    .title {
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .collecting-view {
    cursor: pointer;

    i {
      margin-right: 8px;
      margin-bottom: 3px;
    }

    span {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .pagination {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

.lx-mulit-list {
  display: grid;
  grid-template-rows: 1fr auto;
  height: 100%;
  width: 100%;
  overflow: hidden; /* 防止出现滚动条 */

  .lx-mulit-videos {
    overflow: hidden;
    width: 100%;

    /* Grid布局系统 */
    .video-grid {
      display: grid;
      width: 100%;
      height: 100%;
      gap: 15px;

      /* 单屏布局 */
      &.grid-one {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
        height: 100%;

        .video-item {
          width: 100%;
          height: 100%; /* 使用100%高度自适应父容器 */
        }
      }

      /* 多屏布局 */
      &.grid-six {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        grid-auto-flow: row;
        height: 100%;
      }
      /* 多屏布局 */
      &.grid-nine {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
        grid-auto-flow: row;
        height: 100%;
      }

      /* 视频项目基础样式 */
      .video-item {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border-radius: 8px;
      }

      ::v-deep {
        .el-col {
          display: flex;
          flex-flow: column;
        }
      }

      .lx-grid-content {
        position: relative;
        flex: 1 1 auto;
        background-color: #444040;

        ::v-deep {
          .video-js {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            overflow: hidden;
          }
        }

        .lx-device-all-info {
          position: absolute;
          display: flex;
          justify-content: center;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background-color: #444040;
          z-index: 9;

          .lx-offline-bg {
            position: absolute;
            margin-top: 10%;
            //width: 60px;
          }

          .lx-info-view {
            position: absolute;
            left: 10%;
            bottom: 15%;
          }
        }
      }

      .lx-device-info {
        height: 50px;
        padding: 15px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        border: 1px solid #e7ecdc;
        border-top: none;
        z-index: 1;

        .lx-info {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1 1 auto;
        }

        .lx-collect {
          width: 80px;
          min-width: 80px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }

  .lx-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }
}
</style>
