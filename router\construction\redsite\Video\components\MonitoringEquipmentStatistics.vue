<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { getDict } from '~/utils/app'
import dayjs from 'dayjs'
const props = defineProps({})
// 图表容器
const chartContainer1 = ref<HTMLDivElement | null>(null)
const chartContainer2 = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance1: ECharts | null = null
let chartInstance2: ECharts | null = null

const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}
const fetchData = () => {
  initChart()
}
const initChart = () => {
  const option1 = {
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        center: ['50%', '80%'],
        radius: '150%',
        endAngle: 0,
        min: 0,
        max: 652,
        width: '100%',
        splitNumber: 1,
        left: 0,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: '#1696C5' },
            { offset: 0.5, color: '#76CCE4' },
            { offset: 1, color: '#33B4CE' },
          ]),
          shadowColor: 'rgba(50, 164, 240, 0.25)',
          shadowBlur: 10,
          shadowOffsetX: 2,
          shadowOffsetY: 2,
        },
        progress: {
          show: true,
          roundCap: true,
          width: 12,
        },
        pointer: {
          show: false,
        },
        axisLine: {
          roundCap: true,
          lineStyle: {
            width: 12,
            color: [[1, 'rgba(255, 255, 255, 0.6)']],
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: 22,
        },
        data: [
          {
            value: 492,
            name: '在线监测',
          },
        ],
        detail: {
          fontSize: 30,
          offsetCenter: [0, '-35%'],
          valueAnimation: true,
          color: '#fff',
        },
        title: {
          offsetCenter: [0, '-10%'],
          color: '#fff',
          fontSize: 16,
        },
      },
    ],
  }
  if (chartContainer1.value) {
    echarts.getInstanceByDom(chartContainer1.value)?.dispose()
    chartInstance1 = echarts.init(chartContainer1.value) // 初始化实例
    chartInstance1.setOption(option1) // 设置配置项
  }
  const option2 = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      top: 'top',
      left: 'center',
      // itemWidth: 14,
      // itemHeight: 14,
      itemStyle: {
        color: 'inherit',
      },
      textStyle: {
        color: '#fff',
        fontSize: 16,
      },
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '0%',
      containLabel: true,
    },
    yAxis: [
      {
        type: 'value',
        name: '单位(个)',
        min: 0,
        max: 400,
        interval: 100,
        nameTextStyle: {
          color: '#E8E8E8',
          fontSize: 12,
          align: 'center',
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(221, 221, 221, 0.44)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        axisLabel: {
          formatter: '{value} %',
          color: '#fff',
          fontSize: 14,
        },
        axisTick: {
          show: false,
        },

        splitLine: {
          lineStyle: {
            color: 'rgba(221, 221, 221, 0.44)',
            type: 'dashed',
          },
        },
      },
    ],
    xAxis: {
      type: 'category',
      data: Array.from(sectionMap.value.values()),
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
        },
      },
    },
    series: [
      {
        name: '在线',
        type: 'bar',
        data: [182, 234, 290, 104, 131, 230],
        stack: 'all',
        barWidth: 12,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(3, 171, 200, 0.29)' },
            { offset: 1, color: 'rgba(50, 209, 237, 1)' },
          ]),
        },
      },
      {
        name: '离线',
        type: 'bar',
        stack: 'all',
        barWidth: 12,
        data: [182, 234, 290, 104, 131, 130],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(254, 165, 64, 0.40)' },
            { offset: 1, color: 'rgba(229, 161, 82, 1)' },
          ]),
        },
      },
      {
        name: '在线率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          },
        },
        data: [18, 23, 90, 10, 31, 63],
        itemStyle: {
          color: '#4EF17C',
        },
      },
    ],
  }
  if (chartContainer2.value) {
    echarts.getInstanceByDom(chartContainer2.value)?.dispose()
    chartInstance2 = echarts.init(chartContainer2.value) // 初始化实例
    chartInstance2.setOption(option2) // 设置配置项
  }
} // 销毁图表
const destroyChart = () => {
  if (chartInstance1) {
    chartInstance1.dispose()
    chartInstance1 = null
  }
  if (chartInstance2) {
    chartInstance2.dispose()
    chartInstance2 = null
  }
}
watchEffect(() => {})
// 生命周期钩子
onMounted(async () => {
  await initDict()
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="MonitoringEquipmentStatistics">
    <div
      ref="chartContainer1"
      class="chartContainer1"
      style="height: 40%"
    ></div>
    <div
      ref="chartContainer2"
      class="chartContainer2"
      style="height: 60%"
    ></div>
  </div>
</template>
<style lang="scss" scoped>
.MonitoringEquipmentStatistics {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
