<template>
  <div class="ys7-player-container" :ref="refKey">
    <div
      :id="compid"
      class="video-container"
    />
    <div v-if="false">
      <button @click="init">
        init
      </button>
      <button @click="stop">
        stop
      </button>
      <button @click="play">
        play
      </button>
      <button @click="openSound">
        openSound
      </button>
      <button @click="closeSound">
        closeSound
      </button>
      <button @click="startSave">
        startSave
      </button>
      <button @click="stopSave">
        stopSave
      </button>
      <button @click="capturePicture">
        capturePicture
      </button>
      <button @click="fullScreen">
        fullScreen
      </button>
      <button @click="getOSDTime">
        getOSDTime
      </button>
      <button @click="ezopenStartTalk">
        startTalk
      </button>
      <button @click="ezopenStopTalk">
        stopTalk
      </button>
      <button @click="destroy">
        destroy
      </button>
    </div>
  </div>
</template>

<script>
import EZUIKit from 'ezuikit-js'
import * as Api from '../api'

let ActionType = {
  Live: '1',
  LocalRec: '2',
  CloudRec: '3',
  Control: '4'
}

export default {
  name: 'Ys7Player',
  props: {
    id: {
      type: String,
      default: ''
    },
    cameraIndexCode: {
      type: String,
      default: ''
    },
    accessTypeCode: {
      type: String,
      default: ''
    },
    node: {
      type: Object,
      default: () => ({})
    },
    actionType: {
      type: String,
      default: ActionType.Live,
      validator(val) {
        return [ ActionType.Live, ActionType.LocalRec, ActionType.CloudRec, ActionType.Control ].indexOf(val) !== -1
      }
    }
  },
  data() {
    let dateTime = (new Date().getTime()).toString()
    return {
      player: null, // 改为实例属性，避免全局共享
      compid: dateTime,
      refKey: 'palyer_' + dateTime,
      width: 600,
      height: 400,
    }
  },
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            const dom = this.$refs[this.refKey]
            this.width = dom.clientWidth
            this.height = dom.clientHeight

            if (this.node.accessToken && this.node.url) {
              let token = this.node.accessToken
              let url = this.node.url
              this.init(token, url)
            } else {
              Api.getVideoById(val, this.actionType).then(res => {
                if (res.status) {
                  let token = res.data.token
                  let url = res.data.url
                  console.log('getVideo', val, JSON.stringify(res.data))
                  this.init(token, url)
                } else {
                  this.destroy()
                }
              })
            }
          })
        } else {
          this.destroy()
        }
      },
      immediate: true
    },
    cameraIndexCode: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            const dom = this.$refs[this.refKey]
            this.width = dom.clientWidth
            this.height = dom.clientHeight

            if (this.node.accessToken && this.node.url) {
              let token = this.node.accessToken
              let url = this.node.url
              this.init(token, url)
            } else {
              Api.getVideo(val, this.accessTypeCode).then(res => {
                if (res.status) {
                  let token = res.data.token
                  let url = res.data.url
                  console.log('getVideo', val, JSON.stringify(res.data))
                  this.init(token, url)
                } else {
                  this.destroy()
                }
              })
            }
          })
        } else {
          this.destroy()
        }
      },
      immediate: true
    },
    actionType: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            const dom = this.$refs[this.refKey]
            this.width = dom.clientWidth
            this.height = dom.clientHeight

            if (this.node.accessToken && this.node.url) {
              let token = this.node.accessToken
              let url = this.node.url
              this.init(token, url)
            } else {
              Api.getVideoById(this.id, val).then(res => {
                if (res.status) {
                  let token = res.data.token
                  let url = res.data.url
                  this.init(token, url)
                } else {
                  this.destroy()
                }
              })
            }
          })
        } else {
          this.destroy()
        }
      },
      immediate: true
    }
  },
  mounted: () => {
    console.group('mounted 组件挂载完毕状态===============》')
  },
  methods: {
    init(toekn, url) {
      if (this.player) {
        this.destroy()
      }
      if ( !url) {
        return
      }

      console.log('init', this.cameraIndexCode, this.height)
      let options = {
        id: this.compid, // 视频容器ID
        accessToken: toekn,
        url: url,
        // // simple: 极简版; pcLive: pc直播; pcRec: pc回放; mobileLive: 移动端直播; mobileRec: 移动端回放;security: 安防版; voice: 语音版;
        // template: template,
        // themeData: themeData,
        // plugin: ["talk"], // 加载插件，talk-对讲
        width: this.width,
        height: this.height,
        audio: false,
        // autoplay: false,
        handleError: (error) => {
          console.error('handleError', error)
        },
        // language: "en", // zh | en
        // staticPath: "/ezuikit_static", // 如果想使用本地静态资源，请复制根目录下ezuikit_static 到当前目录下， 然后设置该值
        env: {
          // https://open.ys7.com/help/1772?h=domain
          // domain默认是 https://open.ys7.com, 如果是私有化部署或海外的环境，请配置对应的domain
          // The default domain is https://open.ys7.com If it is a private deployment or overseas (outside of China) environment, please configure the corresponding domain
          // domain: 'https://open.ys7.com',
        },
        // staticPath:"https://openstatic.ys7.com/ezuikit_js/v8.1.9/ezuikit_static",
        // 日志打印设置
        loggerOptions: {
          // player.setLoggerOptions(options)
          level: 'ERROR', // INFO LOG  WARN  ERROR
          name: 'ezuikit',
          showTime: true,
        },
        // 视频流的信息回调类型
        /**
         * 打开流信息回调，监听 streamInfoCB 事件
         * 0 : 每次都回调
         * 1 : 只回调一次
         * 注意：会影响性能
         * 默认值 1
         */
        streamInfoCBType: 1,
        // 取流成功并出现第一帧画面时触发
        handleFirstFrameDisplay: () => {
          console.log('handleFirstFrameDisplay', arguments)
          // 关闭声音
          this.closeSound()
        },
      }

      let themeData = {
        'autoFocus': 3,
        'header': {
          'color': '#FFFFFF',
          'activeColor': '#FFFFFF',
          'backgroundColor': '#000000',
          'btnList': [
            {
              'iconId': 'deviceID',
              'part': 'left',
              'defaultActive': 0,
              'memo': '顶部设备序列号',
              'isrender': 1
            },
            {
              'iconId': 'deviceName',
              'part': 'left',
              'defaultActive': 0,
              'memo': '顶部设备名称',
              'isrender': 1
            },
            {
              'iconId': 'cloudRec',
              'part': 'right',
              'defaultActive': 0,
              'memo': '云存储',
              'isrender': 0
            },
            {
              'iconId': 'rec',
              'part': 'right',
              'defaultActive': 0,
              'memo': 'SD卡回放',
              'isrender': 0
            }
          ]
        },
        'footer': {
          'color': '#FFFFFF',
          'activeColor': '#1890FF',
          'backgroundColor': '#00000021',
          'btnList': [
            {
              'iconId': 'play',
              'part': 'left',
              'defaultActive': 1,
              'memo': '播放',
              'isrender': 1
            },
            {
              'iconId': 'capturePicture',
              'part': 'left',
              'defaultActive': 0,
              'memo': '截屏按钮',
              'isrender': 1
            },
            {
              'iconId': 'sound',
              'part': 'left',
              'defaultActive': 0,
              'memo': '声音按钮',
              'isrender': 1
            },
            {
              'iconId': 'pantile',
              'part': 'left',
              'defaultActive': 0,
              'memo': '云台控制按钮',
              'isrender': 0
            },
            {
              'iconId': 'recordvideo',
              'part': 'left',
              'defaultActive': 0,
              'memo': '录制按钮',
              'isrender': 1
            },
            {
              'iconId': 'talk',
              'part': 'left',
              'defaultActive': 0,
              'memo': '对讲按钮',
              'isrender': 1
            },
            {
              'iconId': 'zoom',
              'part': 'left',
              'defaultActive': 0,
              'memo': '电子放大',
              'isrender': 1
            },
            {
              'iconId': 'hd',
              'part': 'right',
              'defaultActive': 0,
              'memo': '清晰度切换按钮',
              'isrender': 1
            },
            {
              'iconId': 'webExpend',
              'part': 'right',
              'defaultActive': 0,
              'memo': '网页全屏按钮',
              'isrender': 1
            },
            {
              'iconId': 'expend',
              'part': 'right',
              'defaultActive': 0,
              'memo': '全局全屏按钮',
              'isrender': 1
            }
          ]
        }
      }

      // 类型
      if (this.actionType === ActionType.LocalRec || this.actionType === ActionType.CloudRec) {
        // pc回放
        options.template = 'pcRec'
        delete options.themeData
      } else if (this.actionType === ActionType.Control) {
        // 云台控制
        options.template = 'pcLive'
        delete options.themeData
      } else {
        //直播
        options.themeData = themeData
        delete options.template
      }
      this.player = new EZUIKit.EZUIKitPlayer(options)

      this.player.eventEmitter.on(EZUIKit.EZUIKitPlayer.EVENTS.videoInfo, (info) => {
        console.log('videoinfo', info)
      })

      this.player.eventEmitter.on(EZUIKit.EZUIKitPlayer.EVENTS.audioInfo, (info) => {
        console.log('audioInfo', info)
      })

      // 首帧渲染成功
      // first frame display
      this.player.eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.firstFrameDisplay,
        () => {
          console.log('firstFrameDisplay ')
        }
      )
      this.player.eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.streamInfoCB,
        (info) => {
          console.log('streamInfoCB ', info)
        }
      )
    },
    play() {
      if ( !this.player) return
      let playPromise = this.player.play()
      playPromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    resize(w, h) {
      if ( !this.player) return
      if ( !w || !h) {
        const dom = this.$refs[this.refKey]
        w = dom.clientWidth
        h = dom.clientHeight
      }
      this.player.reSize(w, h)
    },
    stop() {
      if ( !this.player) return
      let stopPromise = this.player.stop()
      stopPromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    getOSDTime() {
      if ( !this.player) return
      let getOSDTimePromise = this.player.getOSDTime()
      getOSDTimePromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    capturePicture() {
      if ( !this.player) return
      let capturePicturePromise = this.player.capturePicture(
        `${ new Date().getTime() }`
      )
      capturePicturePromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    openSound() {
      if ( !this.player) return
      let openSoundPromise = this.player.openSound()
      openSoundPromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    closeSound() {
      if ( !this.player) return
      let openSoundPromise = this.player.closeSound()
      openSoundPromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    startSave() {
      if ( !this.player) return
      let startSavePromise = this.player.startSave(`${ new Date().getTime() }`)
      startSavePromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    stopSave() {
      if ( !this.player) return
      let stopSavePromise = this.player.stopSave()
      stopSavePromise.then((data) => {
        console.log('promise 获取 数据', data)
      })
    },
    ezopenStartTalk() {
      if ( !this.player) return
      this.player.startTalk()
    },
    ezopenStopTalk() {
      if ( !this.player) return
      this.player.stopTalk()
    },
    fullScreen() {
      if ( !this.player) return
      this.player.fullScreen()
    },
    destroy() {
      if (this.player) {
        let destroyPromise = this.player.destroy()
        destroyPromise.then((data) => {
          console.log('promise 获取 数据', data)
        })
        this.player = null
      }
    },

    // 新增方法，用于在切换布局时重新初始化播放器
    reinitPlayer() {
      if (this.player) {
        this.destroy()

        // 重新获取容器尺寸
        this.$nextTick(() => {
          const dom = this.$refs[this.refKey]
          if (dom) {
            this.width = dom.clientWidth
            this.height = dom.clientHeight

            // 重新初始化播放器
            if (this.node.accessToken && this.node.url) {
              this.init(this.node.accessToken, this.node.url)
            } else if (this.cameraIndexCode) {
              Api.getVideoById(this.id, this.actionType).then(res => {
                if (res.status) {
                  let token = res.data.token
                  let url = res.data.url
                  this.init(token, url)
                } else {
                  this.destroy()
                }
              })
            }
          }
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.ys7-player-container {
  width: 100%;
  height: 100%;

  .video-container {
    width: 100%;
    height: 100%;

    .player-down-plugin {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .player-box {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
