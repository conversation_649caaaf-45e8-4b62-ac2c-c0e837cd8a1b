import request from '~/router/request'

/**
 * 资料文件
 */
export function queryPartyMassesDocuments() {
  return request({
    url: `/party-building/partyMassesDocuments/toLED`,
    method: 'get',
  })
}

/** 获取文件详情 */
export function getFileInfo(token: string) {
  return request({
    url: '/sys-storage/file',
    method: 'post',
    data: {
      f8s: [token],
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    transformRequest: function(data) {
      return JSON.stringify(data)
    },
  })
}

// 通知动态
export function queryNotices() {
  return request({
    url: `/party-building/notice/front/list`,
    method: 'post',
    data: {
      createDateBegin: '',
      createDateEnd: '',
      currentPage: 0,
      name: '',
      pageSize: 99,
      typeId: null,
    },
  })
}

// 榜样力量
export function queryExample() {
  return request({
    url: `party-building/awardManagement/largeScreenDisplay`,
    method: 'get',
  })
}


// 组织架构
export function largeScreenDisplay(params: any) {
  return request({
    url: `party-building/organize/largeScreenDisplay`,
    method: 'get',
    params
  })
}

// 清廉工地附件
export function queryIncorruptibleFile(name: String) {
  return request({
    url: `party-building/organize/ILowCorruptionSiteDashboard/file`,
    method: 'get',
    params: {
      name
    }
  })
}