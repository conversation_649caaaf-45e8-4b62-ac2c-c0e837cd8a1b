import request from '~/router/request'
import { AxiosPromise } from 'axios'
let SERVICE_PREFIX = import.meta.env.CVE_VUE_APP_BASE_API_SERVICENAME

/**
 * 文件数量统计
 * @param
 * @returns
 */
export function fileStatistics(params: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/file/bigscreen/statistics`,
    method: 'get',
    params,
  })
}

/**
 * 新增文件数量统计
 * @param
 * @returns
 */
export function fileAddStatistics(params: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/file/bigscreen/add/statistics`,
    method: 'get',
    params,
  })
}

/**
 * 监理文件 / 施工文件
 * @param
 * @returns
 */
export function technologyFiles(params: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/technologyfiles`,
    method: 'get',
    params,
  })
}

/**
 * 设计文件
 * @param
 * @returns
 */
export function designFiles(params: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/design-approval/page`,
    method: 'get',
    params,
  })
}

/**
 * 业务内部审批
 * @param params
 * @returns
 */
export function ownerInternalFiles(params: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/file-owner-internal/page`,
    method: 'get',
    params,
  });
}

/**
 * 业务对外发文
 * @param params
 * @returns
 */
export function ownerPublicFiles(params: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/file-owner-public/page`,
    method: 'get',
    params,
  });
}

/**
 * 业主历史文件
 * @param params
 * @returns
 */
export function ownerHistoryFiles(params: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/file-history/page`,
    method: 'get',
    params,
  });
}