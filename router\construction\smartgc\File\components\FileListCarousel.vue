<template>
  <div class="file-list-carousel-container" v-loading="loading">
    <Carousel v-if="hasFiles" :page-items="nonEmptyFilePages" :autoplay="true" :interval="5000" :show-controls="true">
      <template #default="{ item: pageData }">
        <div class="file-list-page">
          <h4 class="page-category-title">{{ pageData.category }}</h4>
          <ul v-if="pageData.files.length > 0">
            <li v-for="(file, index) in pageData.files" :key="file.id" class="file-item" @click="handleFileClick(file)">
              <span class="file-index">{{ index + 1 }}.</span>
              <el-tooltip
                :content="file.name"
                placement="top"
                effect="dark"
              >
                <span class="file-name">{{ file.name }}</span>
              </el-tooltip>
              <span class="file-date">{{ file.date }}</span>
            </li>
          </ul>
          <div v-else class="empty-page-state">
            暂无数据
          </div>
        </div>
      </template>
    </Carousel>
    <div v-else-if="!hasFiles && !loading" class="empty-state">
      暂无数据
    </div>
    <PreviewDialog ref="previewDialogRef" title="文件预览" :show-fullscreen="true" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, onMounted, computed } from 'vue';
import Carousel from '~/router/components/Carousel/index.vue';
import PreviewDialog from '~/router/components/PreviewDialog/index.vue';
import { technologyFiles, designFiles, ownerInternalFiles, ownerPublicFiles, ownerHistoryFiles } from '../api';
import { getFiles } from '~/utils/api/file'
import dayjs from 'dayjs';

interface FileItem {
  id: string;
  name: string;
  date: string;
}

interface FilePage {
  category: string;
  files: FileItem[];
}

const state = reactive({
  loading: true,
  internalFiles: [] as FileItem[],
  publicFiles: [] as FileItem[],
  historyFiles: [] as FileItem[],
  supervisionFiles: [] as FileItem[],
  constructionFiles: [] as FileItem[],
  designFilesList: [] as FileItem[],
});

const filePages = computed<FilePage[]>(() => [
  { category: '业务内部审批', files: state.internalFiles },
  { category: '业务对外发文', files: state.publicFiles },
  { category: '业主历史文件', files: state.historyFiles },
  { category: '监理文件', files: state.supervisionFiles },
  { category: '施工文件', files: state.constructionFiles },
  { category: '设计文件', files: state.designFilesList },
]);

// 预览弹窗引用
const previewDialogRef = ref(null);

const hasFiles = computed(() => !state.loading && filePages.value.some(p => p.files.length > 0));
const nonEmptyFilePages = computed(() => filePages.value.filter(p => p.files.length > 0));

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '--';
  return dayjs(dateString).format('YYYY-MM-DD');
};

const fetchFiles = async () => {
  state.loading = true;
  try {
    const [internalRes, publicRes, historyRes, supervisionRes, constructionRes, designRes] = await Promise.all([
      // processState=1: 已完成
      ownerInternalFiles({ processState: 1, page: 1, size: 10 }),
      ownerPublicFiles({ processState: 1, page: 1, size: 10 }),
      ownerHistoryFiles({ page: 1, size: 10 }),
      // processState=5: 已完成 + 历史文件
      technologyFiles({ modelType: '监理文件', menuCode: 'jl', processState: 5, page: 1, size: 10 }),
      technologyFiles({ modelType: '施工文件', menuCode: 'cb', processState: 5, page: 1, size: 10 }),
      designFiles({ processState: 5, page: 1, size: 10 }),
    ]);

    state.internalFiles = (internalRes.data?.records || []).map((item: any) => ({
      id: item.id,
      name: item.fileName || '--',
      date: formatDate(item.startTime || item.updateDate || item.createTime),
      fileG9s: item.stampFileUpload || item.fileMain || item.otherAttachment,
    }));

    state.publicFiles = (publicRes.data?.records || []).map((item: any) => ({
      id: item.id,
      name: item.fileName || '--',
      date: formatDate(item.startTime || item.updateDate || item.createTime),
      fileG9s: item.stampFileUpload || item.fileMain || item.otherAttachment,
    }));

    state.historyFiles = (historyRes.data?.records || []).map((item: any) => ({
      id: item.id,
      name: item.fileName || '--',
      date: formatDate(item.startTime || item.updateDate || item.createTime),
      fileG9s: item.stampFileUpload || item.fileMain || item.otherAttachment,
    }));

    state.supervisionFiles = (supervisionRes.data?.list || []).map((item: any) => ({
      id: item.id,
      name: item.sendingName || '--',
      date: formatDate(item.startTime || item.updateDate || item.createTime),
      fileG9s: item.stampFileUpload || item.fileMain || item.otherAttachment,
    }));

    state.constructionFiles = (constructionRes.data?.list || []).map((item: any) => ({
      id: item.id,
      name: item.sendingName || '--',
      date: formatDate(item.startTime || item.updateDate || item.createTime),
      fileG9s: item.stampFileUpload || item.fileMain || item.otherAttachment,
    }));

    state.designFilesList = (designRes.data?.records || []).map((item: any) => ({
      id: item.id,
      name: item.processName || '--',
      date: formatDate(item.startTime || item.updateDate || item.createTime),
      fileG9s: item.stampFileUpload || item.fileMain || item.otherAttachment,
    }));

  } catch (error) {
    console.error('Failed to fetch file lists:', error);
  } finally {
    state.loading = false;
  }
};

// 处理文件点击事件
const handleFileClick = async (file: FileItem) => {
  if (!file.fileG9s) {
    ElMessage.warning('暂无文件');
    return
  }
  try {
    // 根据文件ID获取文件token
    const res = await getFiles({ g9s: [file.fileG9s] });
    if (res && res.data && res.data.length > 0) {
      // 获取所有文件的token
      const fileTokens = res.data.map(item => item.fileToken);
      // 直接使用openPreView方法，它现在可以处理单个或多个文件
      previewDialogRef.value?.openPreView(fileTokens);
    } else {
      ElMessage.warning('暂无文件');
    }
  } catch (error) {
    console.error('Failed to get file token:', error);
    ElMessage.error('文件预览失败');
  }
};

onMounted(() => {
  fetchFiles();
});

const { loading } = toRefs(state);

</script>

<style lang="scss" scoped>
.file-list-carousel-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.loading-state, .empty-state, .empty-page-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #fff;
  font-size: 16px;
}

.file-list-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  color: #fff;

  .page-category-title {
    position: relative;
    font-size: 16px;
    font-weight: normal;
    color: #EAF5FA;
    margin-bottom: 15px;
    text-align: left;
    padding-left: 15px; // Increased padding for the decorative bar

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #6BCAF6; // A light blue from the theme
      border-radius: 2px;
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
    overflow-y: auto;
  }

  .file-item {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 10px;
    margin-bottom: 16px;
    background: linear-gradient( 90deg, #0975A8 0%, rgba(16,123,172,0.14) 100%);
    border-radius: 3px;
    font-size: 14px;
    color: #DDEDF4;

    .file-index {
      margin-right: 8px;
    }

    .file-name {
      flex-grow: 1;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 10px;
    }

    .file-date {
      width: 80px;
      flex-shrink: 0;
      margin-left: 18px;
      text-align: right;
      color: #B9C8CD;
    }
  }
}
</style>
