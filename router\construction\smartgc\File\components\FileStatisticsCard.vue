<template>
  <div class="file-statistics-card" v-loading="loading">
    <div class="stats-container">
      <div v-for="(stat, index) in stats" :key="index" class="stat-box" :style="{ backgroundImage: `url(${stat.icon})`, backgroundSize: 'cover', backgroundPosition: 'center' }">
        <div class="stat-info">
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-value">{{ stat.value }}<span v-if="stat.unit" class="stat-unit">{{ stat.unit }}</span></div>
        </div>
      </div>
    </div>

    <div class="buttons-container">
      <button
        v-for="button in buttons"
        :key="button.type"
        class="stat-button"
        :class="{ active: activeChart === button.type }"
        @click="switchChart(button.type)"
      >
        {{ button.text }}
      </button>
    </div>

    <div class="chart-legend-container">
      <div ref="chartRef" class="chart-container"></div>
      <div class="legend-container">
        <template v-if="chartData.length > 0">
          <div v-for="item in chartData" :key="item.name" class="legend-item">
            <span class="legend-color-box" :style="{ backgroundColor: colorMap[item.name] }"></span>
            <span class="legend-label">{{ item.name }}</span>
            <span class="legend-value">{{ item.value }}</span>
          </div>
        </template>
        <!-- <div v-else class="empty-legend">暂无数据</div> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import img1 from '../img/img1.svg';
import img2 from '../img/img2.svg';
import { ref, onMounted, watch, computed } from 'vue';
import * as echarts from 'echarts';
import { fileStatistics } from '../api';

interface StatInfo {
  icon: string;
  label: string;
  value: string | number;
  unit?: string;
}

interface ChartDataItem {
  name: string;
  value: number;
}

const colorMap: Record<string, string> = {
  '业主文件': '#46EFA3',
  '监理文件': '#6BCAF6',
  '设计文件': '#EBBA65',
  '施工文件': '#1680CC',
};

const loading = ref(false);
const stats = ref<StatInfo[]>([
  { icon: img1, label: '累计文件', value: 0, unit: '个' },
  { icon: img2, label: '流转中', value: 0, unit: '个' },
]);

const activeChart = ref<'total' | 'processing'>('total');
const totalCountMap = ref<ChartDataItem[]>([]);
const processingCountMap = ref<ChartDataItem[]>([]);
const chartRef = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

const chartData = computed(() => {
  const data = activeChart.value === 'total' ? totalCountMap.value : processingCountMap.value;
  return data.sort((a, b) => {
    const aIndex = Object.keys(colorMap).indexOf(a.name);
    const bIndex = Object.keys(colorMap).indexOf(b.name);
    return aIndex - bIndex;
  });
});

const buttons = computed(() => [
  { text: '累计文件分布', type: 'total' as const },
  { text: '流转文件分布', type: 'processing' as const },
]);

const fetchData = async () => {
  loading.value = true;
  try {
    const res = await fileStatistics({});
    if (res.data) {
      const { totalCount, processingCount, totalCountMap: apiTotalMap, processingCountMap: apiProcessingMap } = res.data;
      
      stats.value[0].value = totalCount || 0;
      stats.value[1].value = processingCount || 0;

      totalCountMap.value = Object.entries(apiTotalMap || {}).map(([name, value]) => ({ name, value: value as number }));
      processingCountMap.value = Object.entries(apiProcessingMap || {}).map(([name, value]) => ({ name, value: value as number }));
      
      updateChart(chartData.value);
    }
  } catch (error) {
    console.error("Failed to fetch file statistics:", error);
  } finally {
    loading.value = false;
  }
};

const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);
    window.addEventListener('resize', () => {
      myChart?.resize();
    });
  }
};

const updateChart = (data: ChartDataItem[]) => {
  if (!myChart) return;
  
  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}',
    },
    series: [
      {
        type: 'pie',
        radius: ['70%', '100%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: 'rgba(0,0,0,0)',
          borderWidth: 2,
        },
        label: { show: false },
        labelLine: { show: false },
        data: data.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: colorMap[item.name] || '#ccc',
          },
        })),
      },
    ],
  };
  myChart.setOption(option, true);
};

const switchChart = (type: 'total' | 'processing') => {
  activeChart.value = type;
};

onMounted(() => {
  initChart();
  fetchData();
});

watch(chartData, (newData) => {
  updateChart(newData);
});
</script>

<style lang="scss" scoped>
.file-statistics-card {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  height: 56px;
  margin-bottom: 28px;
  gap: 34px;
}

.stat-box {
  flex: 1;
  
  display: flex;
  align-items: center;
  background-repeat: no-repeat;
  border: none;
  padding-left: 60px;
  

  .stat-info {
    height: 100%;
    display: flex;
    flex-direction: column;

    .stat-label {
      line-height: 24px;
      font-size: 14px;
      color: #aaddff;
    }
    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #ffffff;
      line-height: 28px;
      .stat-unit {
        font-size: 14px;
        margin-left: 4px;
      }
    }
  }
}

.buttons-container {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-button {
  height: 32px;
  line-height: 32px;
  padding: 0 24px;
  min-width: 120px;
  box-shadow: inset 0px 2px 5px 0px rgba(8, 68, 105, 0.59), 0px 2px 6px 0px rgba(44, 134, 183, 0.25);
  border-radius: 3px;
  border: 1px solid #e1f7ff;
  text-align: center;
  background: transparent;
  color: #fff;
  transition: all 0.2s;

  // &:hover {
  //   background-color: rgba(0, 128, 255, 0.4);
  // }

  &.active {
    background: linear-gradient(270deg, rgba(19, 158, 223, 0.13) 0%, rgba(49, 190, 255, 0.5) 100%);
    box-shadow: 0px 2px 6px 0px rgba(44, 134, 183, 0.25);
    border: 1px solid #9ed4e7;
  }
}

.chart-legend-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 28px;
  min-height: 0;
}

.chart-container {
  flex: 1;
  height: 100%;
}

.legend-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 15px;
  width: 160px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #fff;

  .legend-color-box {
    width: 12px;
    height: 12px;
    margin-right: 12px;
    flex-shrink: 0;
    border-radius: 50%;
  }

  .legend-label {
    flex: 1;
    white-space: nowrap;
    font-size: 14px;
  }

  .legend-value {
    width: 32px;
    margin-left: 8px;
    font-size: 18px;
  }
}

.empty-legend {
  color: #86909c;
  font-size: 14px;
  text-align: center;
}
</style>
