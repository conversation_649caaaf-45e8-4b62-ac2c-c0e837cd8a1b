<template>
  <div v-loading="loading" ref="chartRef" class="new-file-statistics-chart"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';
import { fileAddStatistics } from '../api';

const loading = ref(false);
const chartRef = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

const colorMap: Record<string, string> = {
  '业主文件': '#46EFA3',
  '监理文件': '#6BCAF6',
  '设计文件': '#EBBA65',
  '施工文件': '#1680CC',
};

const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);
    window.addEventListener('resize', () => myChart?.resize());
  }
};

const updateChart = (data: any[]) => {
  if (!myChart) return;

  if (!data || data.length === 0) {
    myChart.clear();
    myChart.setOption({
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: { color: '#fff' },
      },
    });
    return;
  }

  const months = data.map(item => item.month);
  const legendData = Object.keys(data[0]).filter(key => key !== 'month');

  const series = legendData.map(key => ({
    name: key,
    type: 'line' as const,
    smooth: false,
    showSymbol: true,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      color: colorMap[key],
    },
    itemStyle: {
      color: colorMap[key],
    },
    data: data.map(item => item[key] || 0),
  }));

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendData,
      textStyle: {
        color: '#fff',
      },
      top: '5%',
    },
    grid: {
      top: '20%',
      left: '8%',
      right: '8%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: months,
      axisLabel: {
        color: '#fff',
      },
    },
    yAxis: {
      type: 'value',
      name: '单位 (个)',
      nameTextStyle: {
        color: '#fff',
        align: 'right'
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.3)',
          type: [5, 5],
        },
      },
    },
    series,
  };
  myChart.setOption(option, true);
};

const fetchData = async () => {
  loading.value = true;
  try {
    const res = await fileAddStatistics({});
    updateChart(res.data || []);
  } catch (error) {
    console.error("Failed to fetch new file statistics:", error);
    updateChart([]); // Handle error by showing empty state
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  initChart();
  fetchData();
});
</script>

<style lang="scss" scoped>
.new-file-statistics-chart {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
