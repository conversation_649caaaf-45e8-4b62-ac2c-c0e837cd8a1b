<svg width="199" height="68" viewBox="0 0 199 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#190;&#133;&#230;&#149;&#180;&#230;&#148;&#185;&#233;&#151;&#174;&#233;&#162;&#152;">
<rect id="Rectangle 34624356" x="0.5" y="-0.5" width="54" height="54" transform="matrix(1 0 0 -1 0 59)" stroke="#E6F1FF"/>
<g id="Rectangle 34624359" filter="url(#filter0_d_405_2985)">
<rect width="132" height="22" transform="matrix(1 0 0 -1 61 27)" fill="url(#paint0_linear_405_2985)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<g id="Rectangle 34624360" filter="url(#filter1_d_405_2985)">
<rect width="132" height="30" transform="matrix(1 0 0 -1 61 60)" fill="url(#paint1_linear_405_2985)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<g id="Group 1321315015" opacity="0.6">
<path id="Union" opacity="0.5" d="M118.554 60H100.554L84.0547 30H102.555L118.554 60ZM141.108 60H123.108L106.609 30H125.109L141.108 60ZM163.108 60H145.108L128.609 30H147.109L163.108 60ZM171.109 30L187.108 60H169.108L152.609 30H171.109Z" fill="url(#paint2_linear_405_2985)"/>
</g>
<g id="Rectangle 34624357" filter="url(#filter2_di_405_2985)">
<rect width="40" height="40" rx="3" transform="matrix(1 0 0 -1 8 53)" fill="url(#paint3_linear_405_2985)" fill-opacity="0.7" shape-rendering="crispEdges"/>
<rect x="0.3" y="-0.3" width="39.4" height="39.4" rx="2.7" transform="matrix(1 0 0 -1 8 52.4)" stroke="url(#paint4_linear_405_2985)" stroke-width="0.6" shape-rendering="crispEdges"/>
</g>
<g id="Frame">
<path id="Vector" d="M36 24C36.2652 24 36.5196 24.1054 36.7071 24.2929C36.8946 24.4804 37 24.7348 37 25V41C37 41.2652 36.8946 41.5196 36.7071 41.7071C36.5196 41.8946 36.2652 42 36 42H20C19.7348 42 19.4804 41.8946 19.2929 41.7071C19.1054 41.5196 19 41.2652 19 41V25C19 24.7348 19.1054 24.4804 19.2929 24.2929C19.4804 24.1054 19.7348 24 20 24H36ZM24.5 31H23.5C23.3674 31 23.2402 31.0527 23.1464 31.1464C23.0527 31.2402 23 31.3674 23 31.5V37.5C23 37.6326 23.0527 37.7598 23.1464 37.8536C23.2402 37.9473 23.3674 38 23.5 38H24.5C24.6326 38 24.7598 37.9473 24.8536 37.8536C24.9473 37.7598 25 37.6326 25 37.5V31.5C25 31.3674 24.9473 31.2402 24.8536 31.1464C24.7598 31.0527 24.6326 31 24.5 31ZM28.5 28H27.5C27.4343 28 27.3693 28.0129 27.3087 28.0381C27.248 28.0632 27.1929 28.1 27.1464 28.1464C27.1 28.1929 27.0632 28.248 27.0381 28.3087C27.0129 28.3693 27 28.4343 27 28.5V37.5C27 37.5657 27.0129 37.6307 27.0381 37.6913C27.0632 37.752 27.1 37.8071 27.1464 37.8536C27.1929 37.9 27.248 37.9368 27.3087 37.9619C27.3693 37.9871 27.4343 38 27.5 38H28.5C28.5657 38 28.6307 37.9871 28.6913 37.9619C28.752 37.9368 28.8071 37.9 28.8536 37.8536C28.9 37.8071 28.9368 37.752 28.9619 37.6913C28.9871 37.6307 29 37.5657 29 37.5V28.5C29 28.4343 28.9871 28.3693 28.9619 28.3087C28.9368 28.248 28.9 28.1929 28.8536 28.1464C28.8071 28.1 28.752 28.0632 28.6913 28.0381C28.6307 28.0129 28.5657 28 28.5 28ZM32.5 34H31.5C31.3674 34 31.2402 34.0527 31.1464 34.1464C31.0527 34.2402 31 34.3674 31 34.5V37.5C31 37.6326 31.0527 37.7598 31.1464 37.8536C31.2402 37.9473 31.3674 38 31.5 38H32.5C32.6326 38 32.7598 37.9473 32.8536 37.8536C32.9473 37.7598 33 37.6326 33 37.5V34.5C33 34.3674 32.9473 34.2402 32.8536 34.1464C32.7598 34.0527 32.6326 34 32.5 34Z" fill="url(#paint5_linear_405_2985)"/>
</g>
<g id="Rectangle 34624358" filter="url(#filter3_d_405_2985)">
<rect x="22" y="51" width="12" height="2" fill="#D4FFF7"/>
</g>
<g id="Rectangle 34624361" filter="url(#filter4_d_405_2985)">
<rect x="61" y="5" width="4" height="2" fill="#D4FFF7"/>
</g>
<rect id="Rectangle 34624363" y="58" width="4" height="2" fill="#D4FFF7"/>
<rect id="Rectangle 34624362" x="67" y="5" width="122" height="2" fill="url(#paint6_linear_405_2985)"/>
<rect id="Rectangle 34624364" x="6" y="58" width="49" height="2" fill="url(#paint7_linear_405_2985)"/>
</g>
<defs>
<filter id="filter0_d_405_2985" x="55" y="1" width="144" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_405_2985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_405_2985" result="shape"/>
</filter>
<filter id="filter1_d_405_2985" x="55" y="26" width="144" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_405_2985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_405_2985" result="shape"/>
</filter>
<filter id="filter2_di_405_2985" x="2" y="9" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_405_2985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_405_2985" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0330014 0 0 0 0 0.26663 0 0 0 0 0.411859 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_405_2985"/>
</filter>
<filter id="filter3_d_405_2985" x="17" y="46" width="22" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_405_2985"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.56912 0 0 0 0 0.88627 0 0 0 0 0.991987 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_405_2985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_405_2985" result="shape"/>
</filter>
<filter id="filter4_d_405_2985" x="56" y="0" width="14" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_405_2985"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.88 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_405_2985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_405_2985" result="shape"/>
</filter>
<linearGradient id="paint0_linear_405_2985" x1="132" y1="11" x2="0" y2="11" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC" stop-opacity="0.13"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint1_linear_405_2985" x1="132" y1="15" x2="0" y2="15" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC" stop-opacity="0.13"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint2_linear_405_2985" x1="102" y1="58.5001" x2="116.813" y2="19.426" gradientUnits="userSpaceOnUse">
<stop stop-color="#6B96CC"/>
<stop offset="1" stop-color="#95BEF1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_405_2985" x1="20" y1="40" x2="20" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint4_linear_405_2985" x1="20" y1="-4.21053" x2="20" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="#E1F7FF"/>
<stop offset="1" stop-color="#D7ECFF"/>
</linearGradient>
<linearGradient id="paint5_linear_405_2985" x1="28" y1="24" x2="28" y2="42" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#B9E7FF"/>
</linearGradient>
<linearGradient id="paint6_linear_405_2985" x1="67" y1="7" x2="189" y2="7" gradientUnits="userSpaceOnUse">
<stop stop-color="#D7E9FF"/>
<stop offset="1" stop-color="#254874" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_405_2985" x1="6" y1="60" x2="55" y2="60" gradientUnits="userSpaceOnUse">
<stop stop-color="#2A79E7"/>
<stop offset="1" stop-color="#73ACF5" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
