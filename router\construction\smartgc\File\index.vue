<!--建设总览 -->
<script lang="ts" setup>
import {
  onMounted,
} from 'vue'
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import FileStatisticsCard from './components/FileStatisticsCard.vue';
import NewFileStatisticsChart from './components/NewFileStatisticsChart.vue';
import FileListCarousel from './components/FileListCarousel.vue';

onMounted(() => {

});

</script>

<template>
  <PageLayout>
    <template #left>
      <Card title="文件统计" style="height: 50%;">
        <FileStatisticsCard />
      </Card>
      <Card title="新增文件统计" style="height: 50%;">
        <NewFileStatisticsChart />
      </Card>
    </template>
    
    <template #right>
      <Card title="文件列表" style="height: 100%;">
        <FileListCarousel />
        
      </Card>
    </template>
    
  </PageLayout>
</template>

<style lang="scss" scoped>


</style>
