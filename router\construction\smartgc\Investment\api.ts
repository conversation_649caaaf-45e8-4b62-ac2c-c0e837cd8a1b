import request from '~/router/request'
import { AxiosPromise } from 'axios'
let TCS_SERVICE_PREFIX = import.meta.env.CVE_VUE_APP_BASE_TCS_SERVICENAME

/**
 * 合同投资额统计
 * @param
 * @returns
 */
export function contractAmountStatistics(): AxiosPromise<any> {
  return request({
    url: `${TCS_SERVICE_PREFIX}/invest/bigscreen/contract/amount/statistics`,
    method: 'get',
  })
}

/**
 * 合同文件统计
 * @param
 * @returns
 */
export function contractFileStatistics(): AxiosPromise<any> {
  return request({
    url: `${TCS_SERVICE_PREFIX}/invest/bigscreen/contract/file/statistics`,
    method: 'get',
  })
}

/**
 * 招标文件统计
 * @param
 * @returns
 */
export function tenderFileStatistics(): AxiosPromise<any> {
  return request({
    url: `${TCS_SERVICE_PREFIX}/invest/bigscreen/tenders/file/statistics`,
    method: 'get',
  })
}
