<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import { contractAmountStatistics } from '../api';

const pieChartRef = ref<HTMLElement | null>(null);
const barChartRef = ref<HTMLElement | null>(null);
const contractMoneyTotals = ref('0.00');
const alreadyPayMoneyTotals = ref('0.00');

let pieChart: echarts.ECharts;
let barChart: echarts.ECharts;

const initPieChart = (data: any) => {
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value);
    const settled = parseFloat(data.alreadyPayMoneyTotals) || 0;
    const total = parseFloat(data.contractMoneyTotals) || 0;
    const unsettled = total - settled > 0 ? total - settled : 0;
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      series: [
        {
          type: 'pie',
          radius: ['65%', '85%'],
          avoidLabelOverlap: false,
          label: { show: false },
          emphasis: { label: { show: false } },
          labelLine: { show: false },
          data: [
            { value: settled, name: '已结算金额' },
            { value: unsettled, name: '未结算金额' },
          ],
          color: ['#76e7ec', '#4668e4']
        }
      ]
    };
    pieChart.setOption(option);
  }
};

const initBarChart = (barData: any[]) => {
  if (barChartRef.value) {
    barChart = echarts.init(barChartRef.value);
    const barOption = {
      grid: {
        left: '0%',
        right: '12%', // 留出空间给数值
        bottom: '0%',
        top: '0%',
        containLabel: true,
      },
      xAxis: {
        show: false, // 不显示x轴
        type: 'value',
      },
      yAxis: {
        type: 'category',
        data: barData.map(item => item.contractType).reverse(), // 反转数据，使一标段在最上面
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: { show: false },
        axisLabel: {
          color: '#A2C2D4', // 字体颜色
          fontSize: 14,
        },
      },
      series: [
        {
          type: 'bar',
          data: barData.map(item => item.total).reverse(), // 反转数据
          barWidth: 16, // 调整宽度
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(3,87,102,0.51)' },
              { offset: 1, color: '#21D3F3' }
            ])
          },
          label: {
            show: true,
            position: 'right',
            color: '#A2C2D4', // 数值颜色
            fontSize: 14,
            offset: [5, 0] // 偏移
          },
        },
      ],
    };
    barChart.setOption(barOption);
  }
};

const resizeCharts = () => {
  pieChart?.resize();
  barChart?.resize();
};

onMounted(async () => {
  try {
    const res = await contractAmountStatistics();
    if (res.data) {
      contractMoneyTotals.value = res.data.contractMoneyTotals;
      alreadyPayMoneyTotals.value = res.data.alreadyPayMoneyTotals;
      await nextTick();
      initPieChart(res.data);
      initBarChart(res.data.contractTypeRank || []);
      window.addEventListener('resize', resizeCharts);
    }
  } catch (error) {
    console.error('Failed to fetch contract amount statistics:', error);
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeCharts);
  pieChart?.dispose();
  barChart?.dispose();
});

</script>

<template>
  <div class="contract-investment-container">
    <div class="top-section">
      <div class="chart-wrapper">
        <div ref="pieChartRef" class="gauge-chart"></div>
      </div>
      <div class="info-wrapper">
        <div class="info-item accumulated">
          <span class="label">已结算金额</span>
          <span class="value">{{ alreadyPayMoneyTotals }}</span>
          <span class="unit">亿</span>
        </div>
        <div class="info-item contract">
          <span class="label">合同金额</span>
          <span class="value">{{ contractMoneyTotals }}</span>
          <span class="unit">亿</span>
        </div>
      </div>
    </div>
    <div ref="barChartRef" class="bar-chart"></div>
  </div>
</template>

<style lang="scss" scoped>
.contract-investment-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .top-section {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .chart-wrapper {
      width: 162px;
      height: 162px;
      margin-right: 16px;
      position: relative;
      background-image: url('../img/img1.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      .gauge-chart {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    .info-wrapper {
      flex-grow: 1;
      width: 200px;
      height: 126px;
      padding-left: 24px;
      background-image: url('../img/img2.svg');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .info-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        position: relative;
        padding-left: 22px;
        color: #fff;

        &.accumulated {
          margin-bottom: 24px;
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 18px;
            background: url('../img/img3.svg') no-repeat center center;
            background-size: contain;
          }
        }

        &.contract {
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 18px;
            background: url('../img/img4.svg') no-repeat center center;
            background-size: contain;
          }
        }

        .label {
          min-width: 68px;
          margin-right: 24px;
        }
        .value {
          font-size: 20px;
          font-weight: bold;
          margin-right: 3px;
        }
        .unit {
          color: #BCC5CE;
          font-size: 12px;
        }
      }
    }
  }

  .bar-chart {
    flex-grow: 1;
    width: 100%;
  }
}
</style>
