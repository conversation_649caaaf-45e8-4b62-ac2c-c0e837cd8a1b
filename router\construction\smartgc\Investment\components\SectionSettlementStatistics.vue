<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { contractAmountStatistics } from '../api';

let myChart: echarts.ECharts | null = null;
const chartRef = ref<HTMLElement | null>(null);

const initChart = (data: any[]) => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);
    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const item = params.find((p: any) => p.seriesName === '结算率');
          if (item) {
            return `${item.name}<br/>${item.seriesName}: ${item.value}%`;
          }
          return '';
        }
      },
      grid: {
        top: '20%',
        left: '8%',
        right: '8%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.subProjectId),
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.5)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          interval: 0,
        }
      },
      yAxis: {
        type: 'value',
        name: '单位 (%)',
        nameTextStyle: {
          color: '#ffffff',
          align: 'right',
          padding: [0, 10, 0, 0]
        },
        max: 100,
        axisLabel: {
          color: '#ffffff'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '背景',
          type: 'bar',
          barWidth: '40%',
          itemStyle: {
            color: 'rgba(50, 209, 237, 0.19)',
          },
          barGap: '-100%',
          data: data.map(() => 100),
          animation: false,
          tooltip: {
             show: false
          }
        },
        {
          name: '结算率',
          type: 'bar',
          barWidth: '40%',
          data: data.map(item => item.rate),
          itemStyle: {
            color: 'rgba(50, 209, 237, 1)'
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
            color: '#ffffff',
            fontSize: 12,
          }
        }
      ]
    };
    myChart.setOption(option);
  }
};

const fetchData = async () => {
  try {
    const res = await contractAmountStatistics();
    if (res.status && res.data && res.data.subProjectSettle) {
      const apiData = res.data.subProjectSettle;

      const nameMapping: { [key: string]: string } = {
        '前期1标': '一标段',
        '前期2标': '二标段',
        '前期3标': '三标段',
        'EPC标': 'EPC',
        '供电工程': '供电工程'
      };

      const filteredData = apiData.filter((item: any) => nameMapping.hasOwnProperty(item.subProjectId));
      
      const processedData = filteredData.map((item: any) => ({
        ...item,
        subProjectId: nameMapping[item.subProjectId]
      }));

      const order = ['一标段', '二标段', '三标段', 'EPC', '供电工程'];
      processedData.sort((a: any, b: any) => {
        return order.indexOf(a.subProjectId) - order.indexOf(b.subProjectId);
      });
      
      initChart(processedData);
    }
  } catch (error) {
    console.error('Failed to fetch settlement statistics:', error);
  }
};

const resizeChart = () => {
  myChart?.resize();
};

onMounted(() => {
  fetchData();
  window.addEventListener('resize', resizeChart);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
  myChart?.dispose();
});
</script>

<style scoped>
.container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.chart-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
