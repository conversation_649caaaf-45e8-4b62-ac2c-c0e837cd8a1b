<template>
  <div class="statistics-card">
    <div class="stats-container">
      <div v-for="(stat, index) in stats" :key="index" class="stat-box" :style="{ backgroundImage: `url(${stat.icon})`, backgroundSize: 'cover', backgroundPosition: 'center' }">
        <div class="stat-info">
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-value">{{ stat.value }}<span v-if="stat.unit" class="stat-unit">{{ stat.unit }}</span></div>
        </div>
      </div>
    </div>

    <div class="buttons-container">
      <button
        v-for="button in buttons"
        :key="button.type"
        class="stat-button"
        :class="{ active: activeChart === button.type }"
        @click="switchChart(button.type)"
      >
        {{ button.text }}
      </button>
    </div>

    <div class="chart-legend-container">
      <div ref="chartRef" class="chart-container"></div>
      <div class="legend-container">
        <div class="legend-items-wrapper">
          <div v-for="(item, index) in chartData" :key="index" class="legend-item">
            <span class="legend-color-box" :style="{ backgroundColor: item.color }"></span>
            <span class="legend-label">{{ item.name }}</span>
            <span class="legend-value">{{ item.value }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, PropType, watch } from 'vue';
import * as echarts from 'echarts';

interface StatInfo {
  icon: string;
  label: string;
  value: string | number;
  unit?: string;
}

interface ButtonInfo {
  text: string;
  type: string;
}

interface ChartDataItem {
  name: string;
  value: number;
  color: string;
}

const props = defineProps({
  stats: {
    type: Array as PropType<StatInfo[]>,
    required: true,
  },
  buttons: {
    type: Array as PropType<ButtonInfo[]>,
    required: true,
  },
  chartData: {
    type: Array as PropType<ChartDataItem[]>,
    required: true,
  },
});

const emit = defineEmits(['switch-chart']);

const chartRef = ref<HTMLElement | null>(null);
const activeChart = ref(props.buttons[0]?.type || '');
let myChart: echarts.ECharts | null = null;

const updateChart = () => {
  if (!chartRef.value) return;

  if (!myChart) {
    myChart = echarts.init(chartRef.value);
  }

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {d}%',
    },
    series: [
      {
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: 'rgba(0,0,0,0)',
          borderWidth: 2,
        },
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: props.chartData.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color,
          },
        })),
      },
    ],
  };
  myChart.setOption(option, true);
};

const switchChart = (type: string) => {
  if (activeChart.value === type) return;
  activeChart.value = type;
  emit('switch-chart', type);
};

const resizeChart = () => {
  myChart?.resize();
};

watch(
  () => props.chartData,
  () => {
    updateChart();
  },
  { deep: true }
);

onMounted(() => {
  updateChart();
  window.addEventListener('resize', resizeChart);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
  myChart?.dispose();
});
</script>

<style lang="scss" scoped>
.statistics-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  height: 56px;
  margin-bottom: 28px;
  gap: 34px;
}

.stat-box {
  flex: 1;
  
  display: flex;
  align-items: center;
  background-repeat: no-repeat;
  border: none;
  padding-left: 60px;
  

  .stat-info {
    height: 100%;
    display: flex;
    flex-direction: column;

    .stat-label {
      line-height: 24px;
      font-size: 14px;
      color: #aaddff;
    }
    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #ffffff;
      line-height: 28px;
      .stat-unit {
        font-size: 14px;
        margin-left: 4px;
      }
    }
  }
}

.buttons-container {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-button {
  height: 32px;
  line-height: 32px;
  padding: 0 24px;
  min-width: 120px;
  box-shadow: inset 0px 2px 5px 0px rgba(8, 68, 105, 0.59), 0px 2px 6px 0px rgba(44, 134, 183, 0.25);
  border-radius: 3px;
  border: 1px solid #e1f7ff;
  text-align: center;
  background: transparent;
  color: #fff;
  transition: all 0.2s;

  // &:hover {
  //   background-color: rgba(0, 128, 255, 0.4);
  // }

  &.active {
    background: linear-gradient(270deg, rgba(19, 158, 223, 0.13) 0%, rgba(49, 190, 255, 0.5) 100%);
    box-shadow: 0px 2px 6px 0px rgba(44, 134, 183, 0.25);
    border: 1px solid #9ed4e7;
  }
}

.chart-legend-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
  min-height: 0;
  position: relative;
  overflow: hidden;
}

.chart-container {
  flex: 1;
  height: 100%;
}

.legend-container {
  display: flex;
  flex-direction: column;
  width: 170px;
  height: 100%;
  overflow-y: auto;
  padding: 10px 0;
}

.legend-items-wrapper {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: auto 0;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: white;

  .legend-color-box {
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .legend-label {
    width: 95px;
    white-space: nowrap;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .legend-value {
    font-weight: bold;
  }
}
</style>
