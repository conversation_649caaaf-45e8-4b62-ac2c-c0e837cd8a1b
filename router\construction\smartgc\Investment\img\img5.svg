<svg width="199" height="68" viewBox="0 0 199 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#180;&#175;&#232;&#174;&#161;&#228;&#184;&#180;&#230;&#163;&#128;">
<rect id="Rectangle 34624356" x="0.5" y="-0.5" width="54" height="54" transform="matrix(1 0 0 -1 0 59)" stroke="#E6F1FF"/>
<g id="Rectangle 34624359" filter="url(#filter0_d_404_2248)">
<rect width="132" height="22" transform="matrix(1 0 0 -1 61 27)" fill="url(#paint0_linear_404_2248)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<g id="Rectangle 34624360" filter="url(#filter1_d_404_2248)">
<rect width="132" height="30" transform="matrix(1 0 0 -1 61 60)" fill="url(#paint1_linear_404_2248)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<g id="Group 1321315015" opacity="0.6">
<path id="Union" opacity="0.5" d="M118.554 60H100.554L84.0547 30H102.555L118.554 60ZM141.108 60H123.108L106.609 30H125.109L141.108 60ZM163.108 60H145.108L128.609 30H147.109L163.108 60ZM171.109 30L187.108 60H169.108L152.609 30H171.109Z" fill="url(#paint2_linear_404_2248)"/>
</g>
<g id="Rectangle 34624357" filter="url(#filter2_di_404_2248)">
<rect width="40" height="40" rx="3" transform="matrix(1 0 0 -1 8 53)" fill="url(#paint3_linear_404_2248)" fill-opacity="0.7" shape-rendering="crispEdges"/>
<rect x="0.3" y="-0.3" width="39.4" height="39.4" rx="2.7" transform="matrix(1 0 0 -1 8 52.4)" stroke="url(#paint4_linear_404_2248)" stroke-width="0.6" shape-rendering="crispEdges"/>
</g>
<g id="mx_n_1697511298542">
<path id="Vector" d="M34.832 29.5C35.3345 29.5377 35.8139 29.7261 36.2077 30.0405C36.6014 30.3549 36.8912 30.7808 37.0392 31.2624C37.1871 31.7441 37.1864 32.2592 37.037 32.7405C36.8877 33.2217 36.5967 33.6467 36.202 33.96L36.052 34.071L35.808 34.225L35.778 34.514C35.672 35.323 35.497 37.951 37.525 38.946L37.735 39.04L37.859 39.089H39.044C39.18 39.089 39.294 39.184 39.324 39.311L39.332 39.378V42.096C39.332 42.353 39.247 42.406 39.132 42.414L39.089 42.415H30.29C30.2229 42.4159 30.1577 42.3932 30.1057 42.3507C30.0538 42.3083 30.0185 42.2489 30.006 42.183L30.001 42.126V39.378C30.0001 39.3109 30.0228 39.2457 30.0653 39.1937C30.1077 39.1418 30.1671 39.1065 30.233 39.094L30.29 39.089H31.618L31.726 39.04C33.981 38.127 33.792 35.342 33.71 34.648L33.703 34.588L33.654 34.285L33.399 34.135C33.0265 33.9135 32.7172 33.5998 32.5011 33.2241C32.285 32.8484 32.1692 32.4234 32.165 31.99C32.166 31.3589 32.4063 30.7516 32.8374 30.2906C33.2685 29.8297 33.8584 29.5493 34.488 29.506L34.651 29.5H34.831H34.832ZM34 22.5C34.2652 22.5 34.5196 22.6054 34.7071 22.7929C34.8946 22.9804 35 23.2348 35 23.5V27.8C34.8753 27.7846 34.7496 27.7783 34.624 27.781C34.0892 27.7638 33.5563 27.8536 33.0566 28.0451C32.5569 28.2365 32.1004 28.5258 31.714 28.896C31.3286 29.2651 31.0212 29.7078 30.8099 30.1979C30.5986 30.6879 30.4878 31.2154 30.484 31.749C30.4873 32.3784 30.6348 32.9987 30.9151 33.5622C31.1954 34.1258 31.6011 34.6176 32.101 35C32.129 35.625 32.078 36.817 31.296 37.362L31.141 37.456H29.843C29.008 37.456 28.323 38.093 28.249 38.905L28.243 39.05V41.498H19.967L19.883 41.493C19.6397 41.4643 19.4155 41.3474 19.2527 41.1644C19.09 40.9813 19 40.7449 19 40.5V23.5C19 23.2348 19.1054 22.9804 19.2929 22.7929C19.4804 22.6054 19.7348 22.5 20 22.5H34ZM26.5 29.5H22.5C22.383 29.5 22.2696 29.541 22.1797 29.6159C22.0898 29.6908 22.0291 29.7949 22.008 29.91L22 30V30.5C22 30.617 22.041 30.7304 22.1159 30.8203C22.1908 30.9102 22.2949 30.9709 22.41 30.992L22.5 31H26.5C26.617 31 26.7304 30.959 26.8203 30.8841C26.9102 30.8092 26.9709 30.7051 26.992 30.59L27 30.5V30C27 29.9343 26.9871 29.8693 26.9619 29.8087C26.9368 29.748 26.9 29.6929 26.8536 29.6464C26.8071 29.6 26.752 29.5632 26.6913 29.5381C26.6307 29.5129 26.5657 29.5 26.5 29.5ZM29.5 25.5H22.5C22.383 25.5 22.2696 25.541 22.1797 25.6159C22.0898 25.6908 22.0291 25.7949 22.008 25.91L22 26V26.5C22 26.617 22.041 26.7304 22.1159 26.8203C22.1908 26.9102 22.2949 26.9709 22.41 26.992L22.5 27H29.5C29.617 27 29.7304 26.959 29.8203 26.8841C29.9102 26.8092 29.9709 26.7051 29.992 26.59L30 26.5V26C30 25.8674 29.9473 25.7402 29.8536 25.6464C29.7598 25.5527 29.6326 25.5 29.5 25.5Z" fill="url(#paint5_linear_404_2248)"/>
</g>
<g id="Rectangle 34624358" filter="url(#filter3_d_404_2248)">
<rect x="22" y="51" width="12" height="2" fill="#D4FFF7"/>
</g>
<g id="Rectangle 34624361" filter="url(#filter4_d_404_2248)">
<rect x="61" y="5" width="4" height="2" fill="#D4FFF7"/>
</g>
<rect id="Rectangle 34624363" y="58" width="4" height="2" fill="#D4FFF7"/>
<rect id="Rectangle 34624362" x="67" y="5" width="122" height="2" fill="url(#paint6_linear_404_2248)"/>
<rect id="Rectangle 34624364" x="6" y="58" width="49" height="2" fill="url(#paint7_linear_404_2248)"/>
</g>
<defs>
<filter id="filter0_d_404_2248" x="55" y="1" width="144" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2248"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2248" result="shape"/>
</filter>
<filter id="filter1_d_404_2248" x="55" y="26" width="144" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2248"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2248" result="shape"/>
</filter>
<filter id="filter2_di_404_2248" x="2" y="9" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2248"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2248" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0330014 0 0 0 0 0.26663 0 0 0 0 0.411859 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_404_2248"/>
</filter>
<filter id="filter3_d_404_2248" x="17" y="46" width="22" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_404_2248"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.56912 0 0 0 0 0.88627 0 0 0 0 0.991987 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2248"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2248" result="shape"/>
</filter>
<filter id="filter4_d_404_2248" x="56" y="0" width="14" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_404_2248"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.88 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2248"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2248" result="shape"/>
</filter>
<linearGradient id="paint0_linear_404_2248" x1="132" y1="11" x2="0" y2="11" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC" stop-opacity="0.13"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint1_linear_404_2248" x1="132" y1="15" x2="0" y2="15" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC" stop-opacity="0.13"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint2_linear_404_2248" x1="102" y1="58.5001" x2="116.813" y2="19.426" gradientUnits="userSpaceOnUse">
<stop stop-color="#6B96CC"/>
<stop offset="1" stop-color="#95BEF1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_404_2248" x1="20" y1="40" x2="20" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint4_linear_404_2248" x1="20" y1="-4.21053" x2="20" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="#E1F7FF"/>
<stop offset="1" stop-color="#D7ECFF"/>
</linearGradient>
<linearGradient id="paint5_linear_404_2248" x1="29.166" y1="22.5" x2="29.166" y2="42.415" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#B9E7FF"/>
</linearGradient>
<linearGradient id="paint6_linear_404_2248" x1="67" y1="7" x2="189" y2="7" gradientUnits="userSpaceOnUse">
<stop stop-color="#D7E9FF"/>
<stop offset="1" stop-color="#254874" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_404_2248" x1="6" y1="60" x2="55" y2="60" gradientUnits="userSpaceOnUse">
<stop stop-color="#2A79E7"/>
<stop offset="1" stop-color="#73ACF5" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
