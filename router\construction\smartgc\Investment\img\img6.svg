<svg width="199" height="68" viewBox="0 0 199 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#233;&#151;&#174;&#233;&#162;&#152;&#229;&#143;&#145;&#231;&#142;&#176;&#233;&#162;&#145;&#230;&#172;&#161;">
<rect id="Rectangle 34624356" x="0.5" y="-0.5" width="54" height="54" transform="matrix(1 0 0 -1 0 59)" stroke="#E6F1FF"/>
<g id="Rectangle 34624359" filter="url(#filter0_d_404_2269)">
<rect width="132" height="22" transform="matrix(1 0 0 -1 61 27)" fill="url(#paint0_linear_404_2269)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<g id="Rectangle 34624360" filter="url(#filter1_d_404_2269)">
<rect width="132" height="30" transform="matrix(1 0 0 -1 61 60)" fill="url(#paint1_linear_404_2269)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<g id="Group 1321315015" opacity="0.6">
<path id="Union" opacity="0.5" d="M118.554 60H100.554L84.0547 30H102.555L118.554 60ZM141.108 60H123.108L106.609 30H125.109L141.108 60ZM163.108 60H145.108L128.609 30H147.109L163.108 60ZM171.109 30L187.108 60H169.108L152.609 30H171.109Z" fill="url(#paint2_linear_404_2269)"/>
</g>
<g id="Rectangle 34624357" filter="url(#filter2_di_404_2269)">
<rect width="40" height="40" rx="3" transform="matrix(1 0 0 -1 8 53)" fill="url(#paint3_linear_404_2269)" fill-opacity="0.7" shape-rendering="crispEdges"/>
<rect x="0.3" y="-0.3" width="39.4" height="39.4" rx="2.7" transform="matrix(1 0 0 -1 8 52.4)" stroke="url(#paint4_linear_404_2269)" stroke-width="0.6" shape-rendering="crispEdges"/>
</g>
<g id="Frame">
<path id="Vector" d="M34.837 35.158L37.593 36.622C37.97 36.812 38.106 37.286 37.915 37.657C37.8426 37.7926 37.7302 37.9026 37.593 37.972L28.363 42.912C28.2508 42.9696 28.1265 42.9997 28.0005 42.9997C27.8744 42.9997 27.7501 42.9696 27.638 42.912L18.409 37.972C18.032 37.782 17.896 37.308 18.087 36.932C18.1623 36.7999 18.2741 36.6923 18.409 36.622L21.159 35.158L18.409 33.678C18.2293 33.5808 18.0949 33.417 18.0346 33.2218C17.9744 33.0266 17.9932 32.8155 18.087 32.634C18.1658 32.5059 18.2768 32.4008 18.409 32.329L21.159 30.859L18.409 29.379C18.287 29.3143 18.1849 29.2177 18.1137 29.0993C18.0425 28.981 18.0049 28.8456 18.0049 28.7075C18.0049 28.5694 18.0425 28.434 18.1137 28.3157C18.1849 28.1973 18.287 28.1007 18.409 28.036L27.638 23.09C27.868 22.97 28.14 22.97 28.363 23.09L37.593 28.036C37.772 28.1312 37.9062 28.2932 37.9665 28.4868C38.0267 28.6804 38.0082 28.89 37.915 29.07C37.8377 29.2006 37.7264 29.3078 37.593 29.38L34.837 30.86L37.593 32.33C37.7718 32.4254 37.9058 32.5874 37.9661 32.7809C38.0263 32.9745 38.008 33.1839 37.915 33.364C37.8377 33.4959 37.7265 33.6047 37.593 33.679L34.837 35.159V35.158ZM27.998 37.074C30.536 35.719 33.08 34.364 35.612 32.998L33.222 31.72L28.363 34.316C28.288 34.3612 28.2047 34.3908 28.118 34.403H28.102L28.031 34.413H27.971L27.906 34.403H27.884C27.7972 34.3899 27.7137 34.3603 27.638 34.316L22.781 31.72L20.391 32.998C22.922 34.364 25.471 35.718 27.998 37.074Z" fill="url(#paint5_linear_404_2269)"/>
</g>
<g id="Rectangle 34624358" filter="url(#filter3_d_404_2269)">
<rect x="22" y="51" width="12" height="2" fill="#D4FFF7"/>
</g>
<g id="Rectangle 34624361" filter="url(#filter4_d_404_2269)">
<rect x="61" y="5" width="4" height="2" fill="#D4FFF7"/>
</g>
<rect id="Rectangle 34624363" y="58" width="4" height="2" fill="#D4FFF7"/>
<rect id="Rectangle 34624362" x="67" y="5" width="122" height="2" fill="url(#paint6_linear_404_2269)"/>
<rect id="Rectangle 34624364" x="6" y="58" width="49" height="2" fill="url(#paint7_linear_404_2269)"/>
</g>
<defs>
<filter id="filter0_d_404_2269" x="55" y="1" width="144" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2269"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2269" result="shape"/>
</filter>
<filter id="filter1_d_404_2269" x="55" y="26" width="144" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2269"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2269" result="shape"/>
</filter>
<filter id="filter2_di_404_2269" x="2" y="9" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172199 0 0 0 0 0.525895 0 0 0 0 0.716346 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2269"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2269" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0330014 0 0 0 0 0.26663 0 0 0 0 0.411859 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_404_2269"/>
</filter>
<filter id="filter3_d_404_2269" x="17" y="46" width="22" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_404_2269"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.56912 0 0 0 0 0.88627 0 0 0 0 0.991987 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2269"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2269" result="shape"/>
</filter>
<filter id="filter4_d_404_2269" x="56" y="0" width="14" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_404_2269"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.88 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_404_2269"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_404_2269" result="shape"/>
</filter>
<linearGradient id="paint0_linear_404_2269" x1="132" y1="11" x2="0" y2="11" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC" stop-opacity="0.13"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint1_linear_404_2269" x1="132" y1="15" x2="0" y2="15" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC" stop-opacity="0.13"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint2_linear_404_2269" x1="102" y1="58.5001" x2="116.813" y2="19.426" gradientUnits="userSpaceOnUse">
<stop stop-color="#6B96CC"/>
<stop offset="1" stop-color="#95BEF1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_404_2269" x1="20" y1="40" x2="20" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#107BAC"/>
<stop offset="1" stop-color="#2196CC"/>
</linearGradient>
<linearGradient id="paint4_linear_404_2269" x1="20" y1="-4.21053" x2="20" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="#E1F7FF"/>
<stop offset="1" stop-color="#D7ECFF"/>
</linearGradient>
<linearGradient id="paint5_linear_404_2269" x1="28.0006" y1="23" x2="28.0006" y2="42.9997" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#B9E7FF"/>
</linearGradient>
<linearGradient id="paint6_linear_404_2269" x1="67" y1="7" x2="189" y2="7" gradientUnits="userSpaceOnUse">
<stop stop-color="#D7E9FF"/>
<stop offset="1" stop-color="#254874" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_404_2269" x1="6" y1="60" x2="55" y2="60" gradientUnits="userSpaceOnUse">
<stop stop-color="#2A79E7"/>
<stop offset="1" stop-color="#73ACF5" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
