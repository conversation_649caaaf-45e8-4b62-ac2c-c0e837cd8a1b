<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import ContractInvestment from './components/ContractInvestment.vue';
import SectionSettlementStatistics from './components/SectionSettlementStatistics.vue';
import StatisticsChartCard from './components/StatisticsChartCard.vue';
import { contractFileStatistics, tenderFileStatistics } from './api';

import img5 from './img/img5.svg'
import img6 from './img/img6.svg'
import img7 from './img/img7.svg'
import img8 from './img/img8.svg'

const contractApiData = ref<any>(null);

// --- Data for Statistics Cards ---
const contractCardData = reactive({
  stats: [
    { label: '主合同总数', value: 0, unit: '个', icon: img5 },
    { label: '分包合同总数', value: 0, unit: '个', icon: img6 },
  ],
  buttons: [
    { text: '主合同数量分布统计', type: 'main' },
    { text: '分包合同数量分布统计', type: 'sub' },
  ],
  chartData: [] as Array<{ name: string; value: number; color: string }>,
});

const biddingCardData = reactive({
  stats: [
    { label: '累计文件', value: 0, unit: '个', icon: img7 },
    { label: '流转中', value: 0, unit: '个', icon: img8 },
  ],
  buttons: [
    { text: '招标文件状态统计', type: 'status' }, // Only one button, so no switching needed
  ],
  chartData: [] as Array<{ name: string; value: number; color: string }>,
});

const generateColors = (count: number) => {
  const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'];
  return colors.slice(0, count);
};

const handleContractChartSwitch = (type: 'main' | 'sub') => {
  if (!contractApiData.value) return;

  const dataMap = type === 'main'
    ? contractApiData.value.investContractCountMap
    : contractApiData.value.investSubContractCountMap;

  const items = Object.entries(dataMap || {}).map(([key, value]) => ({
    name: key,
    value: (value as number) || 0,
  }));

  const colors = generateColors(items.length);
  const total = items.reduce((sum, item) => sum + item.value, 0);

  if (total === 0) {
    contractCardData.chartData = items.map((item, index) => ({
      name: item.name,
      value: 0,
      color: colors[index],
    }));
  } else {
    contractCardData.chartData = items.map((item, index) => ({
      name: item.name,
      value: parseFloat(((item.value / total) * 100).toFixed(2)),
      color: colors[index],
    }));
  }
};

onMounted(async () => {
  try {
    // Fetch and process contract data
    const contractRes = await contractFileStatistics();
    if (contractRes.data) {
      contractApiData.value = contractRes.data;
      contractCardData.stats[0].value = contractRes.data.investContractTotal || 0;
      contractCardData.stats[1].value = contractRes.data.investSubContractTotal || 0;
      handleContractChartSwitch('main'); // Set initial chart to 'main'
    }

    // Fetch and process tender data
    const tenderRes = await tenderFileStatistics();
    if (tenderRes.data) {
      biddingCardData.stats[0].value = tenderRes.data.total || 0;
      biddingCardData.stats[1].value = tenderRes.data.countMap?.['流转中'] || 0;
      const tenderMap = tenderRes.data.countMap || {};
      const tenderItems = Object.entries(tenderMap).map(([key, value]) => ({
        name: key,
        value: (value as number) || 0,
      }));

      const tenderColors = generateColors(tenderItems.length);
      const tenderTotal = tenderItems.reduce((sum, item) => sum + item.value, 0);

      if (tenderTotal === 0) {
        biddingCardData.chartData = tenderItems.map((item, index) => ({
          name: item.name,
          value: 0,
          color: tenderColors[index],
        }));
      } else {
        biddingCardData.chartData = tenderItems.map((item, index) => ({
          name: item.name,
          value: parseFloat(((item.value / tenderTotal) * 100).toFixed(2)),
          color: tenderColors[index],
        }));
      }
    }
  } catch (error) {
    console.error('Failed to fetch statistics data:', error);
  }
});
</script>

<template>
  <PageLayout>
    <template #left>
      <Card title="合同投资金额" style="height: 55%;">
        <ContractInvestment />
      </Card>
      <Card title="标段结算统计" style="height: 45%;">
        <SectionSettlementStatistics />
      </Card>
    </template>
    
    <template #right>
      <Card title="合同文件统计" style="height: 50%;">
        <StatisticsChartCard
          :stats="contractCardData.stats"
          :buttons="contractCardData.buttons"
          :chartData="contractCardData.chartData"
          @switch-chart="handleContractChartSwitch"
        />
      </Card>
      <Card title="招标文件状态统计" style="height: 50%;">
        <StatisticsChartCard 
          :stats="biddingCardData.stats" 
          :buttons="biddingCardData.buttons" 
          :chartData="biddingCardData.chartData" 
        />
      </Card>
    </template>
  </PageLayout>
</template>

<style lang="scss" scoped>
</style>
