import request from '~/router/request'
import { AxiosPromise } from 'axios'
let SERVICE_PREFIX = import.meta.env.CVE_VUE_APP_BASE_API_SERVICENAME

/**
 * 产值完成情况
 * @param
 * @returns
 */
export function outputValueCompleted(): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/big-screen/progress/finished`,
    method: 'get',
  })
}

/**
 * 未完成计划状态
 * @param
 * @returns
 */
export function uncompletedPlanStatus(): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/big-screen/progress/unfinished`,
    method: 'get',
  })
}

/**
 * 进度计划填报查询
 * @param
 * @returns
 */
export function progressPlanFillQuery(): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/big-screen/progress/warning-condition`,
    method: 'get',
  })
}

/**
 * 里程碑统计
 * @param
 * @returns
 */
export function milestoneStatistics(data: any): AxiosPromise<any> {
  return request({
    url: `${SERVICE_PREFIX}/progress-milepost/getPageByAll`,
    method: 'get',
    params: data,
  })
}