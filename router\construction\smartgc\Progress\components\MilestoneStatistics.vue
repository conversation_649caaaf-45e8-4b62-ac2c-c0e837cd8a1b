<template>
  <div v-loading="loading" class="ProgressMilestone">
    <div class="content">
      <div class="Timeline" v-for="(milestones, year) in tableData" :key="year">
        <div class="year">{{ year }}年</div>
        <div
          v-for="(item, index) in milestones"
          :key="item.id"
          class="node"
          :id="isTodayNode(year, index) ? 'today-node' : undefined"
        >
          <div class="node_info" :class="getStatusClass(item)">
            <div class="date">
              <div v-if="isTodayNode(year, index)" class="today-label">今日</div>
              <span v-else>{{ dayjs(item.planDate).format("YYYY-MM-DD") }}</span>
            </div>
            <div class="lineBox">
              <div class="node-icon"></div>
              <div class="line"></div>
            </div>
            <div class="detail">
              <div v-if="item.isTodayMarker" class="today-horizontal-line"></div>
              <div v-else>
                <div class="milepostName">
                  <el-tooltip effect="dark" :content="item.milepostName" placement="top">
                    <span>{{ item.milepostName }}</span>
                  </el-tooltip>
                </div>
                <!-- <div class="sectionName" v-if="item.sectionName">
                  {{ item.sectionName }}
                </div> -->
                <div class="state-date">
                  <span :class="`state ${getMilestoneStatus(item).class}`">
                    <span class="icon">{{ getMilestoneStatus(item).icon }}</span>
                    {{ getMilestoneStatus(item).text }}
                  </span>
                  <span class="date" v-if="item.realDate">
                    {{ dayjs(item.realDate).format('YYYY-MM-DD') }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-if="!loading && Object.keys(tableData).length === 0" description="暂无里程碑数据" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { milestoneStatistics } from '../api';
import dayjs from 'dayjs';
import { getDict } from '~/utils/app';

interface Milestone {
  id: string;
  planDate: string;
  realDate?: string; // 实际完成日期
  processState: number;
  milepostName: string;
  sectionId: string;
  sectionName?: string;
  isTodayMarker?: boolean;
}

const loading = ref(false);
const tableData = ref<Record<string, Milestone[]>>({});
const showTodayInfo = ref<{ year: string; index: number } | null>(null);

const isTodayNode = (year: string, index: number) => {
  return showTodayInfo.value?.year === year && showTodayInfo.value?.index === index;
};

const groupByYear = (data: Milestone[]): Record<string, Milestone[]> => {
  return data.reduce((acc, item) => {
    if (!item.planDate) return acc;
    const date = new Date(item.planDate);
    if (isNaN(date.getTime())) return acc;
    const year = date.getFullYear().toString();
    if (!acc[year]) {
      acc[year] = [];
    }
    acc[year].push(item);
    return acc;
  }, {} as Record<string, Milestone[]>);
};

const getStatusClass = (item: Milestone) => {
  if (dayjs(item.planDate).isSame(dayjs(), 'day')) {
    return 'status-today';
  }
  if (item.processState === 1 || item.processState === 3) {
    return 'status-completed';
  }
  if (dayjs(item.planDate).isBefore(dayjs(), 'day')) {
    return 'status-overdue';
  }
  return 'status-not-completed';
};

// 获取里程碑状态文本和图标
const getMilestoneStatus = (item: Milestone) => {
  if (item.realDate) {
    return {
      text: '已完成',
      icon: '✓',
      class: 'state-completed'
    };
  } else if (dayjs().isBefore(dayjs(item.planDate))) {
    const remainingDays = dayjs(item.planDate).diff(dayjs(), 'day');
    return {
      text: `剩余${remainingDays}天`,
      icon: '⏰',
      class: 'state-remaining'
    };
  } else if (dayjs().isAfter(dayjs(item.planDate))) {
    const overdueDays = dayjs().diff(dayjs(item.planDate), 'day');
    return {
      text: `超期${overdueDays}天`,
      icon: '⚠',
      class: 'state-overdue'
    };
  } else {
    return {
      text: '今日到期',
      icon: '⏰',
      class: 'state-today'
    };
  }
};

const fetchData = async () => {
  loading.value = true;
  showTodayInfo.value = null;
  try {
    // 1. 获取标段字典
    const dictRes = await getDict({code: 'project_section'});
    const sectionMap = new Map<string, string>();
    if (dictRes.data && dictRes.data.project_section) {
      dictRes.data.project_section.forEach((item: { code: string; 'zh-CN': string }) => {
        sectionMap.set(item.code, item['zh-CN']);
      });
    }

    // 2. 获取里程碑数据
    const res = await milestoneStatistics({
      page: 1,
      size: 999,
      ascCloumn: 'planDate',
    });

    let data: Milestone[] = res.data.records || [];

    // 3. 映射标段名称
    data.forEach(item => {
      if (item.sectionId && sectionMap.has(item.sectionId)) {
        item.sectionName = sectionMap.get(item.sectionId);
      }
    });

    // 4. 处理“今天”标记
    const todayExists = data.some(item => dayjs(item.planDate).isSame(dayjs(), 'day'));

    if (!todayExists) {
      const todayMarker: Milestone = {
        id: 'today-marker',
        planDate: dayjs().format('YYYY-MM-DD'),
        processState: -1,
        milepostName: '',
        sectionId: '',
        isTodayMarker: true,
      };

      const insertionIndex = data.findIndex(item => dayjs(item.planDate).isAfter(dayjs(), 'day'));
      if (insertionIndex === -1) {
        data.push(todayMarker);
      } else {
        data.splice(insertionIndex, 0, todayMarker);
      }
    }

    // 5. 分组并更新UI
    tableData.value = groupByYear(data);

    // 6. 定位并滚动到“今天”
    const todayItem = data.find(item => dayjs(item.planDate).isSame(dayjs(), 'day'));
    if (todayItem) {
      const year = dayjs(todayItem.planDate).format('YYYY');
      const yearData = tableData.value[year];
      if (yearData) {
        const indexInYear = yearData.findIndex(d => d.id === todayItem.id);
        if (indexInYear !== -1) {
          showTodayInfo.value = { year, index: indexInYear };
        }
      }
    }

    if (showTodayInfo.value) {
      setTimeout(() => {
        const todayElement = document.getElementById('today-node');
        if (todayElement) {
          todayElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 500);
    }

  } catch (error) {
    console.error('Failed to fetch milestone statistics:', error);
    tableData.value = {};
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
.ProgressMilestone {
  height: 100%;
  width: 100%;

  .content {
    height: 100%;
    overflow: auto;
    padding: 10px;
    box-sizing: border-box;

    .Timeline {
      &:not(:last-child) {
        margin-bottom: 24px;
        border-bottom: 2px solid #B5FDFF;
        padding-bottom: 24px;
      }

      .year {
        font-weight: normal;
        font-size: 20px;
        color: #60E9ED;
        line-height: 24px;
        margin-bottom: 24px;
      }

      .node {
        position: relative;

        .node_info {
          height: 70px;
          display: flex;
          gap: 12px;
          margin: 0 12px;
          position: relative;

          .date {
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            gap: 8px;
            font-weight: normal;
            font-size: 16px;
            color: #ffffff;
            min-width: 130px;
            line-height: 24px;

            .today-label {
              position: relative;
              width: 46px;
              height: 24px;
              line-height: 24px;
              padding-left: 5px;
              background-image: url("~/router/construction/smartgc/Progress/img/img5.svg");
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }

          .lineBox {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 5px;

            .node-icon {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              z-index: 1;
            }

            .line {
              flex-grow: 1;
              width: 1px;
              border-left: 1px dashed #96b5d0;
              border-image: repeating-linear-gradient(
                to bottom,
                #96b5d0 0,
                #96b5d0 6px, 
                transparent 6px,
                transparent 12px 
              );
              border-image-slice: 1;
            }
          }

          .detail {
            flex: 1;
            padding-bottom: 23px;
            overflow: hidden;

            .milepostName {
              font-weight: normal;
              font-size: 14px;
              line-height: 24px;
              margin-bottom: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .sectionName {
              margin-top: 8px;
              font-weight: normal;
              font-size: 14px;
              line-height: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .today-horizontal-line {
                width: 100%;
                height: 1px;
                border-top: 1px dashed #60E9ED;
                margin-top: 11px;
                border-image: repeating-linear-gradient(
                  to right,
                  #60E9ED 0,
                  #60E9ED 6px,
                  transparent 6px,
                  transparent 12px
                );
                border-image-slice: 1;
            }

            .state-date {
              margin-top: 8px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              // gap: 8px;

              .state {
                display: inline-flex;
                align-items: center;
                height: 24px;
                padding: 0 8px;
                border-radius: 2px;
                font-size: 12px;
                line-height: 24px;
                border: 1px solid;

                .icon {
                  font-size: 12px;
                  margin-right: 4px;
                  display: inline-block;
                }

                &.state-completed {
                  background: #e2ffe9;
                  border-color: #00b42a;
                  color: #00b42a;
                }

                &.state-remaining {
                  background: #ebf6ff;
                  border-color: #1d90ef;
                  color: #1d90ef;
                }

                &.state-overdue {
                  background: #ffece8;
                  border-color: #f53f3f;
                  color: #f53f3f;
                }

                &.state-today {
                  background: #fff7e6;
                  border-color: #ff7d00;
                  color: #ff7d00;
                }
              }

              .date {
                font-size: 12px;
                color: #00b42a;
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }
}

.status-completed {
  .node-icon {
    background: rgba(122, 239, 255, 0.32);
  }
  .milepostName {
    color: #60E9ED !important;
  }
}
.status-overdue {
  .node-icon {
    background-color: #f53f3f;
  }
  .milepostName {
    color: #f53f3f !important;
  }
}
.status-not-completed {
  .node-icon {
    background: #B7B7B7;
  }
  .milepostName {
    color: #666666 !important;
  }
}
.status-today {
  .node-icon {
    background-color: rgba(122, 239, 255, 0.32);
  }
  .milepostName {
    color: #fff;
  }
}
</style>