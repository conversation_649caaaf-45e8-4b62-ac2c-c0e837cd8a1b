<!-- 产值完成情况 -->
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, reactive } from 'vue';
import * as echarts from 'echarts';
import { ECharts } from 'echarts';
import { outputValueCompleted, uncompletedPlanStatus } from '../api';

// 用于存储API返回的数据
const summaryData = reactive({
  accumulated: 0,
  contract: 0,
});

const barData = reactive<{ name: string; value: number }[]>([]);

const gaugeChartContainer = ref<HTMLDivElement | null>(null);
const barChartContainer = ref<HTMLDivElement | null>(null);

let gaugeChart: ECharts | null = null;
let barChart: ECharts | null = null;

const initCharts = () => {
  // 环形进度图
  if (gaugeChartContainer.value) {
    const percentage = summaryData.contract > 0 ? (summaryData.accumulated / summaryData.contract) : 0;
    gaugeChart = echarts.init(gaugeChartContainer.value);
    const gaugeOption = {
      tooltip: {
        trigger: 'item',
        formatter: (params: { value: number }) => {
          if (typeof params.value === 'number') {
            return `${params.value.toFixed(2)}%`;
          }
          return '';
        },
      },
      series: [
        // {
        //   type: 'gauge',
        //   radius: '80%',
        //   center: ['50%', '50%'],
        //   startAngle: 90,
        //   endAngle: -270,
        //   pointer: { show: false },
        //   progress: {
        //     show: true,
        //     overlap: false,
        //     roundCap: false,
        //     clip: false,
        //     itemStyle: {
        //       color: '#76e7ec' // 累计产值
        //     }
        //   },
        //   axisLine: {
        //     lineStyle: {
        //       width: 8,
        //       color: [[1, '#4668e4']] // 合同产值
        //     }
        //   },
        //   splitLine: { show: false },
        //   axisTick: { show: false },
        //   axisLabel: { show: false },
        //   data: [
        //     {
        //       value: percentage * 100,
        //       detail: { show: false },
        //     },
        //   ],
        //   detail: { show: false },
        // },
        {
          type: 'pie',
          radius: ['65%', '85%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          label: { show: false },
          emphasis: { label: { show: false } },
          labelLine: { show: false },
          data: [
            { 
              value: summaryData.accumulated, 
              name: '累计产值',
              itemStyle: { color: '#76e7ec' }
            },
            { 
              value: summaryData.contract - summaryData.accumulated, 
              name: '剩余合同产值',
              itemStyle: { color: '#4668e4' }
            }
          ]
        }
      ],
    };
    gaugeChart.setOption(gaugeOption);
  }


  // 水平条形图
  if (barChartContainer.value) {
    barChart = echarts.init(barChartContainer.value);
    const barOption = {
      grid: {
        left: '0%',
        right: '8%', // 留出空间给数值
        bottom: '0%',
        top: '0%',
        containLabel: true,
      },
      xAxis: {
        show: false, // 不显示x轴
        type: 'value',
      },
      yAxis: {
        type: 'category',
        data: barData.map(item => item.name).reverse(), // 反转数据，使一标段在最上面
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: { show: false },
        axisLabel: {
          color: '#A2C2D4', // 字体颜色
          fontSize: 14,
        },
      },
      series: [
        {
          type: 'bar',
          data: barData.map(item => item.value).reverse(), // 反转数据
          barWidth: 16, // 调整宽度
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(3,87,102,0.51)' },
              { offset: 1, color: '#21D3F3' }
            ])
          },
          label: {
            show: true,
            position: 'right',
            color: '#A2C2D4', // 数值颜色
            fontSize: 14,
            offset: [5, 0] // 偏移
          },
        },
      ],
    };
    barChart.setOption(barOption);
  }
};

const fetchData = async () => {
  try {
    const [summaryRes, barRes] = await Promise.all([
      outputValueCompleted(),
      uncompletedPlanStatus()
    ]);

    if (summaryRes.data && summaryRes.data['总合同']) {
      const totalContract = summaryRes.data['总合同'];
      summaryData.accumulated = parseFloat((parseFloat(totalContract.realTotal) / 100000000).toFixed(2));
      summaryData.contract = parseFloat((parseFloat(totalContract.planTotal) / 100000000).toFixed(2));
    }

    if (barRes.data) {
      const rawBarData = barRes.data;
      const processedBarData = Object.keys(rawBarData).map(key => {
        const item = rawBarData[key];
        const value = (item['正常'] || 0) + (item['滞后'] || 0);
        return { name: key, value: value };
      }).filter(item => item.name !== '信息化标');
      
      barData.splice(0, barData.length, ...processedBarData);
    }
  } catch (error) {
    console.error('获取产值完成情况数据失败:', error);
  } finally {
    initCharts();
  }
};

onMounted(() => {
  fetchData();
});

onBeforeUnmount(() => {
  gaugeChart?.dispose();
  barChart?.dispose();
});
</script>

<template>
  <div class="output-value-container">
    <div class="top-section">
      <div class="chart-wrapper">
        <div ref="gaugeChartContainer" class="gauge-chart"></div>
      </div>
      <div class="info-wrapper">
        <div class="info-item accumulated">
          <span class="label">累计产值</span>
          <span class="value">{{ summaryData.accumulated }}</span>
          <span class="unit">亿</span>
        </div>
        <div class="info-item contract">
          <span class="label">合同产值</span>
          <span class="value">{{ summaryData.contract }}</span>
          <span class="unit">亿</span>
        </div>
      </div>
    </div>
    <div ref="barChartContainer" class="bar-chart"></div>
  </div>
</template>

<style lang="scss" scoped>
.output-value-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .top-section {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .chart-wrapper {
      width: 162px;
      height: 162px;
      margin-right: 16px;
      position: relative;
      background-image: url('../img/img1.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      .gauge-chart {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    .info-wrapper {
      flex-grow: 1;
      width: 200px;
      height: 126px;
      padding-left: 24px;
      background-image: url('../img/img2.svg');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .info-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        position: relative;
        padding-left: 22px;
        color: #fff;

        &.accumulated {
          margin-bottom: 24px;
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 18px;
            background: url('../img/img3.svg') no-repeat center center;
            background-size: contain;
          }
        }

        &.contract {
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 18px;
            background: url('../img/img4.svg') no-repeat center center;
            background-size: contain;
          }
        }

        .label {
          min-width: 68px;
          margin-right: 24px;
        }
        .value {
          font-size: 20px;
          font-weight: bold;
          margin-right: 3px;
        }
        .unit {
          color: #BCC5CE;
          font-size: 12px;
        }
      }
    }
  }

  .bar-chart {
    flex-grow: 1;
    width: 100%;
  }
}
</style>
