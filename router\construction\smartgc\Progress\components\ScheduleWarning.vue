<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { progressPlanFillQuery } from '../api';
import CommonDialog from '~/router/components/CommonDialog/index.vue';
import dayjs from 'dayjs'

// 预警数据
const warnings = reactive<{ lateDays: string; name: string; id: string; }[]>([]);

// 弹窗控制
const dialogVisible = ref(false);
// 当前选中的预警项
const currentWarning = ref<any>(null);

const fetchData = async () => {
  try {
    const res = await progressPlanFillQuery();
    if (res.data && Array.isArray(res.data)) {
      const processedData = res.data.map(item => ({
        id: item.id,
        ...item,
        belongYear: item.belongYear ? dayjs(item.belongYear).format('YYYY') : '--',
        planStartDate: item.planStartDate ? dayjs(item.planStartDate).format('YYYY-MM-DD') : '--',
        planEndDate: item.planEndDate ? dayjs(item.planEndDate).format('YYYY-MM-DD') : '--',
        realStartDate: item.realStartDate ? dayjs(item.realStartDate).format('YYYY-MM-DD') : '--',
        realEndDate: item.realEndDate ? dayjs(item.realEndDate).format('YYYY-MM-DD') : '--'
      }));
      warnings.splice(0, warnings.length, ...processedData);
    }
  } catch (error) {
    console.error('获取进度预警数据失败:', error);
  }
};

// 点击预警项，打开弹窗
const handleWarningClick = (warning) => {
  currentWarning.value = { ...warning };
  dialogVisible.value = true;
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="schedule-warning-container">
    <ul>
      <li v-for="(warning, index) in warnings" :key="index" class="warning-item" @click="handleWarningClick(warning)">
        <span class="lateDays-status">已滞后{{ warning.lateDays }}天</span>
        <div class="description-wrapper">
          <el-tooltip
            :content="warning.name"
            placement="top"
            effect="dark"
          >
            <span class="description">{{ warning.name }}</span>
          </el-tooltip>
        </div>
      </li>
    </ul>

    <CommonDialog 
      v-model="dialogVisible" 
      title="进度预警详情"
      width="650px">
      <div class="warning-detail" v-if="currentWarning">
        <div class="detail-row">
          <div class="detail-label">所属标段</div>
          <div class="detail-value">{{ currentWarning.sectionId }}</div>
          <div class="detail-label">所属年份</div>
          <div class="detail-value">{{ currentWarning.belongYear || '--' }}</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">任务名称</div>
          <div class="detail-value">{{ currentWarning.name || '--' }}</div>
          <div class="detail-label">任务编号</div>
          <div class="detail-value">{{ currentWarning.code || '--' }}</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">计划开始时间</div>
          <div class="detail-value">{{ currentWarning.planStartDate }}</div>
          <div class="detail-label">计划完成时间</div>
          <div class="detail-value">{{ currentWarning.planEndDate }}</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">实际开始时间</div>
          <div class="detail-value">{{ currentWarning.realStartDate }}</div>
          <div class="detail-label">实际完成时间</div>
          <div class="detail-value">{{ currentWarning.realEndDate }}</div>
        </div>
      </div>
    </CommonDialog>
  </div>
</template>

<style lang="scss" scoped>
.schedule-warning-container {
  width: 100%;
  height: 100%;

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .warning-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    padding: 4px;
    &:hover {
      background-color: rgba(0, 123, 255, 0.1); 
    }

    .lateDays-status {
      color: #FB2E27;
      font-weight: bold;
      flex-shrink: 0;
      width: 120px;
      margin-right: 16px;
    }

    .description-wrapper {
      flex-grow: 1;
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .description {
      color: #FEFFFF;
    }
  }
}

// 弹窗内容样式
.warning-detail {
  // padding: 16px 0;
  color: #FEFFFF;
  
  .detail-row {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    &:last-child {
      margin-bottom: 0;
    }
    .detail-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #A1CEE3;
    }
    .detail-value {
      width: 180px;
      margin-right: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #ffffff;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
