<!--建设总览 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
} from 'vue'
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import PreviewDialog from '~/router/components/PreviewDialog/index.vue'
import OutputValueCompleted from './components/OutputValueCompleted.vue'
import ScheduleWarning from './components/ScheduleWarning.vue'
import MilestoneStatistics from './components/MilestoneStatistics.vue'

const previewDialogRef = ref();

onMounted(() => {

});

</script>

<template>
  <PageLayout>
    <template #left>
      <Card title="产值完成情况统计" style="height: 55%;">
        <OutputValueCompleted/>
      </Card>
      <Card title="进度计划预警情况" style="height: 45%;">
        <ScheduleWarning />
      </Card>
    </template>
    
    <template #right>
      <Card title="里程碑统计" style="height: 100%;">
        <MilestoneStatistics />
      </Card>
    </template>
    <PreviewDialog ref="previewDialogRef" />
    </PageLayout>
</template>

<style lang="scss" scoped>


</style>
