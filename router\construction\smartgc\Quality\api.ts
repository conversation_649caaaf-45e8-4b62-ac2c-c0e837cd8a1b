import request from '~/router/request'
import { AxiosPromise } from 'axios'

let SERVICE_PREFIX_TCS = import.meta.env.CVE_VUE_APP_BASE_TCS_SERVICENAME
let SERVICE_PREFIX_API = import.meta.env.CVE_VUE_APP_BASE_API_SERVICENAME
// let PREFIX = '/local-test'
// let baseURL = '/'
let PREFIX = '/cybereng-quality'


/**
 * @description 获取质量整改page
 * @param params 
 * @returns
 */
export function postPage(params: any) {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/qualityInspection/correction/postPage`,
    method: 'post',
    params,
  })
}
/**
 * @description 工程部位
 * @param params 
 * @returns
 */
export function nodeTree(params: any) {
  return request({
    url: `${SERVICE_PREFIX_API}/wbs/node/tree`,
    method: 'get',
    params,
  })
}
/**
 * @description 获取流程审批记录
 * @param params 
 * @returns
*/
export function flowHistory(params: any) {
  return request({
    url: `/sys-bpm/process/history`,
    method: 'get',
    params,
  })
}
/**
 * @description 质量验评信息-统计
 * @param params 
 * @returns
 */
export function evaluation_statistics() {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/evaluation/statistics`,
    method: 'get',
  })
}
/**
 * @description 质量验评信息-标段统计
 * @param params 
 * @returns
 */
export function evaluation_statisticsBySection(params: any) {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/evaluation/statisticsBySection`,
    method: 'get', params
  })
}
/**
 * @description 质量问题
 * @param params 
 * @returns
 */
export function correction_statistics() {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/inspection/correction/statistics`,
    method: 'get',
  })
}
/**
 * @description 质量问题-标段统计
 * @param params 
 * @returns
 */
export function correction_statisticsBySection(params: any) {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/inspection/correction/statisticsBySection`,
    method: 'get', params
  })
}
/**
 * @description 质量检查-统计
 * @param params 
 * @returns
 */
export function inspection_statistics() {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/inspection/statistics`,
    method: 'get',
  })
}
/**
 * @description 质量检查-标段统计
 * @param params 
 * @returns
 */
export function inspection_statisticsBySection(params: any) {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/inspection/statisticsBySection`,
    method: 'get', params
  })
}
/**
 * @description 试验检测情况-统计
 * @param params 
 * @returns
 */
export function test_detection_statistics() {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/test-detection/statistics`,
    method: 'get',
  })
}
/**
 * @description 试验检测-标段统计
 * @param params 
 * @returns
 */
export function test_detection_statisticsBySection(params: any) {
  return request({
    url: `${SERVICE_PREFIX_TCS}/quality/bigscreen/test-detection/statisticsBySection`,
    method: 'get', params
  })
}