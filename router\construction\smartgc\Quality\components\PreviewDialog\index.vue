<script setup lang="ts">
import { ref } from 'vue'
/* 文件预览 */
import PreView from '../PreView/index.vue' // 文件预览

const props = defineProps({
  title: {
    type: String,
    default: '预览',
  },
  showFullscreen: {
    type: Boolean,
    default: false,
  },
})

const dialogData = ref<Record<string, any>>({
  previewVisible: false,
  currentFileToken: '',
})

function openPreView(fileToken: any): void {
  // 具体看一下 不能预览的文件给出提示
  dialogData.value.previewVisible = true
  dialogData.value.currentFileToken = fileToken
 
}
// 暴露方法给父组件
defineExpose({
  openPreView,
})
// 关闭预览弹框
function closePreview() {
  dialogData.value.previewVisible = false
  dialogData.value.currentFileToken = ''
}
</script>

<template>
  <div>
    <el-dialog
      :model-value.sync="dialogData.previewVisible"
      :width="1200"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :append-to-body="true"
      :show-fullscreen="props.showFullscreen"
      class="previewDialog"
      @close="closePreview"
    >
      <template #header="{ titleId, titleClass }">
        <div class="my-header lx-work-area-title">
          <div :id="titleId" :class="{ titleClass, 'title-content': true }">
            {{ props.title }}
          </div>
        </div>
      </template>
      <pre-view :file-token="dialogData.currentFileToken" class="pre-view-con" />
      <slot name="bottom"></slot>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.previewDialog {
  .lx-work-area-title {
    margin-top: 0px !important;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    // background-image: url('~/assets/images/constructorSecure/card-header.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: default;
  }
  .title-content {
    font-family: Source-Bold;
    font-weight: 700;
    font-size: 24px;
    color: #ffffff;
    line-height: 24px;
    letter-spacing: 1px;
    text-shadow: 0px 2px 2px #000;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding: 4px 35px 8px 32px;
  }
  .status-tab {
    color: #fff;
  }
}
::v-deep(.pre-dialog) {
  color: #fff;
  .lx-work-area-title {
    margin-top: 0px !important;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    // background-image: url('~/assets/images/constructorSecure/card-header.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: default;
  }
  &.is-fullscreen {
    display: flex;
    flex-direction: column;
    .fks-dialog__body {
      flex: 1;
      display: flex;
      flex-direction: column;
      .pre-view-con {
        flex: 1;
      }
    }
  }
}
.pre-view-con {
  height: calc(75vh - 96px);
}
</style>
