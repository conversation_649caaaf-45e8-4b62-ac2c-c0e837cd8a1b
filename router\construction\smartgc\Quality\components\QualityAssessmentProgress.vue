<!-- 质量考核进度 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import {
  test_detection_statistics,
  test_detection_statisticsBySection,
} from '../api'
import { getDict } from '~/utils/app'
const props = defineProps({
  layer: {
    type: String,
    default: 'project',
  },
  portalId: {
    type: String,
    default: '',
  },
})
// 图表容器
const chartContainer = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance: ECharts | null = null
const test_detection_statistics_data = ref({})
const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}
const fetchData = () => {
  if (props.layer === 'project') {
    test_detection_statistics()
      .then((res) => {
        // console.log(res.data, "test_detection_statistics");
        test_detection_statistics_data.value = res.data || {}
        initChart()
      })
      .catch(() => {
        test_detection_statistics_data.value = {}
        initChart()
      })
  } else {
    test_detection_statisticsBySection({ portalId: props.portalId })
      .then((res) => {
        // console.log(res.data, "test_detection_statisticsBySection");
        test_detection_statistics_data.value = res.data || {}
        initChart()
      })
      .catch(() => {
        test_detection_statistics_data.value = {}
        initChart()
      })
  }
}
const initChart = () => {
  let xAxisData
  if (props.layer == 'project') {
    xAxisData = Array.from(sectionMap.value.values())
  } else {
    xAxisData = Object.keys(test_detection_statistics_data.value)
  }
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      top: 'top',
      left: 'center',
      itemWidth: 14,
      itemHeight: 14,
      itemStyle: {
        color: 'inherit',
      },
      textStyle: {
        color: '#fff',
        fontSize: 16,
      },
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '2%',
      containLabel: true,
    },
    yAxis: [
      {
        type: 'value',
        name: '单位(个)',
        min: 0,
        max: 100,
        interval: 25,
        nameTextStyle: {
          color: '#E8E8E8',
          fontSize: 12,
          align: 'center',
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(221, 221, 221, 0.44)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        axisLabel: {
          formatter: '{value} %',
          color: '#fff',
          fontSize: 14,
        },
        axisTick: {
          show: false,
        },

        splitLine: {
          lineStyle: {
            color: 'rgba(221, 221, 221, 0.44)',
            type: 'dashed',
          },
        },
      },
    ],
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
        },
      },
    },
    series: [
      {
        name: '合格',
        type: 'bar',
        data: Object.values(test_detection_statistics_data.value).map(
          (x) => x.qualified
        ),
        stack: 'all',
        barWidth: 12,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(3, 171, 200, 0.29)' },
            { offset: 1, color: 'rgba(50, 209, 237, 1)' },
          ]),
        },
      },
      {
        name: '不合格',
        type: 'bar',
        stack: 'all',
        barWidth: 12,
        data: Object.values(test_detection_statistics_data.value).map(
          (x) => x.unqualified
        ),

        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(254, 165, 64, 0.40)' },
            { offset: 1, color: 'rgba(229, 161, 82, 1)' },
          ]),
        },
      },
      {
        name: '合格率',
        type: 'line',
        yAxisIndex: 1,
         tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          },
        },
        data: Object.values(test_detection_statistics_data.value).map(
          (x) => x.qualifiedPoint
        ),
        itemStyle: {
          color: '#4EF17C',
        },
      },
    ],
  }
  if (chartContainer.value) {
    echarts.getInstanceByDom(chartContainer.value)?.dispose()
    chartInstance = echarts.init(chartContainer.value) // 初始化实例
    chartInstance.setOption(option) // 设置配置项
  }
}

watchEffect(() => {})
watch(
  () => props.layer,
  () => {
    fetchData()
  }
)
// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 生命周期钩子
onMounted(async () => {
  await initDict()
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="QualityAssessmentProgress">
    <div ref="chartContainer" class="chartContainer"></div>
  </div>
</template>
<style lang="scss" scoped>
.QualityAssessmentProgress {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .chartContainer {
    height: 100%;
    width: 100%;
  }
}
</style>
