<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
  computed,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { evaluation_statistics, evaluation_statisticsBySection } from '../api'
import { getDict } from '~/utils/app'
const props = defineProps({
  layer: {
    type: String,
    default: 'project',
  },
  portalId: {
    type: String,
    default: '',
  },
})
// 图表容器
const chartContainer = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance: ECharts | null = null
const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}
const percent = computed(() => {
  return (value: string | number, total: string | number) => {
    if (!Number(total)) return 0
    else if (total <= value) return 100
    else return Math.ceil((Number(value) / Number(total)) * 100)
  }
})
watchEffect(() => {})
const initChart = () => {
  const option = {
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        center: ['50%', '80%'],
        radius: '150%',
        endAngle: 0,
        min: 0,
        max: evaluation_statistics_data.value.total || 0,
        width: '100%',
        splitNumber: 1,
        left: 0,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: '#1696C5' },
            { offset: 0.5, color: '#76CCE4' },
            { offset: 1, color: '#33B4CE' },
          ]),
          shadowColor: 'rgba(50, 164, 240, 0.25)',
          shadowBlur: 10,
          shadowOffsetX: 2,
          shadowOffsetY: 2,
        },
        progress: {
          show: true,
          roundCap: true,
          width: 12,
        },
        pointer: {
          show: false,
        },
        axisLine: {
          roundCap: true,
          lineStyle: {
            width: 12,
            color: [[1, 'rgba(255, 255, 255, 0.6)']],
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: 22,
        },
        data: [
          {
            value: evaluation_statistics_data.value.completed || 0,
            name: '已验评',
          },
        ],
        detail: {
          fontSize: 30,
          offsetCenter: [0, '-35%'],
          valueAnimation: true,
          color: '#fff',
        },
        title: {
          offsetCenter: [0, '-10%'],
          color: '#fff',
          fontSize: 16,
        },
      },
    ],
  }
  if (chartContainer.value) {
    echarts.getInstanceByDom(chartContainer.value)?.dispose()
    chartInstance = echarts.init(chartContainer.value) // 初始化实例
    chartInstance.setOption(option) // 设置配置项
  }
}
// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}
const evaluation_statistics_data = ref({ portalStatic: {}, unitStatic: {} })
const list = ref({})
const fetchData = () => {
  if (props.layer == 'project') {
    evaluation_statistics().then((res) => {
      // console.log(res.data, "evaluation_statistics");
      evaluation_statistics_data.value = res.data || {}
      list.value = res.data?.portalStatic || {}
      initChart()
    })
  } else {
    evaluation_statisticsBySection({ portalId: props.portalId }).then((res) => {
      // console.log(res.data, "evaluation_statisticsBySection");
      evaluation_statistics_data.value = res.data || {}
      list.value = res.data?.unitStatic || {}
      initChart()
    })
  }
}
// 生命周期钩子
onMounted(async () => {
  await initDict()
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="QualityEvaluationProgress">
    <div ref="chartContainer" class="chartContainer"></div>
    <div class="SectionBox">
      <div v-for="(value, key) in list" :key="key" v-show="key != '信息化标'">
        <span class="name">{{ key }}</span>
        <div>
          <div
            :style="{
              width: `${percent(value, evaluation_statistics_data.total)}%`,
            }"
          ></div>
          <img
            src="../img/光圈.png"
            :style="{
              left: `${percent(value, evaluation_statistics_data.total)}%`,
            }"
          />
        </div>
        <span class="data">{{ value }}</span>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.QualityEvaluationProgress {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .chartContainer {
    height: 40%;
  }
  .SectionBox {
    height: 60%;
    overflow: hidden;
    > div {
      //   margin-bottom: 30px;
      //   padding-right: 30px;
      padding: 28px 30px 0 0;
      display: flex;
      align-items: center;
      position: relative;
      > div {
        flex: 1;
        height: 10px;
        background: rgba(0, 86, 125, 0.75);
        border-radius: 5px 5px 5px 5px;
        position: relative;
        > div {
          height: 100%;
          position: absolute;
          background: linear-gradient(90deg, #2a83f0 0%, #57fde7 100%);
          box-shadow: 0px 2px 6px 0px rgba(10, 75, 110, 0.25);
          border-radius: 5px 5px 5px 5px;
          z-index: 1;
        }
        img {
          position: absolute;
          width: 22px;
          height: 22px;
          top: 50%;
          transform: translate(-50%, -50%);
          z-index: 2;
        }
      }
      .name {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        font-weight: normal;
        font-size: 14px;
        color: #cccaca;
        margin-right: 6px;
        width: 100px;
      }
      .data {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        font-weight: normal;
        font-size: 14px;
        color: #ffffff;
        position: absolute;
        // top: 0;
        transform: translateY(-100%);
        right: 30px;
      }
    }
  }
}
</style>
