<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watchEffect, watch, reactive } from "vue";
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { graphic } from "echarts";
import { usePortalStore } from "~/stores";
import { flowHistory } from "../api";
const props = defineProps({
  layer: {
    type: String,
    default: "project"
  },
  detailData: {
    type: Object,
    default: () => {}
  }
});
const flowHistoryList = ref([]);
const fetchData = () => {
  flowHistory({
    bizId: props.detailData.id,
    taskId: props.detailData.taskId
  }).then((res) => {
    flowHistoryList.value = res.data;
  });
};

watchEffect(() => {});

// 生命周期钩子
onMounted(() => {
  fetchData();
});
onBeforeUnmount(() => {});
</script>
<template>
  <div class="QualityFlowDetail">
    <div v-for="item in flowHistoryList" :key="item.id" class="flowItem">
      <div class="lineBox">
        <img src="../img/flow_completed.png" v-if="item.taskState == 1" />
        <img src="../img/flow_todo.png" v-else />
        <div class="line"></div>
      </div>
      <div class="flow">
        <div class="info">
          <span class="date">{{ item.createDate }}</span>
          <span :class="`state ${item.taskState == 1 ? 'state1' : 'state2'}`">{{
            item.taskState == 1 ? "通过" : "待办"
          }}</span>
        </div>
        <div class="name">{{ item.taskSubject }}</div>
        <div class="user">
          <span>
            处理人 <span>{{ item.assigneeName }}</span>
          </span>
          <span>
            处理意见 <span>{{ item.comment }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.QualityFlowDetail {
  width: 100%;
  height: 100%;
  overflow: auto;
  .flowItem {
    display: flex;
    gap: 22px;
    .lineBox {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      img {
        width: 18px;
        height: 18px;
      }
      .line {
        width: 1px;
        height: calc(100% - 18px);
        border: 1px dashed #71bef9;
      }
    }
    .flow {
      flex: 1;
      .info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .date {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 16px;
          color: #ffffff;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .state {
          display: inline-block;
          border-radius: 16px;
          padding: 3px 12px;
          font-size: 16px;
          text-align: center;
        }
        .state1 {
          background: rgba(3, 102, 143, 0.6);
          color: #32d1ed;
          border: 1px solid #32d1ed;
        }
        .state2 {
          background: rgba(114, 56, 2, 0.6);
          color: #f28b2a;
          border: 1px solid #f28b2a;
        }
      }
      .name {
        height: 36px;
        background: linear-gradient(90deg, #2196cc 0%, rgba(16, 123, 172, 0.35) 100%);
        border-radius: 3px 3px 3px 3px;
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        font-size: 18px;
        color: #ffffff;
        line-height: 36px;
        padding-left: 10px;
        margin-bottom: 19px;
      }
      .user {
        display: flex;
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        gap: 30px;
        span {
          font-size: 16px;
          color: #aaaaaa;
          margin-bottom: 36px;
          span {
            font-size: 18px;
            color: #ffffff;
            line-height: 22px;
            letter-spacing: 2px;
          }
        }
      }
    }
    &:last-child {
      .lineBox .line {
        border-color: transparent;
      }
    }
  }
}
</style>
