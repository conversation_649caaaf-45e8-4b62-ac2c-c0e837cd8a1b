<!-- 质量检查 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { inspection_statistics, inspection_statisticsBySection } from '../api'
const props = defineProps({
  layer: {
    type: String,
    default: 'project',
  },
  portalId: {
    type: String,
    default: '',
  },
  active_org_index: {
    type: Number,
    default: 0,
  },
})

// 图表容器
const chartContainer = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance: ECharts | null = null
const safe_quality_org = ref([
  '业主检查',
  '总包检查',
  '监理检查',
  '施工方检查',
  '其他检查',
])
watch(
  () => props.layer,
  () => {
    fetchData()
  }
)
const inspection_statistics_data = ref({})
const activeOrg = ref('')
const fetchData = () => {
  if (props.layer === 'project') {
    inspection_statistics().then((res) => {
      inspection_statistics_data.value = res.data || {}
      // console.log(res.data, "inspection_statistics");
      initChart()
    })
  } else {
    inspection_statisticsBySection({ portalId: props.portalId }).then((res) => {
      inspection_statistics_data.value = res.data || {}
      activeOrg.value = '业主检查'
      // console.log(res.data, "inspection_statisticsBySection");
    })
  }
}
const initChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      show: false,
      top: 'top',
      left: 'center',
      itemWidth: 14,
      itemHeight: 14,
      itemStyle: {
        color: 'inherit',
      },
      textStyle: {
        color: '#fff',
        fontSize: 16,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      minInterval: 1,
      startValue: 0,
      max: (value: any) => {
        return value.max < 10 ? 10 : null
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
          type: 'dashed',
        },
      },
    },
    yAxis: {
      type: 'category',
      data: Object.keys(inspection_statistics_data.value.countMap || {}),
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
        },
      },
    },
    series: [
      {
        type: 'bar',
        data: Object.values(inspection_statistics_data.value.countMap || {}),
        barWidth: 12,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgba(3, 87, 102, 0.51)' },
            { offset: 1, color: 'rgba(57, 208, 235, 1)' },
          ]),
        },
      },
    ],
  }
  if (chartContainer.value) {
    echarts.getInstanceByDom(chartContainer.value)?.dispose()
    chartInstance = echarts.init(chartContainer.value) // 初始化实例
    chartInstance.setOption(option) // 设置配置项
  }
}

watchEffect(() => {})

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 生命周期钩子
onMounted(() => {
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="QualityInspection">
    <div class="top">
      <div class="ljlj">
        <span>累计临检</span>
        <span
          >{{
            props.layer == 'project'
              ? inspection_statistics_data.total || 0
              : inspection_statistics_data[safe_quality_org[active_org_index]]
              ? inspection_statistics_data[safe_quality_org[active_org_index]]
                  .total
              : 0
          }}
          <span>次</span></span
        >
      </div>
      <div class="byjc" v-if="props.layer === 'section'">
        <span>本月检查</span>
        <span
          >{{
            props.layer == 'project'
              ? inspection_statistics_data.monthTotal || 0
              : inspection_statistics_data[safe_quality_org[active_org_index]]
              ? inspection_statistics_data[safe_quality_org[active_org_index]]
                  .monthTotal
              : 0
          }}
          <span>次</span></span
        >
      </div>
      <div class="ljfxwt" v-if="props.layer === 'section'">
        <span>累计发现问题</span>
        <span
          >{{
            props.layer == 'project'
              ? inspection_statistics_data.questionableTotal || 0
              : inspection_statistics_data[safe_quality_org[active_org_index]]
              ? inspection_statistics_data[safe_quality_org[active_org_index]]
                  .questionableTotal
              : 0
          }}
          <span>次</span></span
        >
      </div>
      <div class="wtfxpc">
        <span>问题发现频次</span>
        <span
          >{{
            props.layer == 'project'
              ? inspection_statistics_data.point || 0
              : inspection_statistics_data[safe_quality_org[active_org_index]]
              ? inspection_statistics_data[safe_quality_org[active_org_index]]
                  .point
              : 0
          }}
          <span>次</span></span
        >
      </div>
    </div>
    <div
      ref="chartContainer"
      v-if="props.layer === 'project'"
      class="chartContainer"
    ></div>
  </div>
</template>
<style lang="scss" scoped>
.QualityInspection {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .top {
    display: flex;
    gap: 50px 30px;
    flex-wrap: wrap;
    > div {
      width: calc(50% - 15px);
      display: flex;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      flex-direction: column;
      height: 60px;
      padding-left: 15%;
      justify-content: center;
      &.ljlj {
        background-image: url('../img/累计临检.png');
      }
      &.byjc {
        background-image: url('../img/本月检查.png');
      }
      &.ljfxwt {
        background-image: url('../img/累计发现问题.png');
      }
      &.wtfxpc {
        background-image: url('../img/问题发现频次.png');
      }
      > span {
        &:first-child {
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 14px;
          color: #c4e2ee;
        }
        &:last-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 20px;
          color: #e0fbff;
          span {
            font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 12px;
          }
        }
      }
    }
  }
  .chartContainer {
    margin-top: 16px;
    height: calc(100% - 60px - 16px);
  }
}
</style>
