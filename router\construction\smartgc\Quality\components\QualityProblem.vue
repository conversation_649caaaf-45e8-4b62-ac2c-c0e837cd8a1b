<!-- 质量问题 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
  computed,
} from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { correction_statistics, correction_statisticsBySection } from '../api'
const props = defineProps({
  layer: {
    type: String,
    default: 'project',
  },
  portalId: {
    type: String,
    default: '',
  },
})

// 图表容器
const chartContainer1 = ref<HTMLDivElement | null>(null)
const chartContainer2 = ref<HTMLDivElement | null>(null)
const chartContainer3 = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance1: ECharts | null = null
let chartInstance2: ECharts | null = null
let chartInstance3: ECharts | null = null
const colors = ref(['#27F9FF', '#2DB5FF', '#2EEB90', '#C5FF73', '#FAAE56'])
const project_section_color = ref([
  'rgba(238, 181, 82, 0.5)',
  'rgba(70, 239, 163, 0.5)',
  'rgba(138, 109, 224, 0.5)',
  'rgba(33, 211, 243, 0.5)',
  'rgba(45, 181, 255, 0.5)',
  'rgba(46, 235, 144, 0.5)',
])
const state1 = ref(['待审核', '待整改', '待审批', '待复核', '已完成'])
const state2 = ref(['临期', '逾期', '正常'])
/**
 * 获取面的参数方程
 * @param {*} startRatio 扇形起始位置比例
 * @param {*} endRatio 扇形结束位置比例
 * @param {*} k 辅助参数,控制饼图半径
 * @param {*} value 数值
 */
const getParametricEquation = (startRatio, endRatio, k, value) => {
  const startRadian = startRatio * Math.PI * 2
  const endRadian = endRatio * Math.PI * 2

  k = typeof k === 'number' && !isNaN(k) ? k : 1 / 3 //默认1/3

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x(u, v) {
      if (u < startRadian) {
        return Math.cos(startRadian) * (1 + Math.cos(v) * k)
      }
      if (u > endRadian) {
        return Math.cos(endRadian) * (1 + Math.cos(v) * k)
      }
      return Math.cos(u) * (1 + Math.cos(v) * k)
    },

    y(u, v) {
      if (u < startRadian) {
        return Math.sin(startRadian) * (1 + Math.cos(v) * k)
      }
      if (u > endRadian) {
        return Math.sin(endRadian) * (1 + Math.cos(v) * k)
      }
      return Math.sin(u) * (1 + Math.cos(v) * k)
    },

    z(u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * 100 * 0.1
      }
      // 扇形高度根据value值计算
      return Math.sin(v) > 0 ? 100 * 0.1 : -1
    },
  }
}

const options = computed(() => {
  //总数
  // let total = data.value.reduce((a, b) => a + b.value, 0);
  let total = 100
  //当前累加值
  let sumValue = 0
  //辅助参数,控制饼图半径，（0-1）范围内控制环形大小，值越小环形内半径越大
  let k = 0.2
  const potalsPointMap = correction_statistics_data.value.potalsPointMap || {}
  //series配置（每个扇形）
  let series = Object.keys(potalsPointMap).map((item, index) => {
    //当前扇形起始位置占饼图比例
    let startRatio = sumValue / total
    //值累加
    sumValue += Number(potalsPointMap[item])
    //当前扇形结束位置占饼图比例
    let endRatio = sumValue / total

    return {
      name: item ?? null,
      type: 'surface', //曲面图
      itemStyle: {
        color: project_section_color.value[index], //颜色
      },
      wireframe: {
        show: false, //不显示网格线
      },
      pieData: Number(potalsPointMap[item]), //数据
      //饼图状态
      pieStatus: {
        k, //辅助参数
        startRatio, //起始位置比例
        endRatio, //结束位置比例
        value: Number(potalsPointMap[item]), //数值
      },
      parametric: true, //参数曲面
      //曲面的参数方程
      parametricEquation: getParametricEquation(
        startRatio,
        endRatio,
        k,
        Number(potalsPointMap[item])
      ),
    }
  })

  //返回配置
  return {
    //提示框
    tooltip: {
      formatter: (params: any) => {
        if (
          params.seriesName !== 'mouseoutSeries' &&
          params.seriesName !== 'pie2d'
        ) {
          return `${
            params.seriesName
          }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
            params.color
          };"></span>${potalsPointMap[params.seriesName]}%`
        }
        return ''
      },
    },
    legend: {
      show: false,
      top: 'center',
      right: 'right',
      icon: 'circle',
      orient: 'vertical',
      formatter: (name: string) => {
        return `{a|${name}}{b|${Number(potalsPointMap[name])}%}`
      },
      itemStyle: {
        borderWidth: 1,
        borderColor: '#fff',
      },
      textStyle: {
        color: '#fff',
        rich: {
          a: {
            fontSize: 14,
            padding: [0, 15, 0, 0],
          },
          b: {
            fontSize: 18,
          },
        },
      },
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    //
    grid3D: {
      show: false, //不显示坐标系
      boxHeight: 2, //饼图高度
      // 用于鼠标的旋转，缩放等视角控制
      viewControl: {
        alpha: 25, //视角
        distance: 200, //距离，值越大饼图越小
        rotateSensitivity: 1, //禁止旋转
        zoomSensitivity: 0, //禁止缩放
        panSensitivity: 0, //禁止平移
        // rotateSensitivity: 0 //禁用鼠标控制旋转
        autoRotate: true, //禁止自动旋转
      },
    },
    series,
  }
})
const initChart = () => {
  // 图表配置
  if (chartContainer1.value && props.layer === 'project') {
    echarts.getInstanceByDom(chartContainer1.value)?.dispose()
    chartInstance1 = echarts.init(chartContainer1.value) // 初始化实例
    chartInstance1.setOption(options.value) // 设置配置项
  } else {
    const total2 = Object.values(
      correction_statistics_data.value.rectificationStatusMap
    ).reduce((acc, val) => acc + val, 0)
    const total3 = Object.values(
      correction_statistics_data.value.periodStatusMap
    ).reduce((acc, val) => acc + val, 0)
    const option2 = {
      tooltip: {
        trigger: 'item',
      },
      title: {
        text: total2,
        subtext: '质量问题',
        left: '30%',
        top: '45%',
        textAlign: 'center',
        textVerticalAlign: 'middle',
        textStyle: {
          color: '#fff',
          fontSize: 30,
        },
        subtextStyle: {
          color: '#fff',
          fontSize: 16,
        },
      },
      color: colors.value,
      legend: {
        top: 'center',
        icon: 'circle',
        right: 'right',
        orient: 'vertical',
        formatter: (name: string) => {
          return `{a|${name}}{b|${
            correction_statistics_data.value.rectificationStatusMap[name] || 0
          }}`
        },
        textStyle: {
          color: '#fff',
          rich: {
            a: {
              fontSize: 14,
              padding: [0, 15, 0, 0],
            },
            b: {
              fontSize: 18,
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['55%', '70%'],
          center: ['30%', '50%'],
          label: {
            show: false,
          },
          // padAngle: 5,
          labelLine: {
            show: false,
          },
          data: Object.keys(
            correction_statistics_data.value['rectificationStatusMap']
          ).map((x) => {
            return {
              value:
                correction_statistics_data.value['rectificationStatusMap'][x] ||
                0,
              name: x,
            }
          }),
        },
      ],
    }
    if (chartContainer2.value) {
      echarts.getInstanceByDom(chartContainer2.value)?.dispose()
      chartInstance2 = echarts.init(chartContainer2.value) // 初始化实例
      chartInstance2.setOption(option2) // 设置配置项
    }
    const option3 = {
      tooltip: {
        trigger: 'item',
      },
      title: {
        text: total3,
        subtext: '待整改问题',
        left: '30%',
        top: '45%',
        textAlign: 'center',
        textVerticalAlign: 'middle',
        textStyle: {
          color: '#fff',
          fontSize: 30,
        },
        subtextStyle: {
          color: '#fff',
          fontSize: 16,
        },
      },
      color: colors.value,
      legend: {
        top: 'center',
        right: 'right',
        icon: 'circle',
        orient: 'vertical',
        formatter: (name: string) => {
          return `{a|${name}}{b|${
            correction_statistics_data.value.periodStatusMap[name] || 0
          }}`
        },
        textStyle: {
          color: '#fff',
          rich: {
            a: {
              fontSize: 14,
              padding: [0, 15, 0, 0],
            },
            b: {
              fontSize: 18,
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['55%', '70%'],
          center: ['30%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          // padAngle: 5,
          data: Object.keys(
            correction_statistics_data.value['periodStatusMap']
          ).map((x) => {
            return {
              value:
                correction_statistics_data.value['periodStatusMap'][x] || 0,
              name: x,
            }
          }),
        },
      ],
    }
    if (chartContainer3.value) {
      echarts.getInstanceByDom(chartContainer3.value)?.dispose()
      chartInstance2 = echarts.init(chartContainer3.value) // 初始化实例
      chartInstance2.setOption(option3) // 设置配置项
    }
  }
}

watch(
  () => props.layer,
  () => {
    fetchData()
  }
)
const correction_statistics_data = ref({})
const fetchData = () => {
  if (props.layer == 'project') {
    correction_statistics().then((res) => {
      correction_statistics_data.value = res.data || {}
      // console.log(res.data, "correction_statistics");
      initChart()
    })
  } else {
    correction_statisticsBySection({ portalId: props.portalId }).then((res) => {
      correction_statistics_data.value = res.data || {}
      // console.log(res.data, "correction_statisticsBySection");
      initChart()
    })
  }
}
// 销毁图表
const destroyChart = () => {
  if (chartInstance1) {
    chartInstance1.dispose()
    chartInstance1 = null
  }
  if (chartInstance2) {
    chartInstance2.dispose()
    chartInstance2 = null
  }
  if (chartInstance3) {
    chartInstance3.dispose()
    chartInstance3 = null
  }
}

// 生命周期钩子
onMounted(() => {
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="QualityInspection">
    <div class="top" v-if="props.layer === 'project'">
      <div>
        <span>累计问题</span>
        <span>{{ correction_statistics_data.total || 0 }} <span>个</span></span>
      </div>
      <div>
        <span>待整改问题</span>
        <span
          >{{ correction_statistics_data.pendingTotal || 0 }}
          <span>个</span></span
        >
      </div>
    </div>
    <div class="chartContainer1" v-if="props.layer === 'project'">
      <div ref="chartContainer1" class="chart"></div>
      <div class="legend">
        <div
          v-for="(
            value, key, index
          ) in correction_statistics_data.potalsPointMap"
          :key="key"
          v-show="key != '信息化标'"
        >
          <span
            :style="{ backgroundColor: project_section_color[index] }"
          ></span>
          <span>{{ key }}</span>
          <span>{{ value }}%</span>
        </div>
      </div>
    </div>
    <div
      ref="chartContainer2"
      class="chartContainer2"
      v-if="props.layer === 'section'"
    ></div>
    <div
      ref="chartContainer3"
      class="chartContainer3"
      v-if="props.layer === 'section'"
    ></div>
  </div>
</template>
<style lang="scss" scoped>
.QualityInspection {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .top {
    display: flex;
    gap: 30px;
    margin-bottom: 16px;
    > div {
      display: flex;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      flex-direction: column;
      height: 60px;
      padding-left: 15%;
      width: 50%;
      justify-content: center;
      &:first-child {
        background-image: url('../img/累计问题.png');
      }
      &:last-child {
        background-image: url('../img/待整改问题.png');
      }
      > span {
        &:first-child {
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 14px;
          color: #c4e2ee;
        }
        &:last-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 20px;
          color: #e0fbff;
          span {
            font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 12px;
          }
        }
      }
    }
  }
  .chartContainer1,
  .chartContainer2,
  .chartContainer3 {
    flex: 1;
  }
  .chartContainer1 {
    // background-image: url("../img/echarts_bg.png");
    background-size: 80% 100%;
    background-position: center left;
    background-repeat: no-repeat;
    display: flex;
    .chart {
      width: 60%;
    }
    .legend {
      width: 40%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        span {
          &:first-child {
            width: 12px;
            height: 12px;
            border: 1px solid #ffffff;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
          }
          &:nth-child(2) {
            text-align: left;
            width: calc(calc(100% - 22px) * 0.6);
          }
          &:last-child {
            width: calc(calc(100% - 22px) * 0.4);
            text-align: left;
          }
        }
      }
    }
  }
}
</style>
