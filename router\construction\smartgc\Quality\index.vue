<!--智慧建设-》 安全 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  computed,
  watch, // 导入 watch
} from 'vue'
// 假设 disableActions 是一个通用工具，如果它也依赖 Freedo，可能需要调整
// import { disableActions } from '~/utils/disableActions.ts'
// import { queryProjectNews } from './api'
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import Carousel from '~/router/components/Carousel/index.vue'
import QualityProblem from './components/QualityProblem.vue'
import QualityInspection from './components/QualityInspection.vue'
import QualityAssessmentProgress from './components/QualityAssessmentProgress.vue'
import QualityEvaluationProgress from './components/QualityEvaluationProgress.vue'
import QualityFlowDetail from './components/QualityFlowDetail.vue'
import QualityAfterRectification from './components/QualityAfterRectification.vue'
import QualityBeforeRectification from './components/QualityBeforeRectification.vue'
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
// 1. 导入 useFreedo
import { useFreedo } from '../../../useFreedo'
import { type MarkerObject, type ClickedObjectInfo } from '../../freedo' // 导入类型
// 导入 portal store
import { usePortalStore } from '~/stores/portal'
import { getDict } from '~/utils/app'
import { postPage } from './api'

// 2. 使用 Composable
const {
  isApiReady,
  player,
  playerApi: freedoApiRef,
  clickedObject,
  exitWorld,
  enterWorld,
} = useFreedo()

// 使用 portal store
const portalStore = usePortalStore()
const portalId = ref('')
const layer = ref('project')
// const layer = ref('section')
// const layer=ref('dataInfo')
const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}

// ClickItem 接口定义应在此处，确保只有一个
interface ClickItem {
  subProjectId: string
  portalId: string
  portalTitle: string
  siteId: string
  position?: number[]
  target?: number[]
  duration?: number
}

const isShowBack = ref(false) // 初始不显示返回，当进入子场景或飞到点位时设为 true
const isClickItem = ref<ClickItem | null>(null) // 存储当前点击/激活的3D对象信息
const detailData = ref({
  id: '1933362879499603969',
  taskId: '6cd77093-4805-11f0-b7a1-0242ac150012',
  taskName: '已完成',
  firstTaskKey: 'UserTask_0',
  preTaskKey: 'UserTask_4',
  taskKey: 'UserTask_4',
  taskAsignee: 'huangjun',
  taskAsigneeName: '已完成',
  lastApproveDate: '已完成',
  processInstanceId: 'd5d39e2c-4804-11f0-b7a1-0242ac150012',
  correctionNumber: 'ZLWT20250004',
  description:
    '整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容',
  projectPosition: '1897204183350644737',
  constructionArea: '大屏测试',
  requirement:
    '整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容整改内容',
  deadline: '2025-06-04 00:00:00',
  reportDate: '2025-06-13 11:15:52',
  reporter: 'huangjun',
  reporterName: '黄俊',
  reporterOrgName: null,
  phone: null,
  acceptancePerson: 'huangjun',
  acceptancePersonName: '黄俊',
  correctivePerson: 'huangjun',
  correctivePersonName: '黄俊',
  rectificationStatus: '-1',
  periodStatus: 'overdue',
  periodStatusDesc: '已逾期',
  measure:
    '整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施',
  situation:
    '整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况整改情况',
  measureDate: '2025-06-01 00:00:00',
  measureFileUpload: '62034797-7BFF-4209-98F2-C90B1EBCEEE5',
  fileUpload: 'EAE9B3D8-0C34-4833-A275-8F68D47BDB58',
  processState: 1,
  isPass: 1,
  createBy: 'huangjun',
  sectionId: '1897204183350644737',
  lng: null,
  lat: null,
  height: null,
  deadLineDays: 5,
})
const safe_quality_org = ref([
  '业主检查',
  '总包检查',
  '监理检查',
  '施工方检查',
  '其他检查',
])
const active_org_index = ref(0)
const prev = () => {
  if (active_org_index.value == 0) active_org_index.value = 4
  else active_org_index.value--
}
const next = () => {
  if (active_org_index.value == 4) active_org_index.value = 0
  else active_org_index.value++
}
// 页面加载时获取数据

const list = ref([])
const fetchData = () => {
  const clickMarker: MarkerObject = {
    id: `click-marker-zl`,
    groupId: 'clickedMarkers',
    coordinate: [1570.583251953125, -489.9153137207031, 755.166259765625],
    coordinateType: 0, // 假设是世界坐标
    imagePath: '/images/video-bg.png', // 使用一个默认或显眼的图标
    imageSize: [30, 30],
    anchors: [-15, 30],
    fixedSize: true,
    text: '这是质量隐患标点',
    fontSize: 14,
    fontColor: [1.0, 1.0, 0.0, 1.0], // 黄色
    textBackgroundColor: [0.1, 0.1, 0.1, 0.7],
    showLine: false,
    range: [1, 100000],
  }
  freedoApiRef.value.marker.add(clickMarker)
  return
  postPage({
    page: 1,
    size: 999,
  }).then((res) => {
    list.value = res.data.records || []
  })
}

const backFullScene = async () => {
  console.log('返回全景')
  if (isApiReady.value) {
    try {
      layer.value = 'project'
      portalId.value = ''
      isShowBack.value = false
      await exitWorld() // 假设 exitWorld() 会重置到主视角
      console.log('Exited world / Reset view successful')
      isClickItem.value = null // 清除点击项状态
      isShowBack.value = false
    } catch (error) {
      console.error('Failed to exit world / reset view:', error)
    }
  } else {
    console.warn('Freedo API not ready for backFullScene.')
    isClickItem.value = null // 即使API未就绪，也应清除状态
    isShowBack.value = false
  }
}

// 初始的 disableActions，现在依赖 Freedo API
const initialSetup = async () => {
  try {
    await enterWorld()
    console.log('Entered world successfully during initial setup.')
  } catch (error) {
    console.error('Failed to enter world during initial setup:', error)
  }
}
onMounted(async () => {
  await initDict()
  watch(
    isApiReady,
    async (ready) => {
      if (ready) {
        // await initialSetup() // 执行初始设置，例如禁用操作
        fetchData()
        // 新增：监听场景点击以添加标记
        watch(
          clickedObject,
          (newClickedInfo) => {
            console.log(
              ' watch(clickedObject============>',
              clickedObject.value,
              newClickedInfo
            )
            if (
              clickedObject.value?.PointName &&
              Array.from(sectionMap.value.values()).indexOf(
                clickedObject.value.PointName
              ) != -1
            ) {
              // 点击的是标段标签
              layer.value = 'section'
              portalId.value = Array.from(sectionMap.value.keys())[index]
              isShowBack.value = true
            } else if (clickedObject.value?.Id == 'click-marker-zl') {
              // 如果点击的是隐患数据标签
              layer.value = 'dataInfo'
              // detailData.value = hazardList.value.find((x: any) => x.id == '00')
              // detailData.value = list.value[0]
              isShowBack.value = true
            }
            if (newClickedInfo?.point && freedoApiRef.value?.marker?.add) {
            } else if (newClickedInfo?.point) {
              console.warn(
                'Security/index.vue: Click detected, but marker API not ready or add function missing.'
              )
            }
          },
          { deep: true }
        ) // 使用 deep watch 以防 point 数组内部变化不被察觉（虽然通常 ref 的 .value 变化会触发）
      } else {
        console.log(
          'Freedo API (isApiReady) not yet true in Security/index.vue, waiting...'
        )
      }
    },
    { immediate: true }
  )
})
onBeforeUnmount(() => {
  freedoApiRef.value.marker.delete('click-marker-zl', () => {
    console.log('删除成功')
  })
})
</script>
<template>
  <PageLayout
    :showBackButton="!!isShowBack"
    :backTitle="isClickItem ? isClickItem.portalTitle : '返回全景'"
    @back="backFullScene"
  >
    <template #left>
      <Card title="质量验评进度" style="height: 55%" v-if="layer != 'dataInfo'">
        <QualityEvaluationProgress :layer="layer" />
      </Card>
      <Card
        :title="portalId ? '质量考核进度' : '试验检测情况'"
        style="flex: 1"
        v-if="layer != 'dataInfo'"
      >
        <QualityAssessmentProgress :layer="layer" />
      </Card>
      <Card title="信息流程" v-if="layer == 'dataInfo'" style="height: 100%">
        <QualityFlowDetail :layer="layer" :detailData="detailData" />
      </Card>
    </template>
    <template #right>
      <Card
        title="质量检查"
        :style="{ height: layer === 'project' ? '50%' : '30%' }"
        v-if="layer != 'dataInfo'"
      >
        <template #header-right>
          <div class="QualityInspection_header" v-if="layer === 'section'">
            <el-icon @click="prev()"><ArrowLeftBold /></el-icon>
            {{ safe_quality_org[active_org_index] }}
            <el-icon @click="next()"><ArrowRightBold /></el-icon>
          </div>
        </template>
        <QualityInspection
          :layer="layer"
          :active_org_index="active_org_index"
        />
      </Card>
      <Card title="质量问题" style="flex: 1" v-if="layer != 'dataInfo'">
        <QualityProblem :layer="layer" />
      </Card>
      <Card title="整改前" style="height: 60%" v-if="layer == 'dataInfo'">
        <QualityBeforeRectification :detailData="detailData" />
      </Card>
      <Card title="整改后" style="height: 40%" v-if="layer == 'dataInfo'">
        <QualityAfterRectification :detailData="detailData" />
      </Card>
    </template>
  </PageLayout>
</template>

<style lang="scss" scoped>
.QualityInspection_header {
  margin-right: 16px;
  display: flex;
  align-items: center;
  > i {
    cursor: pointer;
  }
}
</style>
