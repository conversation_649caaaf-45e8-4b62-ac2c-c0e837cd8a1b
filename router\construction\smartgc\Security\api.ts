
import request from '~/router/request'
import { AxiosPromise } from 'axios'

let SERVICE_PREFIX_TCS = import.meta.env.CVE_VUE_APP_BASE_TCS_SERVICENAME
let SERVICE_PREFIX_API = import.meta.env.CVE_VUE_APP_BASE_API_SERVICENAME
/**
 * @description 隐患管理列表
 * @returns 
 */
export function hazardPage(params: any) {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/inspection/hazard/page",
    method: "get", params
  });
}
/**
 * @description 危险源分布情况
 * @returns 
 */
export function statistics() {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/dangerSource/statistics",
    method: "get",
  });
}
/**
 * @description 危险源分布情况-标段统计
 * @returns 
 */
export function statsBySection(params: any) {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/dangerSource/statisticsBySection",
    method: "get", params
  });
}
/**
 * @description 安全隐患
 * @returns 
 */
export function hazard_statistics() {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/safeInspection/hazard/statistics",
    method: "get",
  });
}
/**
 * @description 安全隐患-标段统计
 * @returns 
 */
export function hazard_statisticsBySection(params: any) {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/safeInspection/hazard/statisticsBySection",
    method: "get", params
  });
}
/**
 * @description 安全检查
 * @returns 
 */
export function safeInspection_statistics() {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/safeInspection/statistics",
    method: "get",
  });
}
/**
 * @description 安全检查-标段统计
 * @returns 
 */
export function safeInspection_statisticsBySection(params: any) {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/safeInspection/statisticsBySection",
    method: "get", params
  });
}
/**
 * @description 安全教育开展情况
 * @returns 
 */
export function safeTeach_statistics() {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/safeTeach/statistics",
    method: "get",
  });
}
/**
 * @description 安全教育开展情况-标段统计
 * @returns 
 */
export function safeTeach_statisticsBySection(params: any) {
  return request({
    url: SERVICE_PREFIX_TCS + "/safe/bigscreen/safeTeach/statisticsBySection",
    method: "get", params
  });
}

/**
 * @description 工程部位
 * @param params 
 * @returns
 */
export function nodeTree(params: any) {
  return request({
    url: `${SERVICE_PREFIX_API}/wbs/node/tree`,
    method: 'get',
    params,
  })
}

