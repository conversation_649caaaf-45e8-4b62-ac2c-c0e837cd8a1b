<!-- 危险源分布情况 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { obj } from 'video.js/dist/types/utils/obj'
import { statistics, statsBySection } from '../api'
import { getDict } from '~/utils/app'
const props = defineProps({
  layer: {
    type: String,
    default: 'project',
  },
  portalId: {
    type: String,
    default: '',
  },
})
// 图表容器
const chartContainer = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance: ECharts | null = null
const sectionMap = ref<Map<string, string>>(new Map())
const riskLevelMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
  const dictRes1 = await getDict({ code: 'danger_source_risk_level' })
  if (dictRes1.data && dictRes1.data.danger_source_risk_level) {
    dictRes1.data.danger_source_risk_level
      .reverse()
      .forEach((item: { code: string; 'zh-CN': string }) => {
        riskLevelMap.value.set(item.code, item['zh-CN'])
      })
  }
}

const colors = ref([
  ['rgba(3, 171, 200, 0.29)', 'rgba(50, 209, 237, 1)'],
  ['rgba(254, 165, 64, 0.40)', 'rgba(229, 161, 82, 1)'],
  ['rgba(253, 103, 39, 0.29)', 'rgba(253, 103, 39, 1)'],
  ['rgba(251, 46, 39, 0.29)', 'rgba(251, 46, 39, 1)'],
])

const initChart = () => {
  let values = []
  const xAxisData = Array.from(
    props.layer === 'project'
      ? sectionMap.value.values()
      : riskLevelMap.value.values()
  )
  if (props.layer == 'project') {
    values = Array.from(riskLevelMap.value.keys()).map((x: any, i: number) => {
      return {
        name: riskLevelMap.value.get(x),
        type: 'bar',
        data: Array.from(sectionMap.value.keys()).map(
          (y: string, j: number) => {
            return {
              name: sectionMap.value.get(y),
              value: statistics_data.value[sectionMap.value.get(y)]
                ? statistics_data.value[sectionMap.value.get(y)][
                    riskLevelMap.value.get(x)
                  ] || 0
                : 0,
            }
          }
        ),
        stack: 'all',
        barWidth: 12,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: colors.value[i][0] },
            { offset: 1, color: colors.value[i][1] },
          ]),
        },
      }
    })
  } else {
    values = [
      {
        type: 'bar',
        data: Array.from(riskLevelMap.value.values()).map(
          (x) => statistics_data.value[x] || 0
        ),
        stack: 'all',
        barWidth: 12,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(3, 171, 200, 0.29)' },
            { offset: 1, color: 'rgba(50, 209, 237, 1)' },
          ]),
        },
      },
    ]
  }
  console.log(xAxisData, values, 'xAxisData, values')
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      top: 'top',
      left: 'center',
      itemWidth: 14,
      itemHeight: 14,

      textStyle: {
        color: '#fff',
        fontSize: 16,
      },
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '0%',
      containLabel: true,
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      startValue: 0,
      max: (value: any) => {
        return value.max < 10 ? 10 : null
      },
      // name: "单位(个)",
      // nameTextStyle: {
      //   color: "#E8E8E8",
      //   fontSize: 12,
      //   align: "right"
      // },
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
          type: 'dashed',
        },
      },
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        color: '#fff',
        fontSize: 14,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(221, 221, 221, 0.44)',
        },
      },
    },
    series: values,
  }
  if (chartContainer.value) {
    echarts.getInstanceByDom(chartContainer.value)?.dispose()
    chartInstance = echarts.init(chartContainer.value) // 初始化实例
    chartInstance.setOption(option) // 设置配置项
  }
}
watch(
  () => props.layer,
  () => {
    fetchData()
  }
)

watchEffect(() => {})

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}
const statistics_data = ref({})
const fetchData = () => {
  if (props.layer === 'project') {
    statistics()
      .then((res) => {
        console.log(res.data, 'statistics_data')
        statistics_data.value = res.data || {}
        initChart()
      })
      .catch((err) => {})
  } else {
    statsBySection({ portalId: props.portalId })
      .then((res) => {
        // console.log(res.data, "statistics_data");
        statistics_data.value = res.data || {}
        initChart()
      })
      .catch((err) => {})
  }
}

// 生命周期钩子
onMounted(async () => {
  await initDict()
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="QualityAssessmentProgress">
    <div ref="chartContainer" class="chartContainer"></div>
  </div>
</template>
<style lang="scss" scoped>
.QualityAssessmentProgress {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .chartContainer {
    height: 100%;
    width: 100%;
  }
}
</style>
