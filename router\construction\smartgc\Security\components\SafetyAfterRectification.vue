<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watchEffect, watch, reactive } from "vue";
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { graphic } from "echarts";
import { usePortalStore } from "~/stores";
import { getFile, downloadImage } from "~/utils/api/file";
import dayjs from "dayjs";
const props = defineProps({
  layer: {
    type: String,
    default: "project"
  },
  detailData: {
    type: Object,
    default: () => {}
  }
});
const imgList = ref([]);
const LABEL_MAP = ref({
  "-1": "已完成",
  "0": "暂存",
  "1": "待分发",
  "2": "待整改",
  "3": "待监理审核",
  "4": "待复核"
});

const fetchData = () => {
  if (props.detailData.measureFileUpload) {
    getFile({ g9s: [props.detailData.measureFileUpload] }).then((res) => {
      res.data.forEach((x: any) => {
        downloadImage(x.fileToken).then((resp) => {
          const objecturl = window.URL.createObjectURL(resp);
          imgList.value.push(objecturl);
        });
      });
    });
  }
};

watchEffect(() => {});

// 生命周期钩子
onMounted(() => {
  fetchData();
});
onBeforeUnmount(() => {});
</script>
<template>
  <div class="QualityAfterRectification">
    <div>
      <span>问题整改人</span>
      <span>{{ detailData.acceptancePersonName }}</span>
    </div>
    <div>
      <span>整改完成日期</span>
      <span>{{ dayjs(detailData.measureDate).format("YYYY-MM-DD") }}</span>
    </div>
    <div class="content">
      <div>
        <span>整改措施</span>
        <span>
          <span>
            {{ detailData.measure }}
          </span>
        </span>
      </div>
      <div>
        <span>整改情况</span>
        <span>
          <span>
            {{ LABEL_MAP[detailData.rectificationStatus] }}
          </span>
        </span>
      </div>
    </div>
    <div class="file">
      <span>整改后图片</span>
      <div class="imgBox">
        <img v-for="item in imgList" :key="item" :src="item" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.QualityAfterRectification {
  width: 100%;
  height: 100%;
  overflow: auto;
  > div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    > span {
      font-size: 16px;
      &:first-child {
        color: #ffffff;
      }
      &:last-child {
        color: #effbff;
        text-align: right;
      }
    }
    &.content {
      gap: 30px;
      > div {
        flex: 1;
        display: flex;
        flex-direction: column;
        > span {
          &:first-child {
            margin-bottom: 4px;
          }
          &:last-child {
            background: linear-gradient(90deg, #2196cc 0%, rgba(16, 123, 172, 0.35) 100%);
            border-radius: 3px 3px 3px 3px;
            padding: 10px;
            span {
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 4; /* 控制显示的行数 */
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    &.file {
      flex-direction: column;
      span {
        margin-bottom: 8px;
      }
      .imgBox {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(auto, 1fr);
        gap: 8px;
        img {
          height: 110px;
          width: 100%;
        }
      }
    }
  }
}
</style>
