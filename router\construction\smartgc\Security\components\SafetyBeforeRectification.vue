<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watchEffect, watch, reactive } from "vue";
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { graphic } from "echarts";
import { usePortalStore } from "~/stores";
import { getFile, downloadImage } from "~/utils/api/file";
import dayjs from "dayjs";
import { getDict } from "~/utils/app";
import { nodeTree } from "../api";
const props = defineProps({
  layer: {
    type: String,
    default: "project"
  },
  detailData: {
    type: Object,
    default: () => {}
  }
});
const imgList = ref([]);
const nodeTreeData = ref([]);
const fetchData = () => {
  if (props.detailData.fileUpload) {
    getFile({ g9s: [props.detailData.fileUpload] }).then((res) => {
      res.data.forEach((x: any) => {
        downloadImage(x.fileToken).then((resp) => {
          const objecturl = window.URL.createObjectURL(resp);
          imgList.value.push(objecturl);
        });
      });
    });
  }
  if (props.detailData.sectionId) {
    nodeTree({ portalId: props.detailData.sectionId }).then((res) => {
      nodeTreeData.value = treeToArray(res.data);
    });
  }
};
const treeToArray = (tree, result = []) => {
  if (!tree || !tree.length) return result;
  for (const node of tree) {
    result.push(node);
    if (node.children && node.children.length) {
      treeToArray(node.children, result);
    }
  }
  return result;
};
const sectionMap = ref<Map<string, string>>(new Map());
const initDict = async () => {
  const dictRes = await getDict({ code: "project_section" });
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach((item: { code: string; "zh-CN": string }) => {
      sectionMap.value.set(item.code, item["zh-CN"]);
    });
  }
};

watchEffect(() => {});

// 生命周期钩子
onMounted(() => {
  initDict();
  fetchData();
});
onBeforeUnmount(() => {});
</script>
<template>
  <div class="QualityBeforeRectification">
    <div>
      <span>整改单号</span><span>{{ detailData.hazardNumber }}</span>
    </div>
    <div>
      <span>标段工程</span><span>{{ sectionMap.get(detailData.sectionId) }}</span>
    </div>
    <div>
      <span>工程部位</span>
      <span>{{
        nodeTreeData.find((x) => x.id === detailData.projectPosition)?.nodeName
      }}</span>
    </div>
    <div>
      <span>整改期限</span>
      <span>{{ dayjs(detailData.deadline).format("YYYY-MM-DD") }}</span>
    </div>
    <div>
      <span>逾期状态</span><span>{{ detailData.periodStatusDesc }}</span>
    </div>
    <div>
      <span>问题上报人</span><span>{{ detailData.reporterName }}</span>
    </div>
    <div class="content">
      <span>整改内容</span>
      <div>
        <span> {{ detailData.description }} </span>
      </div>
    </div>
    <div class="file">
      <span>整改前图片</span>
      <div class="imgBox">
        <img v-for="item in imgList" :key="item" :src="item" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.QualityBeforeRectification {
  width: 100%;
  height: 100%;
  overflow: auto;
  > div {
    display: flex;
    margin-bottom: 8px;
    justify-content: space-between;
    > span {
      font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
      &:first-child {
        font-size: 16px;
        color: #ffffff;
        margin-right: 16px;
      }
      &:last-child {
        flex: 1;
        text-align: right;
        font-size: 16px;
        color: #effbff;
      }
    }
    &.content {
      flex-direction: column;
      > span {
        margin-bottom: 4px;
      }
      div {
        padding: 10px;
        background: linear-gradient(90deg, #2196cc 0%, rgba(16, 123, 172, 0.35) 100%);
        border-radius: 3px;
        font-size: 16px;
        color: #ddedf4;
        text-align: left;
        span {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 控制显示的行数 */
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 16px;
          color: #ddedf4;
        }
      }
    }
    &.file {
      flex-direction: column;
      span {
        margin-bottom: 8px;
      }
      .imgBox {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(auto, 1fr);
        gap: 8px;
        img {
          height: 110px;
          width: 100%;
        }
      }
    }
  }
}
</style>
