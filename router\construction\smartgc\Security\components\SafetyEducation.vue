<!-- 建设总览-安全-风险等级 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
  computed,
} from 'vue'
import * as echarts from 'echarts'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { safeTeach_statistics, safeTeach_statisticsBySection } from '../api'
import { getDict } from '~/utils/app'
const props = defineProps({
  layer: {
    type: String,
    default: 'project',
  },
  portalId: {
    type: String,
    default: '',
  },
})
// 图表容器
const chartContainer = ref<HTMLDivElement | null>(null)
const activeTab = ref('aqjd')
// 图表实例
let chartInstance: ECharts | null = null
watchEffect(() => {})

const percent = computed(() => {
  return (value: string | number, total: string | number) => {
    if (!Number(total)) return 0
    else if (total <= value) return 100
    else return Math.ceil((Number(value) / Number(total)) * 100)
  }
})
const safeTeach_statistics_data = ref({
  classMeetingCountMap: {},
  safeTeachCountMap: {},
})
const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}
const fetchData = () => {
  if (props.layer === 'project') {
    safeTeach_statistics().then((res) => {
      // console.log(res.data, "safeTeach_statistics");
      if (res.data) safeTeach_statistics_data.value = res.data
    })
  } else {
    safeTeach_statisticsBySection({ portalId: props.portalId }).then((res) => {
      // console.log(res.data, "safeTeach_statisticsBySection");
      if (res.data) safeTeach_statistics_data.value = res.data
      initChart()
    })
  }
}
const clickTab = (key: string) => {
  activeTab.value = key
  if (props.layer == 'section') initChart()
}
const initChart = () => {
  const option = {
    tooltip: {
      trigger: 'item',
    },
    title: {
      text:
        safeTeach_statistics_data.value[
          activeTab.value == 'aqjd' ? 'classMeetingTotal' : 'safeTeachTotal'
        ] || 0,
      subtext: '全部数据',
      left: '30%',
      top: '45%',
      textAlign: 'center',
      textVerticalAlign: 'middle',
      textStyle: {
        color: '#fff',
        fontSize: 30,
      },
      subtextStyle: {
        color: '#fff',
        fontSize: 16,
      },
    },
    legend: {
      top: 'center',
      right: 'right',
      icon: 'circle',
      orient: 'vertical',
      formatter: (name: string) => {
        console.log(
          name,
          safeTeach_statistics_data.value.safeTeachCountMap,
          activeTab.value == 'aqjd'
            ? 'classMeetingCountMap'
            : 'safeTeachCountMap'
        )
        return `{a|${name}}{b|${percent.value(
          safeTeach_statistics_data.value[
            activeTab.value == 'aqjd'
              ? 'classMeetingCountMap'
              : 'safeTeachCountMap'
          ][name] || 0,
          safeTeach_statistics_data.value[
            activeTab.value == 'aqjd' ? 'classMeetingTotal' : 'safeTeachTotal'
          ] || 0
        )}%}`
      },
      textStyle: {
        color: '#fff',
        rich: {
          a: {
            fontSize: 14,
            padding: [0, 15, 0, 0],
          },
          b: {
            fontSize: 18,
          },
        },
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['55%', '70%'],
        center: ['30%', '50%'],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: Object.keys(
          safeTeach_statistics_data.value[
            activeTab.value == 'aqjd'
              ? 'classMeetingCountMap'
              : 'safeTeachCountMap'
          ]
        ).map((x) => {
          return {
            name: x,
            value:
              safeTeach_statistics_data.value[
                activeTab.value == 'aqjd'
                  ? 'classMeetingCountMap'
                  : 'safeTeachCountMap'
              ][x] || 0,
          }
        }),
      },
    ],
  }
  if (chartContainer.value) {
    echarts.getInstanceByDom(chartContainer.value)?.dispose()
    chartInstance = echarts.init(chartContainer.value) // 初始化实例
    chartInstance.setOption(option) // 设置配置项
  }
}
// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}
// 生命周期钩子
onMounted(async () => {
  await initDict()
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="SafetyEducation">
    <div class="top">
      <div>
        <span>安全交底</span>
        <span
          >{{ safeTeach_statistics_data.classMeetingTotal || 0 }}
          <span>次</span></span
        >
      </div>
      <div>
        <span>安全教育</span>
        <span
          >{{ safeTeach_statistics_data.safeTeachTotal || 0 }}
          <span>次</span></span
        >
      </div>
    </div>
    <div class="btns">
      <div :class="{ active: activeTab == 'aqjd' }" @click="clickTab('aqjd')">
        安全交底统计
      </div>
      <div :class="{ active: activeTab == 'aqjy' }" @click="clickTab('aqjy')">
        安全教育
      </div>
    </div>
    <div class="SectionBox" v-if="props.layer === 'project'">
      <div v-for="[key, value] in sectionMap" :key="key">
        <span class="name">{{ value }}</span>
        <div>
          <div
            :style="{
              width: `${percent(
                safeTeach_statistics_data[
                  activeTab == 'aqjd'
                    ? 'classMeetingCountMap'
                    : 'safeTeachCountMap'
                ][value],
                safeTeach_statistics_data[
                  activeTab == 'aqjd' ? 'classMeetingTotal' : 'safeTeachTotal'
                ]
              )}%`,
            }"
          ></div>
          <img
            src="../img/光圈.png"
            alt=""
            :style="{
              left: `${percent(
                safeTeach_statistics_data[
                  activeTab == 'aqjd'
                    ? 'classMeetingCountMap'
                    : 'safeTeachCountMap'
                ][value],
                safeTeach_statistics_data[
                  activeTab == 'aqjd' ? 'classMeetingTotal' : 'safeTeachTotal'
                ]
              )}%`,
            }"
          />
        </div>
        <span class="data">{{
          safeTeach_statistics_data[
            activeTab == 'aqjd' ? 'classMeetingCountMap' : 'safeTeachCountMap'
          ][value] || 0
        }}</span>
      </div>
    </div>
    <div v-else ref="chartContainer" class="chartContainer"></div>
  </div>
</template>
<style lang="scss" scoped>
.SafetyEducation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .top {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    > div {
      display: flex;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      flex-direction: column;
      height: 60px;
      padding-left: 15%;
      flex: 1;
      justify-content: center;
      &:first-child {
        background-image: url('../img/安全交底.png');
      }
      &:last-child {
        background-image: url('../img/安全教育.png');
      }
      > span {
        &:first-child {
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 14px;
          color: #c4e2ee;
        }
        &:last-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 20px;
          color: #e0fbff;
          span {
            font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 12px;
          }
        }
      }
    }
  }
  .btns {
    display: flex;
    margin-bottom: 18px;
    > div {
      cursor: pointer;
      margin-right: 16px;
      width: 116px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      box-shadow: inset 0px 2px 5px 0px rgba(8, 68, 105, 0.59),
        0px 2px 6px 0px rgba(44, 134, 183, 0.25);
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e1f7ff;

      &.active {
        background: linear-gradient(
          270deg,
          rgba(19, 158, 223, 0.13) 0%,
          rgba(49, 190, 255, 0.5) 100%
        );
        box-shadow: 0px 2px 6px 0px rgba(44, 134, 183, 0.25);
        border: 1px solid #9ed4e7;
      }
    }
  }
  .SectionBox {
    flex: 1;
    overflow: auto;
    > div {
      padding: 30px 30px 0 0;
      display: flex;
      align-items: center;
      position: relative;
      > div {
        flex: 1;
        height: 10px;
        background: rgba(0, 86, 125, 0.75);
        border-radius: 5px 5px 5px 5px;
        position: relative;
        > div {
          height: 100%;
          position: absolute;
          background: linear-gradient(90deg, #2a83f0 0%, #57fde7 100%);
          box-shadow: 0px 2px 6px 0px rgba(10, 75, 110, 0.25);
          border-radius: 5px 5px 5px 5px;
          z-index: 1;
        }
        img {
          position: absolute;
          width: 22px;
          height: 22px;
          top: 50%;
          transform: translate(-50%, -50%);
          z-index: 2;
        }
      }
      .name {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        font-weight: normal;
        font-size: 14px;
        color: #cccaca;
        margin-right: 6px;
        width: 100px;
        text-align: center;
      }
      .data {
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        font-weight: normal;
        font-size: 14px;
        color: #ffffff;
        position: absolute;
        // top: 0;
        transform: translateY(-100%);
        right: 30px;
      }
    }
  }
  .chartContainer {
    flex: 1;
  }
}
</style>
