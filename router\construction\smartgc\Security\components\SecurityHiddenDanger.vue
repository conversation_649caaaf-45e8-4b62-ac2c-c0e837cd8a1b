<!-- 安全隐患 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  watch,
  reactive,
  computed,
} from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { ECharts } from 'echarts'
import { graphic } from 'echarts'
import { usePortalStore } from '~/stores'
import { hazard_statistics, hazard_statisticsBySection } from '../api'
import { getDict } from '~/utils/app'
const props = defineProps({
  layer: {
    type: String,
    default: 'project',
  },
  portalId: {
    type: String,
    default: '',
  },
})
// 图表容器
const chartContainer1 = ref<HTMLDivElement | null>(null)
const chartContainer2 = ref<HTMLDivElement | null>(null)
const chartContainer3 = ref<HTMLDivElement | null>(null)
// 图表实例
let chartInstance1: ECharts | null = null
let chartInstance2: ECharts | null = null
let chartInstance3: ECharts | null = null
const color = ref(['#27F9FF', '#2DB5FF', '#2EEB90', '#C5FF73', '#FAAE56'])

const project_section_color = ref([
  'rgba(238, 181, 82, 0.5)',
  'rgba(70, 239, 163, 0.5)',
  'rgba(138, 109, 224, 0.5)',
  'rgba(33, 211, 243, 0.5)',
  'rgba(45, 181, 255, 0.5)',
  'rgba(46, 235, 144, 0.5)',
])
const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}
const state1 = ref(['待审核', '待整改', '待审批', '待复核', '已完成'])
const state2 = ref(['临期', '逾期', '正常'])
/**
 * 获取面的参数方程
 * @param {*} startRatio 扇形起始位置比例
 * @param {*} endRatio 扇形结束位置比例
 * @param {*} k 辅助参数,控制饼图半径
 * @param {*} value 数值
 */
const getParametricEquation = (startRatio, endRatio, k, value) => {
  const startRadian = startRatio * Math.PI * 2
  const endRadian = endRatio * Math.PI * 2

  k = typeof k === 'number' && !isNaN(k) ? k : 1 / 3 //默认1/3

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x(u, v) {
      if (u < startRadian) {
        return Math.cos(startRadian) * (1 + Math.cos(v) * k)
      }
      if (u > endRadian) {
        return Math.cos(endRadian) * (1 + Math.cos(v) * k)
      }
      return Math.cos(u) * (1 + Math.cos(v) * k)
    },

    y(u, v) {
      if (u < startRadian) {
        return Math.sin(startRadian) * (1 + Math.cos(v) * k)
      }
      if (u > endRadian) {
        return Math.sin(endRadian) * (1 + Math.cos(v) * k)
      }
      return Math.sin(u) * (1 + Math.cos(v) * k)
    },

    z(u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * 100 * 0.1
        // return Math.sin(u) * value * 0.1
      }
      // 扇形高度根据value值计算
      return Math.sin(v) > 0 ? 100 * 0.1 : -1
      // return Math.sin(v) > 0 ? value * 0.1 : -1
    },
  }
}

const initOptions = () => {
  const data =
    hazard_statistics_data.value[
      activeTab.value == 'aqjd' ? 'pendingPotalsPointMap' : 'potalsPointMap'
    ] || {}
  delete data['信息化标']
  //总数
  let total = Object.keys(data).reduce((a, b) => a + Number(data[b]) || 0, 0)
  //当前累加值
  let sumValue = 0
  //辅助参数,控制饼图半径，（0-1）范围内控制环形大小，值越小环形内半径越大
  let k = 0.2

  //series配置（每个扇形）
  let series = Object.keys(data).map((item, index) => {
    //当前扇形起始位置占饼图比例
    let startRatio = sumValue / total
    //值累加
    sumValue += Number(data[item])
    //当前扇形结束位置占饼图比例
    let endRatio = sumValue / total

    return {
      name: item ?? null,
      type: 'surface', //曲面图
      itemStyle: {
        color: project_section_color.value[index], //颜色
      },
      wireframe: {
        show: false, //不显示网格线
      },
      pieData: data[item], //数据
      //饼图状态
      pieStatus: {
        k, //辅助参数
        startRatio, //起始位置比例
        endRatio, //结束位置比例
        value: data[item], //数值
      },
      // center: ['50%', '50%'],
      // radius: '20%',
      parametric: true, //参数曲面
      //曲面的参数方程
      parametricEquation: getParametricEquation(
        startRatio,
        endRatio,
        k,
        data[item]
      ),
    }
  })

  //返回配置
  return {
    //提示框
    tooltip: {
      formatter: (params: any) => {
        if (
          params.seriesName !== 'mouseoutSeries' &&
          params.seriesName !== 'pie2d'
        ) {
          return `${
            params.seriesName
          }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
            params.color
          };"></span>${data[params.seriesName]}%`
        }
        return ''
      },
    },
    legend: {
      show: false,
      top: 'center',
      right: 'right',
      icon: 'circle',
      orient: 'vertical',
      formatter: (name: string) => {
        return `{a|${name}}{b|${Number(data[name])}%}`
      },
      itemStyle: {
        borderWidth: 1,
        borderColor: '#fff',
      },
      textStyle: {
        color: '#fff',
        rich: {
          a: {
            fontSize: 14,
            width: 65,
          },
          b: {
            fontSize: 18,
          },
        },
      },
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    //
    grid3D: {
      show: false, //不显示坐标系
      boxHeight: 2, //饼图高度
      // 用于鼠标的旋转，缩放等视角控制
      viewControl: {
        alpha: 25, //视角
        distance: 200, //距离，值越大饼图越小
        rotateSensitivity: 1, //禁止旋转
        zoomSensitivity: 0, //禁止缩放
        panSensitivity: 0, //禁止平移
        // rotateSensitivity: 0, //禁用鼠标控制旋转
        autoRotate: true, //禁止自动旋转
      },
    },
    series,
  }
}
const hazard_statistics_data = ref({})
const fetchData = () => {
  if (props.layer == 'project') {
    hazard_statistics().then((res) => {
      if (res.data) hazard_statistics_data.value = res.data
      // console.log(res.data, "hazard_statistics");
      initChart()
    })
  } else {
    hazard_statisticsBySection({ portalId: props.portalId }).then((res) => {
      if (res.data) hazard_statistics_data.value = res.data
      // console.log(res.data, "hazard_statisticsBySection");
      initChart()
    })
  }
}
const initChart = () => {
  // 图表配置
  if (chartContainer1.value && props.layer === 'project') {
    const option1 = initOptions()
    console.log('🚀 ~ initChart ~ option1:', option1)
    echarts.getInstanceByDom(chartContainer1.value)?.dispose()
    chartInstance1 = echarts.init(chartContainer1.value) // 初始化实例
    chartInstance1.setOption(option1) // 设置配置项
  } else {
    const total2 = Object.values(
      hazard_statistics_data.value.rectificationStatusMap || {}
    ).reduce((acc, val) => acc + val, 0)
    const total3 = Object.values(
      hazard_statistics_data.value.periodStatusMap || {}
    ).reduce((acc, val) => acc + val, 0)
    const option2 = {
      tooltip: {
        trigger: 'item',
      },
      title: {
        text: total2,
        subtext: '质量问题',
        left: '30%',
        top: '45%',
        textAlign: 'center',
        textVerticalAlign: 'middle',
        textStyle: {
          color: '#fff',
          fontSize: 30,
        },
        subtextStyle: {
          color: '#fff',
          fontSize: 16,
        },
      },
      color: color.value,
      legend: {
        top: 'center',
        icon: 'circle',
        right: 'right',
        orient: 'vertical',
        formatter: (name: string) => {
          return `{a|${name}}{b|${
            hazard_statistics_data.value.rectificationStatusMap[name] || 0
          }}`
        },
        textStyle: {
          color: '#fff',
          rich: {
            a: {
              fontSize: 14,
              padding: [0, 15, 0, 0],
            },
            b: {
              fontSize: 18,
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['55%', '70%'],
          center: ['30%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          // padAngle: 5,
          data: Object.keys(
            hazard_statistics_data.value['rectificationStatusMap']
          ).map((x) => {
            return {
              value:
                hazard_statistics_data.value['rectificationStatusMap'][x] || 0,
              name: x,
            }
          }),
        },
      ],
    }
    if (chartContainer2.value) {
      echarts.getInstanceByDom(chartContainer2.value)?.dispose()
      chartInstance2 = echarts.init(chartContainer2.value) // 初始化实例
      chartInstance2.setOption(option2) // 设置配置项
    }
    const option3 = {
      tooltip: {
        trigger: 'item',
      },
      title: {
        text: total3,
        subtext: '待整改问题',
        left: '30%',
        top: '45%',
        textAlign: 'center',
        textVerticalAlign: 'middle',
        textStyle: {
          color: '#fff',
          fontSize: 30,
        },
        subtextStyle: {
          color: '#fff',
          fontSize: 16,
        },
      },
      color: color.value,
      legend: {
        top: 'center',
        right: 'right',
        icon: 'circle',
        orient: 'vertical',
        formatter: (name: string) => {
          return `{a|${name}}{b|${
            hazard_statistics_data.value.periodStatusMap[name] || 0
          }}`
        },
        textStyle: {
          color: '#fff',
          rich: {
            a: {
              fontSize: 14,
              padding: [0, 15, 0, 0],
            },
            b: {
              fontSize: 18,
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['55%', '70%'],
          center: ['30%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          // padAngle: 5,
          data: Object.keys(
            hazard_statistics_data.value['periodStatusMap']
          ).map((x) => {
            return {
              value: hazard_statistics_data.value['periodStatusMap'][x] || 0,
              name: x,
            }
          }),
        },
      ],
    }
    if (chartContainer3.value) {
      echarts.getInstanceByDom(chartContainer3.value)?.dispose()
      chartInstance2 = echarts.init(chartContainer3.value) // 初始化实例
      chartInstance2.setOption(option3) // 设置配置项
    }
  }
}

watchEffect(() => {})

// 销毁图表
const destroyChart = () => {
  if (chartInstance1) {
    chartInstance1.dispose()
    chartInstance1 = null
  }
  if (chartInstance2) {
    chartInstance2.dispose()
    chartInstance2 = null
  }
  if (chartInstance3) {
    chartInstance3.dispose()
    chartInstance3 = null
  }
}
const activeTab = ref('aqjd')
const clickTab = (key: string) => {
  activeTab.value = key
  initChart()
}
// 生命周期钩子
onMounted(() => {
  initDict()
  fetchData()
})
onBeforeUnmount(() => {
  destroyChart()
})
</script>
<template>
  <div class="QualityInspection">
    <div v-if="props.layer === 'project'" style="height: 100%">
      <div class="top">
        <div>
          <span>累计隐患</span>
          <span>{{ hazard_statistics_data.total || 0 }} <span>个</span></span>
        </div>
        <div>
          <span>待整改隐患</span>
          <span
            >{{ hazard_statistics_data.pendingTotal || 0 }}
            <span>个</span></span
          >
        </div>
      </div>
      <div class="btns">
        <div :class="{ active: activeTab == 'aqjd' }" @click="clickTab('aqjd')">
          安全交底统计
        </div>
        <div :class="{ active: activeTab == 'aqjy' }" @click="clickTab('aqjy')">
          安全教育
        </div>
      </div>
      <div class="chartContainer1">
        <div ref="chartContainer1" class="chart"></div>
        <div class="legend">
          <div
            v-for="(value, key, index) in hazard_statistics_data[
              activeTab == 'aqjd' ? 'pendingPotalsPointMap' : 'potalsPointMap'
            ]"
            :key="key"
          >
            <span
              :style="{ backgroundColor: project_section_color[index] }"
            ></span>
            <span>{{ key }}</span>
            <span>{{ value }}%</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else style="height: 100%; display: flex; flex-direction: column">
      <div ref="chartContainer2" class="chartContainer2"></div>
      <div ref="chartContainer3" class="chartContainer3"></div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.QualityInspection {
  width: 100%;
  height: 100%;
  overflow: hidden;
  > div {
    display: flex;
    flex-direction: column;
  }
  .top {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    > div {
      display: flex;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      flex-direction: column;
      height: 60px;
      padding-left: 15%;
      width: 50%;
      justify-content: center;
      &:first-child {
        background-image: url('../img/累计问题.png');
      }
      &:last-child {
        background-image: url('../img/待整改问题.png');
      }
      > span {
        &:first-child {
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 14px;
          color: #c4e2ee;
        }
        &:last-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 20px;
          color: #e0fbff;
          span {
            font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 12px;
          }
        }
      }
    }
  }
  .btns {
    display: flex;
    margin-bottom: 18px;
    > div {
      cursor: pointer;
      margin-right: 16px;
      width: 116px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      box-shadow: inset 0px 2px 5px 0px rgba(8, 68, 105, 0.59),
        0px 2px 6px 0px rgba(44, 134, 183, 0.25);
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e1f7ff;

      &.active {
        background: linear-gradient(
          270deg,
          rgba(19, 158, 223, 0.13) 0%,
          rgba(49, 190, 255, 0.5) 100%
        );
        box-shadow: 0px 2px 6px 0px rgba(44, 134, 183, 0.25);
        border: 1px solid #9ed4e7;
      }
    }
  }
  .chartContainer1,
  .chartContainer2,
  .chartContainer3 {
    flex: 1;
  }
  .chartContainer1 {
    // background-image: url("../img/echarts_bg.png");
    background-size: 80% 100%;
    background-position: center left;
    background-repeat: no-repeat;
    display: flex;
    .chart {
      width: 60%;
    }
    .legend {
      width: 40%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        span {
          &:first-child {
            width: 12px;
            height: 12px;
            border: 1px solid #ffffff;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
          }
          &:nth-child(2) {
            text-align: left;
            width: calc(calc(100% - 22px) * 0.6);
          }
          &:last-child {
            width: calc(calc(100% - 22px) * 0.4);
            text-align: left;
          }
        }
      }
    }
  }
}
</style>
