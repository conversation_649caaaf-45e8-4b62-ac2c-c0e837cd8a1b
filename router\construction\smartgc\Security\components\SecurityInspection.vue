<!-- 安全检查 -->
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watchEffect, watch, reactive } from "vue";
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { graphic } from "echarts";
import { usePortalStore } from "~/stores";
import { safeInspection_statistics, safeInspection_statisticsBySection } from "../api";
const props = defineProps({
  layer: {
    type: String,
    default: "project"
  },
  portalId: {
    type: String,
    default: ""
  }
});
// 图表容器
const chartContainer = ref<HTMLDivElement | null>(null);
// 图表实例
let chartInstance: ECharts | null = null;
const safe_quality_org = ref([
  { "zh-CN": "业主检查", code: "1" },
  { "zh-CN": "总包检查", code: "2" },
  { "zh-CN": "监理检查", code: "4" },
  { "zh-CN": "施工方检查", code: "5" },
  { "zh-CN": "其他检查", code: "99" }
]);

const initChart = () => {
  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "5%",
      containLabel: true
    },
    xAxis: {
      type: "value",
      minInterval: 1,
      startValue: 0,
      max: (value: any) => {
        return value.max < 10 ? 10 : null;
      },
      axisLabel: {
        color: "#fff",
        fontSize: 14
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: "rgba(221, 221, 221, 0.44)",
          type: "dashed"
        }
      }
    },
    yAxis: {
      type: "category",
      data: safe_quality_org.value.map((x) => x["zh-CN"]),
      axisLabel: {
        color: "#fff",
        fontSize: 14
      },
      axisTick: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: "rgba(221, 221, 221, 0.44)"
        }
      }
    },
    series: [
      {
        type: "bar",
        data: safe_quality_org.value.map(
          (x) => safeInspection_statistics_data.value.countMap[x["zh-CN"]] || 0
        ),
        barWidth: 20,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: "rgba(3, 87, 102, 0.51)" },
            { offset: 1, color: "rgba(57, 208, 235, 1)" }
          ])
        }
      }
    ]
  };
  if (chartContainer.value) {
    echarts.getInstanceByDom(chartContainer.value)?.dispose();
    chartInstance = echarts.init(chartContainer.value); // 初始化实例
    chartInstance.setOption(option); // 设置配置项
  }
};

watchEffect(() => {});
const safeInspection_statistics_data = ref({});
const fetchData = () => {
  if (props.layer == "project") {
    safeInspection_statistics().then((res) => {
      if (res.data) safeInspection_statistics_data.value = res.data;
      // console.log(res.data, "safeInspection_statistics");
      initChart();
    });
  } else {
    safeInspection_statisticsBySection({ portalId: props.portalId }).then((res) => {
      if (res.data) safeInspection_statistics_data.value = res.data;
      // console.log(res, "safeInspection_statisticsBySection");
    });
  }
};
// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
};

// 生命周期钩子
onMounted(() => {
  fetchData();
});
onBeforeUnmount(() => {
  destroyChart();
});
</script>
<template>
  <div class="QualityInspection">
    <div class="top">
      <div class="ljjc">
        <span>累计检查</span>
        <span>{{ safeInspection_statistics_data.total || 0 }} <span>次</span></span>
      </div>
      <div class="byjc" v-if="props.layer === 'section'">
        <span>本月检查</span>
        <span>{{ safeInspection_statistics_data.monthTotal || 0 }} <span>次</span></span>
      </div>
      <div class="ljfxwt" v-if="props.layer === 'section'">
        <span>累计发现问题</span>
        <span
          >{{ safeInspection_statistics_data.questionableTotal || 0 }}
          <span>次</span></span
        >
      </div>
      <div class="wtfxpc">
        <span>问题发现频次</span>
        <span>{{ safeInspection_statistics_data.point || 0 }} <span>次</span></span>
      </div>
    </div>
    <div
      v-if="props.layer === 'project'"
      ref="chartContainer"
      class="chartContainer"
    ></div>
  </div>
</template>
<style lang="scss" scoped>
.QualityInspection {
  width: 100%;
  height: 100%;
  .top {
    display: flex;
    gap: 50px 30px;
    flex-wrap: wrap;
    > div {
      display: flex;
      width: calc(50% - 15px);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      flex-direction: column;
      height: 60px;
      padding-left: 15%;
      justify-content: center;
      &.ljjc {
        background-image: url("../img/累计临检.png");
      }
      &.byjc {
        background-image: url("../img/本月检查.png");
      }
      &.ljfxwt {
        background-image: url("../img/累计发现问题.png");
      }
      &.wtfxpc {
        background-image: url("../img/问题发现频次.png");
      }
      > span {
        &:first-child {
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 14px;
          color: #c4e2ee;
        }
        &:last-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 20px;
          color: #e0fbff;
          span {
            font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 12px;
          }
        }
      }
    }
  }
  .chartContainer {
    margin-top: 16px;
    height: calc(100% - 60px - 16px);
  }
}
</style>
