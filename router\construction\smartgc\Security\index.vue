<!--智慧建设-》 安全 -->
<script lang="ts" setup>
import {
  ref,
  onMounted,
  onUnmounted,
  computed,
  onBeforeUnmount,
  watch, // 导入 watch
} from 'vue'
// 假设 disableActions 是一个通用工具，如果它也依赖 Freedo，可能需要调整
// import { disableActions } from '~/utils/disableActions.ts'
// import { queryProjectNews } from './api'
import PageLayout from '~/router/components/PageLayout/index.vue'
import Card from '~/router/components/Card/index.vue'
import Carousel from '~/router/components/Carousel/index.vue'
import SecurityHiddenDanger from './components/SecurityHiddenDanger.vue'
import SecurityInspection from './components/SecurityInspection.vue'
import HazardSourceDistribution from './components/HazardSourceDistribution.vue'
import SafetyEducation from './components/SafetyEducation.vue'
import SafetyFlowDetail from './components/SafetyFlowDetail.vue'
import SafetyAfterRectification from './components/SafetyAfterRectification.vue'
import SafetyBeforeRectification from './components/SafetyBeforeRectification.vue'
import { hazardPage } from './api'
import { getDict } from '~/utils/app'
// 1. 导入 useFreedo
import { useFreedo } from '../../../useFreedo'
import { type MarkerObject, type ClickedObjectInfo } from '../../freedo' // 导入类型
// 导入 portal store
import { usePortalStore } from '~/stores/portal'

// 2. 使用 Composable
const {
  isApiReady,
  player,
  playerApi: freedoApiRef,
  clickedObject,
  exitWorld,
  enterWorld,
} = useFreedo()

// 使用 portal store
const portalStore = usePortalStore()
const layer = ref('project')
// const layer=ref('section')
// const layer=ref('dataInfo')
const portalId = ref('')
const sectionMap = ref<Map<string, string>>(new Map())
const initDict = async () => {
  const dictRes = await getDict({ code: 'project_section' })
  if (dictRes.data && dictRes.data.project_section) {
    dictRes.data.project_section.forEach(
      (item: { code: string; 'zh-CN': string }) => {
        if (item['zh-CN'] != '信息化标')
          sectionMap.value.set(item.code, item['zh-CN'])
      }
    )
  }
}

// ClickItem 接口定义应在此处，确保只有一个
interface ClickItem {
  subProjectId: string
  portalId: string
  portalTitle: string
  siteId: string
  position?: number[]
  target?: number[]
  duration?: number
}
const legend = ref([
  {
    title: '安全隐患',
    children: [
      { title: '已整改', icon: 'legend1' },
      { title: '未整改', icon: 'legend1' },
    ],
  },
  {
    title: '危险分布图',
    children: [
      { title: '重大', icon: 'legend1' },
      { title: '较大', icon: 'legend1' },
      { title: '一般', icon: 'legend1' },
      { title: '低', icon: 'legend1' },
    ],
  },
])

const isShowBack = ref(false) // 初始不显示返回，当进入子场景或飞到点位时设为 true
const isClickItem = ref<ClickItem | null>(null) // 存储当前点击/激活的3D对象信息
const detailData = ref({
  id: '1933359300734881794',
  taskId: '111a66a7-4803-11f0-b7a1-0242ac150012',
  taskName: '已完成',
  firstTaskKey: 'UserTask_0',
  preTaskKey: 'UserTask_4',
  taskKey: 'UserTask_4',
  taskAsignee: 'huangjun',
  taskAsigneeName: '已完成',
  lastApproveDate: '已完成',
  processInstanceId: 'd9430dea-4802-11f0-b7a1-0242ac150012',
  hazardNumber: 'SJ-J004',
  name: '测试大屏',
  description:
    '测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏',
  level: '0',
  type: '',
  typeChild: '',
  projectPosition: '1909954237236776962',
  constructionArea: '测试大屏',
  requirement:
    '测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏测试大屏',
  deadline: '2025-06-01 00:00:00',
  reportDate: '2025-06-13 11:01:15',
  reporter: 'huangjun',
  reporterName: '黄俊',
  reporterOrgName: null,
  phone: null,
  source: 'week_inspection',
  acceptancePerson: 'huangjun',
  acceptancePersonName: '黄俊',
  rectificationStatus: '-1',
  periodStatus: 'overdue',
  periodStatusDesc: '已逾期',
  measure:
    '整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施整改措施',
  measureDate: '2025-06-02 00:00:00',
  measureFileUpload: '7ED58DF1-9C3D-45CC-8FB9-696D6F357FAE',
  fileUpload: 'A1B71575-7069-413A-917C-2AB392A6B2BE',
  dangerSource: '1930463413406797825',
  dangerSourceName: null,
  processState: 1,
  createBy: 'huangjun',
  isPass: 1,
  needSupervision: 1,
  sectionId: '',
  lng: null,
  lat: null,
  height: null,
  deadLineDays: 5,
})

const backFullScene = async () => {
  console.log('返回全景')
  if (isApiReady.value) {
    try {
      layer.value = 'project'
      portalId.value = ''
      isShowBack.value = false
      await exitWorld() // 假设 exitWorld() 会重置到主视角
      console.log('Exited world / Reset view successful')
      isClickItem.value = null // 清除点击项状态
      isShowBack.value = false
    } catch (error) {
      console.error('Failed to exit world / reset view:', error)
    }
  } else {
    console.warn('Freedo API not ready for backFullScene.')
    isClickItem.value = null // 即使API未就绪，也应清除状态
    isShowBack.value = false
  }
}

// 初始的 disableActions，现在依赖 Freedo API
const initialSetup = async () => {
  try {
    await enterWorld()
    console.log('Entered world successfully during initial setup.')
  } catch (error) {
    console.error('Failed to enter world during initial setup:', error)
  }
}
const hazardList = ref([])
/**
 * @description 获取安全隐患管理列表
 */
const fetchData = () => {
  hazardPage({ page: 1, size: 999 }).then((res) => {
    hazardList.value = res.data.records || []
    hazardList.value.forEach(async (item: any) => {
      const { lng, lat, height } = item
      if (item.lng && item.lat && item.height) {
        console.log('🚀 ~ hazardList.value.forEach ~ item:', [lng, lat, height])

        console.log('🚀 ~ gcs2pcs:', freedoApiRef.value.coord.gcs2pcs)
        let coor = await freedoApiRef.value.coord.gcs2pcs([lng, lat, height], 3)
        console.log('🚀 ~ hazardList.value.forEach ~ coor:', coor)

        //   let coor = await freedoApiRef.value.coord.gcs2pcs(
        //     [113.9825982455081, 30.297492106590411],
        //     1,
        //     (res) => {
        //       console.log(res)
        //     }
        //   )
        //   console.log(coor, 'coor')
      }
    })
    if (hazardList.value.length > 0) {
      const clickMarker: MarkerObject = {
        id: `click-marker-aq`,
        groupId: 'clickedMarkers',
        coordinate: [1549.7342529296875, -403.4830322265625, 768.41845703125],
        coordinateType: 0, // 假设是世界坐标
        imagePath: '/images/video-bg.png', // 使用一个默认或显眼的图标
        imageSize: [30, 30],
        anchors: [-15, 30],
        fixedSize: true,
        text: '这是安全管理隐患标点',
        fontSize: 14,
        fontColor: [1.0, 1.0, 0.0, 1.0], // 黄色
        textBackgroundColor: [0.1, 0.1, 0.1, 0.7],
        showLine: false,
        range: [1, 100000],
      }
      freedoApiRef.value.marker.add(clickMarker)
    }
  })
}
onMounted(async () => {
  await initDict()
  watch(
    isApiReady,
    async (ready) => {
      if (ready) {
        // await initialSetup() // 执行初始设置，例如禁用操作
        fetchData()

        // 新增：监听场景点击以添加标记
        watch(
          clickedObject,
          (newClickedInfo) => {
            console.log(
              ' watch(clickedObject============>',
              clickedObject.value,
              newClickedInfo
            )
            if (
              clickedObject.value?.PointName &&
              Array.from(sectionMap.value.values()).indexOf(
                clickedObject.value.PointName
              ) != -1
            ) {
              const index = Array.from(sectionMap.value.values()).findIndex(
                (x) => x === clickedObject.value?.PointName
              )
              // 点击的是标段标签
              layer.value = 'section'
              portalId.value = Array.from(sectionMap.value.keys())[index]
              isShowBack.value = true
            } else if (clickedObject.value?.Id == 'click-marker-aq') {
              // 如果点击的是隐患数据标签
              layer.value = 'dataInfo'
              // detailData.value = hazardList.value.find((x: any) => x.id == '00')
              detailData.value = hazardList.value[0]
              isShowBack.value = true
            }
            if (newClickedInfo?.point && freedoApiRef.value?.marker?.add) {
              return

              // ----

              // ------
            } else if (newClickedInfo?.point) {
              console.warn(
                'Security/index.vue: Click detected, but marker API not ready or add function missing.'
              )
            }
          },
          { deep: true }
        ) // 使用 deep watch 以防 point 数组内部变化不被察觉（虽然通常 ref 的 .value 变化会触发）
      } else {
        console.log(
          'Freedo API (isApiReady) not yet true in Security/index.vue, waiting...'
        )
      }
    },
    { immediate: true }
  )
})
onBeforeUnmount(() => {
  freedoApiRef.value.marker.delete(['click-marker-aq'], () => {
    console.log('删除成功')
  })
})
</script>
<template>
  <PageLayout
    :showBackButton="!!isShowBack"
    :showLegendButton="true"
    :backTitle="isClickItem ? isClickItem.portalTitle : '返回全景'"
    @back="backFullScene"
    :legend="legend"
  >
    <template #left>
      <Card
        v-if="layer != 'dataInfo'"
        title="安全教育开展情况"
        style="height: 55%"
      >
        <SafetyEducation :layer="layer" :portalId="portalId" />
      </Card>
      <Card v-if="layer != 'dataInfo'" title="危险源分布情况" style="flex: 1">
        <HazardSourceDistribution :layer="layer" :portalId="portalId" />
      </Card>
      <Card title="信息流程" v-if="layer == 'dataInfo'" style="height: 100%">
        <SafetyFlowDetail :layer="layer" :detailData="detailData" />
      </Card>
    </template>

    <template #right>
      <Card
        v-if="layer != 'dataInfo'"
        title="安全检查"
        :style="{ height: layer == 'project' ? '50%' : '30%' }"
      >
        <SecurityInspection :layer="layer" :portalId="portalId" />
      </Card>
      <Card v-if="layer != 'dataInfo'" title="安全隐患" style="flex: 1">
        <SecurityHiddenDanger :layer="layer" :portalId="portalId" />
      </Card>
      <Card title="整改前" style="height: 60%" v-if="layer == 'dataInfo'">
        <SafetyBeforeRectification :detailData="detailData" />
      </Card>
      <Card title="整改后" style="height: 40%" v-if="layer == 'dataInfo'">
        <SafetyAfterRectification :detailData="detailData" />
      </Card>
    </template>
  </PageLayout>
</template>

<style lang="scss" scoped></style>
