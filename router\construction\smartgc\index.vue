<script lang="ts" setup>
import SubMenu from '~/router/layout/SubMenu.vue';

const base = '/construction/build'
const options: any[] = [
  {
    name: '安全管理',
    path: 'secure',
  },
  {
    name: '质量管理',
    path: 'quality',
  },
  {
    name: '投资管理',
    path: 'investment',
  },
  {
    name: '进度管理',
    icon: 'progress',
    path: 'progress',
  },
  {
    name: '文件管理',
    path: 'file',
  },
]
</script>

<template>
  <SubMenu :base="base" :options="options" />
</template>