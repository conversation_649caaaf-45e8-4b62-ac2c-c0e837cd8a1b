<template>
  <div class="main-content">
    <div class="header" @click="handleClick">
      <h1>{{ title }}</h1>
    </div>

    <div class="content">
      <div id="player"></div>

      <div class="status-panel" style="overflow: auto;">
        <p><strong>连接状态:</strong> <span :class="connected ? 'status-connected' : 'status-disconnected'">{{ statusText }}</span></p>
        <p><strong>服务器:</strong> {{ serverAddress }}</p>
        
        <!-- 鼠标事件信息展示区 -->
        <div class="mouse-info-panel">
          <!-- API测试按钮 -->
          <div class="api-test-panel" v-if="apiReady">
            <h3>API测试</h3>
            <div class="button-group">
              <!-- 获取相机位置：Get
              设置相机位置：Set
              锁定相机交互范围：LockByBBox
              解锁相机交互范围：UnLock
              获取两点的欧拉角：GetEulerAngle
              设置相机位置（通过数组参数）：Set
              设置相机位置（通过对象参数）：Set
              通过观察点设置相机位置：LookAt
              设置相机环绕：FlyAround
              进入物体观察模式：LookAtBBox
              进入自由交互模式：LookAtBBox
              进入世界：EnterWorld
              退出世界：ExitWorld -->
              <button @click="EnterWorld">进入世界</button>
              <button @click="ExitWorld">退出世界</button>  

              <button @click="getCameraPosition">获取相机位置</button>
              <button @click="setCameraPosition">设置相机位置</button>
            </div>
            <div class="api-result">
              <p><strong>返回结果:</strong></p>
              <pre>{{ apiResult }}</pre>
            </div>
          </div>

          <h3>鼠标事件监控</h3>
          <div class="mouse-events">
            <p><strong>事件类型:</strong> {{ lastEventType }}</p>
            <p><strong>相对坐标:</strong> X: {{ mousePos.x }}, Y: {{ mousePos.y }}</p>
            <p><strong>世界坐标:</strong> {{ worldPos }}</p>
            <p><strong>点击次数:</strong> {{ clickCount }}</p>
          </div>
          <div class="last-event">
            <p><strong>最新事件:</strong></p>
            <pre>{{ lastEvent }}</pre>
          </div>
        </div>
      </div>
    </div> 
  </div>
</template>

<script lang="ts" setup>
// 使用全局window对象上的DigitalTwinPlayer
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 定义API接口
interface CameraInfo {
  x: number;
  y: number;
  z: number;
  pitch: number;
  yaw: number;
  roll: number;
  camera?: number[];
  position?: number[]; // 保留兼容性
  rotation?: number[]; // 保留兼容性
  target?: number[];
}

interface CameraAPI {
  get(): Promise<CameraInfo>;
  set(x: number, y: number, z: number, pitch: number, yaw: number, flyTime: number): Promise<void>;
  flyTo(options: { position?: number[], rotation?: number[], target?: number[], duration?: number }): Promise<void>;
  getForwardVector(): number[];
  reset(): Promise<void>;
  enterWorld(): Promise<void>;
  exitWorld(): Promise<void>;
}

interface Settings {
  getMousePickMask(): any;
  setMousePickMask(mask: number): void;
  setEnableCameraMovingEvent?: (enable: boolean) => void;
  setPickMode?: (mode: string | number) => void;
  getPickMode?: () => string | number;
  setInteractionMode?: (mode: string | number) => void;
  getInteractionMode?: () => string | number;
}

interface ApiType {
  camera?: CameraAPI;
  settings?: Settings;
  destroy?: () => void;
}

// 声明全局DigitalTwinPlayer类型
declare global {
  interface Window {
    DigitalTwinPlayer?: any;
  }
}

// 状态变量
const api = ref<ApiType | null>(null)
const player = ref<any | null>(null)
const title = ref('飞渡云渲染演示')
const connected = ref(false)
const statusText = ref('未连接')
const serverAddress = ref('')

// 鼠标事件监控相关状态
const lastEventType = ref('无')
const mousePos = ref({ x: 0, y: 0 })
const worldPos = ref('未知')
const clickCount = ref(0)
const lastEvent = ref('{}')

// 点击位置相关状态
interface ClickPosition {
  x: number;
  y: number;
  z: number;
}

const lastClickPosition = ref<ClickPosition | null>(null)
const enableClickToSetCamera = ref(false)

// API测试相关状态
const apiReady = ref(false)
const apiResult = ref('未执行操作')

const handleClick = () => {
  console.log('handleClick')
  alert('点击事件测试')
  // 尝试直接使用相机API进行操作，测试API是否响应
  if (api.value && api.value.camera) {
    try {
      const cameraInfo = api.value.camera.get()
      apiResult.value = '相机位置检测成功：' + JSON.stringify(cameraInfo, null, 2)
    } catch (e) {
      apiResult.value = '相机API调用错误：' + e.message
    }
  } else {
    apiResult.value = 'API未就绪，点击事件生效但API不可用'
  }
}

// 回调函数
function onApiReady() {
  console.log('此时可以调API了')
  connected.value = true
  statusText.value = '已连接'
  apiReady.value = true
  
  // 设置鼠标拾取掩码 - 这是解决鼠标交互问题的关键步骤
  if (api.value && api.value.settings) {
    // 7: 启用点击、移动、悬停全部功能 (1=点击, 2=移动, 4=悬停)
    api.value.settings.setMousePickMask(7)
    console.log('已启用鼠标拾取功能')
    
    // 启用相机移动事件监听
    api.value.settings.setEnableCameraMovingEvent(true)
    console.log('已启用相机移动事件监听')
    
    // 增加额外的交互设置
    try {
      // 设置鼠标拾取模式
      if (api.value.settings.setPickMode) {
        api.value.settings.setPickMode(1)  // 1=默认拾取模式
        console.log('设置鼠标拾取模式成功')
      }

      // 设置交互模式
      if (api.value.settings.setInteractionMode) {
        api.value.settings.setInteractionMode(1)  // 1=默认交互模式
        console.log('设置交互模式成功')
      }
    } catch (e) {
      console.warn('设置额外交互参数时出错:', e.message)
    }
  }
  
  // 用户可以在这里调用API操作场景
  if (api.value) {
    console.log('API对象已就绪:', api.value)
    // 输出相机位置用于调试
    if (api.value.camera) {
      const cameraInfo = api.value.camera.get()
      console.log('当前相机位置:', cameraInfo)
    }
  }
}

function onApiEvent(e) {
  console.log('监听各类交互事件:', e)
  
  // 所有事件都更新到UI显示
  lastEventType.value = e.eventtype || '未知事件'
  lastEvent.value = JSON.stringify(e, null, 2)
  
  // 更新鼠标坐标
  if (e.screenpos) {
    mousePos.value = { x: e.screenpos[0], y: e.screenpos[1] }
  }
  
  // 更新世界坐标
  if (e.hitpos) {
    worldPos.value = `X: ${e.hitpos[0].toFixed(2)}, Y: ${e.hitpos[1].toFixed(2)}, Z: ${e.hitpos[2].toFixed(2)}`
    // 存储最近点击的位置，便于设置相机位置
    lastClickPosition.value = {
      x: e.hitpos[0],
      y: e.hitpos[1],
      z: e.hitpos[2]
    }
  }
  
  // 记录点击次数
  if (e.eventtype === 'LeftMouseButtonClick') {
    clickCount.value++
    console.log('监测到鼠标点击事件:', e)
    
    // 如果启用了点击设置相机功能并且有点击坐标
    if (enableClickToSetCamera.value && e.hitpos) {
      moveCameraToClickPosition(e.hitpos)
    }
  }
  
  // 特定类型事件的处理
  if (e.eventtype === 'LeftMouseButtonClick' && e.Type === 'TileLayer') {
    console.log('监听图层点击事件:', e)
  }
}

function onApiLog(s, nnl) {
  console.log('API日志:', s, nnl)
}

function onVideoLoaded() {
  console.log('视频已加载')
}

function onConnectionClosed() {
  console.log('连接已关闭')
  connected.value = false
  statusText.value = '已断开'
}

// 连接飞渡云渲染
function connectToFreedo() {
  // 从环境变量或配置获取服务器地址
  const host = import.meta.env.CVE_FREEDO_HOST || '*************:8080'
  serverAddress.value = host
  
  console.log('正在连接到服务器:', host)
  
  // 初始化配置参数
  const options = {
    domId: 'player',
    apiOptions: {
      onReady: onApiReady,
      onEvent: onApiEvent,
      // 添加互动控制选项
      enableInteractions: true,
      // 设置完全手动控制模式
      controlMode: 'manual',
      // 禁用默认的UI交互
      disableDefaultInteraction: false
    },
    // 添加自定义鼠标事件处理
    actionEventHander: {
      'onmousedown': e => { 
        console.log(`[MouseDn] button: ${e.button}, pos: ${e.x}, ${e.y}`); 
        lastEvent.value = JSON.stringify({type: 'mousedown', button: e.button, x: e.x, y: e.y}, null, 2);
      },
      'onmouseup': e => { 
        console.log(`[MouseUp] button: ${e.button}, pos: ${e.x}, ${e.y}`);
        lastEvent.value = JSON.stringify({type: 'mouseup', button: e.button, x: e.x, y: e.y}, null, 2);
      },
      'onmousemove': e => { 
        // 鼠标移动事件太频繁，不记录到lastEvent，避免界面更新过于频繁
        console.log(`[MouseMove] pos: ${e.x}, ${e.y}`); 
      },
    },
    ui: {
      startupInfo: true,
      statusButton: true,
      mouseControl: true,   // 启用鼠标控制
      deviceMotion: false   // 是否启用设备运动控制
    },
    events: {
      onVideoLoaded: onVideoLoaded,
      onConnClose: onConnectionClosed
    },
    // 交互控制
    keyEventTarget: 'document',  // 修改为document而不是none
    // 可选参数
    iid: '2574133196358'  // 使用用户提供的IID参数
  }
  
  try {
    // 创建飞渡实例 - 使用全局对象
    player.value = new window.DigitalTwinPlayer(host, options)
    // 获取API对象
    api.value = player.value.getAPI()
    console.log('飞渡实例初始化完成')
  } catch (e) {
    console.error('连接飞渡云渲染失败:', e.message)
    statusText.value = '连接失败'
  }
}

// API测试函数
function getCameraPosition() {
  try {
    if (api.value && api.value.camera) {
      apiResult.value = '正在获取相机位置...'
      api.value.camera.get().then((cameraInfo: CameraInfo) => {
        // 提取相机数据并格式化显示
        apiResult.value = `相机信息：\n位置: [${cameraInfo.x}, ${cameraInfo.y}, ${cameraInfo.z}]\n旋转角度: [俯仰角:${cameraInfo.pitch}, 偏航角:${cameraInfo.yaw}, 滚转角:${cameraInfo.roll}]`
        
        // 添加改回这个点的代码提示
        const resetCode = `fdapi.camera.set(${cameraInfo.x}, ${cameraInfo.y}, ${cameraInfo.z}, ${cameraInfo.pitch}, ${cameraInfo.yaw}, 0);`
        apiResult.value += `\n\n使用以下代码可以设置到该位置:\n${resetCode}`
        
        console.log('获取相机位置成功:', cameraInfo)
      }).catch((error: Error) => {
        apiResult.value = `获取相机位置失败: ${error.message}`
        console.error('获取相机位置错误:', error)
      })
    } else {
      apiResult.value = '无法获取相机位置，API未就绪'
    }
  } catch (e: unknown) {
    const error = e as Error
    apiResult.value = `错误: ${error.message}`
  }
}

function setCameraPosition() {
  try {
    if (api.value && api.value.camera) {
      // 设置示例坐标
      const x = 492543.975
      const y = 2492194.178125
      const z = 19.509531
      const pitch = -27.995653
      const yaw = -44.265804
      const flyTime = 0 // 瞬间切换，设为0
      
      apiResult.value = '正在设置相机位置...'
      
      // 调用相机设置API
      api.value.camera.set(x, y, z, pitch, yaw, flyTime).then(() => {
        apiResult.value = `相机位置设置成功\n位置: [${x}, ${y}, ${z}]\npitch: ${pitch}\nyaw: ${yaw}`
      }).catch((error: Error) => {
        apiResult.value = `设置相机位置失败: ${error.message}`
        console.error('设置相机位置错误:', error)
      })
    } else {
      apiResult.value = '无法设置相机位置，API未就绪'
    }
  } catch (e: unknown) {
    const error = e as Error
    apiResult.value = `错误: ${error.message}`
  }
}

// 进入世界函数
function EnterWorld() {
  try {
    if (api.value && api.value.camera) {
      apiResult.value = '正在进入世界...'
      
      api.value.camera.enterWorld().then(() => {
        apiResult.value = '进入世界成功'
      }).catch((error: Error) => {
        apiResult.value = `进入世界失败: ${error.message}`
        console.error('进入世界错误:', error)
      })
    } else {
      apiResult.value = '无法进入世界，API未就绪'
    }
  } catch (e: unknown) {
    const error = e as Error
    apiResult.value = `错误: ${error.message}`
  }
}

// 退出世界函数
function ExitWorld() {
  try {
    if (api.value && api.value.camera) {
      apiResult.value = '正在退出世界...'
      
      api.value.camera.exitWorld().then(() => {
        apiResult.value = '退出世界成功'
      }).catch((error: Error) => {
        apiResult.value = `退出世界失败: ${error.message}`
        console.error('退出世界错误:', error)
      })
    } else {
      apiResult.value = '无法退出世界，API未就绪'
    }
  } catch (e: unknown) {
    const error = e as Error
    apiResult.value = `错误: ${error.message}`
  }
}

// 生命周期钩子
onMounted(() => {
  connectToFreedo()
})

onBeforeUnmount(() => {
  // 释放资源
  if (api.value && api.value.destroy) {
    api.value.destroy()
    console.log('飞渡资源已释放')
  }
})
</script>

<style lang="scss" scoped>
.main-content {
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: all;
  display: flex;
  flex-direction: column;

  .header {
    padding: 10px;
    background: rgba(0, 0, 0, 0.1);
    cursor: pointer;
    z-index: 10;
    position: relative;
  }
}

.content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  position: relative;
  overflow: hidden;

  #player {
    width: 70%;
    height: 100%;
    position: relative;
    overflow: hidden;
    pointer-events: auto; /* 确保事件能被正确捕获 */
    z-index: 1; /* 设置适当的层级 */
  }

  .status-panel {
    flex: 1;
    margin-left: 10px;
    padding: 20px;
    background-color: #90b9f8;
    text-align: left;
    font-size: 18px;
    position: relative;
    overflow: auto;
    .status-connected {
      color: #0afa32;
      font-weight: bold;
    }
    .status-disconnected {
      color: #f56c6c;
      font-weight: bold;
    }
  }
}

.active-btn {
  background-color: #67c23a !important;
  color: white !important;
  font-weight: bold;
}

</style>