import type { InjectionKey, ShallowRef, Readonly, Ref } from 'vue'

// API 类型定义
export interface FreedoCameraAPI {
  flyTo: (options: { position?: number[], rotation?: number[], target?: number[], duration?: number }) => Promise<void>;
  set: (x: number, y: number, z: number, pitch: number, yaw: number, flyTime: number) => Promise<void>;
  get: () => Promise<any>;
  enterWorld: () => Promise<void>;
  exitWorld: () => Promise<void>;
  playAnimation: (id: string) => Promise<void>;
  getAnimationList: () => Promise<{data: any[]}>;
  getAnimationImage: (name: string) => Promise<{image: string}>;
}

export interface FreedoSettingsAPI {
  setMousePickMask?: (mask: number) => void;
  getMousePickMask?: () => any;
  setEnableCameraMovingEvent?: (enable: boolean) => void;
}

// 定义 Marker 相关接口
export interface MarkerObject {
  id: string;
  coordinate: number[]; // [x, y, z] 或 [x, y] (如果 autoHeight=true)
  coordinateType?: 0 | 1; // 0: 投影坐标系, 1: 经纬度空间坐标系
  text?: string;
  imagePath?: string;
  imageSize?: number[]; // [width, height]
  hoverImagePath?: string;
  hoverImageSize?: number[];
  fixedSize?: boolean;
  anchors?: number[];
  autoHeight?: boolean;
  fontSize?: number;
  fontColor?: number[];
  textBackgroundColor?: number[];
  showLine?: boolean;
  lineColor?: number[];
  range?: number[];
  groupId?: string;
}

export interface FreedoMarkerAPI {
  add: (markerOrMarkers: MarkerObject | MarkerObject[]) => Promise<void> | void;
  clear: () => void;
  remove: (idOrIds: string | string[]) => void;
  update: (marker: Partial<MarkerObject> & { id: string }) => void;
  focus: (id: string, distance?: number, rotation?: number) => void;
  get: (id: string) => MarkerObject | null;
}

export interface FreedoInfoTreeAPI {
  get: () => Promise<{infotree: any[]}>;
  highlight: (id: string, highlight: boolean) => Promise<void>;
  setVisible: (id: string, visible: boolean) => Promise<void>;
}

// 点击对象信息类型
export interface ClickedObjectInfo {
  point: number[] | null; // 世界坐标 [x, y, z]
  objectId: string | null;
  objectName?: string | null;
  position?: number[] | null;
  hitPoint?: number[] | null;
  timestamp?: number;
}

// 主API类型
export interface FreedoApiType {
  camera?: FreedoCameraAPI;
  settings?: FreedoSettingsAPI;
  infoTree?: FreedoInfoTreeAPI;
  marker?: FreedoMarkerAPI;
  reset?: (flags: number) => Promise<void>;
  destroy?: () => void;
}

// Injection Keys
export const FreedoApiKey: InjectionKey<Readonly<ShallowRef<FreedoApiType | null>>> = Symbol('FreedoApiKey')
export const FreedoPlayerKey: InjectionKey<Readonly<ShallowRef<any | null>>> = Symbol('FreedoPlayerKey')
export const FreedoReadyKey: InjectionKey<Readonly<ShallowRef<boolean>>> = Symbol('FreedoReadyKey')
export const FreedoClickedObjectKey: InjectionKey<Ref<ClickedObjectInfo | null>> = Symbol('FreedoClickedObjectKey')