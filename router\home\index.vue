<script lang="ts" setup>
// import { useRoute } from 'vue-router'

import { ref, onMounted } from 'vue'
import { getTs } from '~/utils/app'

function getTime() {
  getTs().then((res) => {
    console.log(res, '时间戳接口')
  })
}

// 在组件挂载和卸载时，添加和移除监听全屏状态变化的事件
onMounted(() => {
  // 程序启动后就调用一次，目的是提前让渲染程序在后台启动
  getTime()
})
</script>

<template>
  <div class="home-page"></div>
</template>

<style lang="scss" scoped>
.home-page {
  width: 100%;
  height: 100%;
  background: #fff;
}
</style>
