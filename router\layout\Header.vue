<script lang="ts" setup>
import { onUnmounted, ref } from 'vue'
import Menu from '~/router/layout/Menu.vue'
import dayjs from 'dayjs'
import { usePortalStore } from '~/stores'
const portalStore = usePortalStore()


const props = defineProps({
  menus: {
    type: Array,
    default: () => [
      {
        name: '建设总览',
        path: 'overview',
      },
      {
        name: '数智建管',
        path: 'build',
      },
      {
        name: '智慧工地',
        path: 'redsite',
      },
    ]
  }
})

const dts = ref(getDTS())
const timer = setInterval(updateDTS, 1000)

onUnmounted(clearTimer)

function clearTimer() {
  clearInterval(timer)
}
function getDTS() {
  return dayjs().format('YYYY-MM-DD HH:mm:ss')
}
function reset() {
  portalStore.updateMicroPortal({})

}
function updateDTS() {
  dts.value = getDTS()
}

</script>

<template>
  <div class="header-nav">
    <RouterLink class="title" to="/construction/overview" @click="reset" />
    <Menu
      base="/construction"
      class="dic-menu"
      dir="horizontal"
      item-class="nav-item"
      :options="menus"
      topic=""
    />
    <div class="hearder-bgc"></div>
    <div class="datetime">{{ dts }}</div>
  </div>
</template>

<style lang="scss" scoped>
.header-nav {
  align-items: center;
  box-sizing: border-box;
  display: block;
  position: absolute;
  top: 0;
  width: 100%;
  height: 88px;
  z-index: 2;
  .hearder-bgc {
    width: 100%;
    height: 100%;
    inset: 0;
    position: absolute;
    z-index: 0;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-image: url('~/assets/images/common/header-nav.png');
  }
}

.title {
  cursor: pointer;
  height: 86px;
  pointer-events: all;
  text-decoration: none;
  display: block;
  width: 38%;
  z-index: 1;
  position: absolute;
}

.datetime {
  box-sizing: border-box;
  font-size: 20px;
  font-family: customerTime;
  display: inline-block;
  line-height: 74px;
  position: absolute;
  right: 0;
  top: 0;
  width: 264px;
}

.dic-menu {
  position: absolute;
  left: 38%;
  top: 15px;
  z-index: 1;
}

@font-face {
  font-family: customerTime;
  src: url('@assets/fonts/PangMenZhengdaoTitle.TTF');
}
</style>
