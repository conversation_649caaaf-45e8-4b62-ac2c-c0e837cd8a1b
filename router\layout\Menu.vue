<script lang="ts" setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import type { RouteMenuProps, RouterOption } from './types'

const props = withDefaults(defineProps<RouteMenuProps>(), {
  base: '/',
  dir: 'vertical',
  iconSize: 48,
  itemClass: '',
  mode: 'path',
  options: () => [],
  size: 72,
  topic: '',
})
const route = useRoute()
const style = computed(() => {
  if (props.itemClass !== '') {
    return {}
  }
  return {
    height: props.size + 'px',
    width: props.size + 'px',
  }
})

function resolvePath(path: string) {
  let url = props.base
  if (props.topic) {
    url += '/' + props.topic
  }
  if (path) {
    url += '/' + path
  }
  // 替换多个斜杠为单个斜杠，并移除末尾的斜杠（如果不是根路径）
  url = url.replace(/\/+/g, '/')
  if (url !== '/' && url.endsWith('/')) {
    url = url.slice(0, -1)
  }
  return url
}
function resolveQuery(path: string) {

  if (route.query[props.topic] === path) {
    return props.base
  }
  return `${props.base}?${props.topic}=${path}`
}
function resolveURL(option: RouterOption) {
  return props.mode === 'path'
    ? resolvePath(option.path)
    : resolveQuery(option.path)
}
const getCanonicalPath = (optionPathSegment: string): string => {
  const normBase = (props.base === '/' || !props.base.endsWith('/')) ? props.base : props.base.slice(0, -1);
  
  let parts: string[] = [];
  if (normBase !== '/') {
      parts.push(normBase.startsWith('/') ? normBase.substring(1) : normBase);
  }
  if (props.topic) {
      parts.push(props.topic);
  }
  if (optionPathSegment) { 
      parts.push(optionPathSegment);
  }

  let fullPath = '/' + parts.join('/');
  fullPath = fullPath.replace(/\/+/g, '/');
  if (fullPath === "" && normBase === '/' && !props.topic && !optionPathSegment) {
    fullPath = '/';
  }
  
  return fullPath;
};

function resolveState(optionPathSegment: string) {
  let className = 'nav-item';

  if (props.mode === 'path') {
    const canonicalTargetURL = getCanonicalPath(optionPathSegment);

    // 将菜单项路径按'/'分割，判断是否为父菜单
    const pathSegments = optionPathSegment.split('/');
    
    let sectionURL = '';
    if (pathSegments.length > 1) {
      // 如果路径包含'/'，则认为是父菜单，其基础路径为去掉最后一部分
      const sectionPath = pathSegments.slice(0, -1).join('/');
      sectionURL = getCanonicalPath(sectionPath);
    } else {
      // 否则，认为是直接链接，基础路径就是其完整路径
      sectionURL = canonicalTargetURL;
    }

    // 如果当前路由以菜单项的基础路径开头，则激活它
    // 增加 sectionURL !== '/' 的判断，防止根路径'/'匹配所有路由
    if (route.path.startsWith(sectionURL) && sectionURL !== '/') {
      className += ' active';
    } else if (route.path === canonicalTargetURL) {
      // 为根路径'/'或不含子菜单的项提供精确匹配作为后备
      className += ' active';
    }

  } else if (props.mode === 'query') {
    if (route.query[props.topic] === optionPathSegment) {
      className += ' active';
    }
  }
  return className;
}
</script>

<template>
  <div class="route-menu" :class="[dir, mode]">
    <RouterLink v-for="option in options" :class="resolveState(option.path)" :key="option.name" :title="option.name"
      :to="resolveURL(option)" :style="style">
      <SvgIcon v-if="option.icon" colr="white" :name="option.icon" :size="iconSize" />
      <span>{{ option.name }}</span>
    </RouterLink>
  </div>
</template>

<style lang="scss" scoped>
.route-menu {
  align-items: center;
  display: flex;
  justify-content: center;
  pointer-events: none;
  background-size: 100% 100%;
  width: 680px;
}

.nav-item {
  font-family: PangMenZhengdao;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  pointer-events: all;
  text-decoration: none;
  color: white;
  background-image: url('~/assets/images/header/nav-item.svg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  font-size: 22px;
  font-weight: bold;
  height: 38px;
  letter-spacing: 3px;
  width: 183px;
  transition: filter 0.2s ease-in-out, transform 0.1s ease;

  &:not(.active):hover {
    filter: brightness(1.5);
  }

  &:active {
    transform: scale(0.95);
  }

  &.active {
    background-image: url('~/assets/images/header/nav-item-active.svg');
  }
}
</style>
