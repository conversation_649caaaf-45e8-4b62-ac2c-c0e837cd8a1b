<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, ref } from 'vue'

const props = withDefaults(defineProps<{
  options: any[];
  modelValue?: string | number;
}>(), {
  options: () => [
    { name: '大坝', objectName: '大坝', playName: '混凝土拱坝导览', imgName: 'dam' },
    { name: '主电站地下厂房', objectName: '主副厂房', playName: '主电站地下厂房', imgName: 'factory' },
    { name: '生态电站', objectName: '生态电站', playName: '生态电站', imgName: 'ecological' },
    { name: '集鱼系统', objectName: '集鱼系统', playName: '集鱼系统', imgName: 'fish' },
    { name: '水垫塘', objectName: '水垫塘', playName: '水垫塘导览', imgName: 'pond' },
  ],
  modelValue: ''
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'click'])

// 当前选中的项
const activeItem = ref(props.modelValue)

// 处理点击事件
const handleClick = (option: any) => {
  activeItem.value = option.name || option.id || option
  emit('update:modelValue', activeItem.value)
  emit('click', option)
}

// 检查项是否激活
const isActive = (option: any) => {
  const optionValue = option.name || option.id || option
  return activeItem.value === optionValue
}

// 动态获取图片URL
const getImageUrl = (imgName: string, isActive: boolean) => {
  const suffix = isActive ? '-active' : ''
  return new URL(`../../assets/images/button/${imgName}${suffix}.svg`, import.meta.url).href
}
</script>

<template>
  <div class="submenu" @contextmenu.prevent @selectstart.prevent @dragstart.prevent>
    <div
      v-for="option in options"
      class="button-item"
      :class="{ 'active': isActive(option) }"
      :key="option.name"
      :title="option.name"
      @click="handleClick(option)"
      @contextmenu.prevent
      @selectstart.prevent
      @dragstart.prevent
    >
      <div class="icon-container">
        <img
          :src="getImageUrl(option.imgName, isActive(option))"
          :alt="option.name"
          class="icon-image"
          @contextmenu.prevent
          @selectstart.prevent
          @dragstart.prevent
        />
      </div>
      <span class="item-name">{{ option.name }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// SCSS Mixin - 禁用用户交互
@mixin disable-user-interaction {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

.submenu {
  pointer-events: all;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 48px;
  gap: 20px;
  z-index: 9;
  @include disable-user-interaction;
}

.button-item {
  width: 100px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  @include disable-user-interaction;

  .icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 64px;
    height: 64px;
    margin-bottom: 4px;
    transition: all 0.3s ease;
    @include disable-user-interaction;
  }

  .icon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    @include disable-user-interaction;
  }

  .item-name {
    font-family: PangMenZhengdao;
    font-size: 14px;
    color: #D0D0D0;
    letter-spacing: 1px;
    text-align: center;
    transition: color 0.3s ease;
    @include disable-user-interaction;
  }

  &:not(.active):hover {
    .item-name {
      color: #FFFFFF;
    }
  }

  &:active {
    transform: scale(0.95);
  }

  &.active {
    .item-name {
      color: #FFFFFF;
    }
  }
}
</style>