<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue'
import Header from './Header.vue'
import { useFreedo } from '../useFreedo'
import packageJson from '../../package.json'

const HEIGHT = Number(import.meta.env.CVE_WINDOW_HEIGHT) || 900
const WIDTH = Number(import.meta.env.CVE_WINDOW_WIDTH) || 1600

// 获取版本号
const version = packageJson.version

const scalable = ref({
  height: `${HEIGHT}px`,
  left: '0px',
  top: '0px',
  transform: `scale(1)`,
  width: `${WIDTH}px`,
})

// 使用更新后的useFreedo composable
const { 
  initFreedo, 
  freedoError,
  freedoStore,
  freedoPlayer
} = useFreedo()

// 基础状态
const loading = ref(false)
// 控制UI显示的状态
const showUI = ref(false)
// 连接超时时间（毫秒）
const CONNECTION_TIMEOUT = 3000
// 连接超时定时器
let connectionTimer: any = null

// 初始化飞渡模型
async function connectToFreedo() {
  const host = import.meta.env.CVE_FREEDO_HOST
  const useHttps = import.meta.env.CVE_FREEDO_USE_HTTPS === 'true'
  const iid = import.meta.env.CVE_FREEDO_IID
  console.log('开始连接飞渡云渲染 (Player.vue):', host, iid, useHttps )
  loading.value = true  
  
  try {
    await initFreedo({
      domId: 'player',
      host,
      useHttps,
      iid,
      onReady: onPlayerReady,
      onEvent: onPlayerEvent,
    })
    
    if (freedoError.value) {
      console.error('连接飞渡云渲染失败 (Player.vue):', freedoError.value)
      loading.value = false
      showUI.value = true
    }
  } catch (e) {
    console.error('连接飞渡云渲染失败 (Player.vue):', (e as Error).message)
    loading.value = false
    showUI.value = true
  }
}

function onPlayerEvent(event: any) {
  // 检查是否是鼠标点击事件
  if (event) {
    const eventType = event.eventtype;
    switch (eventType) {
      case "CameraTourFinished":
          console.log('触发事件类型：播放导览结束，eventType：' + eventType);
          showUI.value = true;
          // 初始化自动移动功能
          freedoStore.startAutoCameraMovementTimer();

          // 安全地退出全屏
          if (freedoPlayer.value.fullscreen) {
            freedoPlayer.value.fullscreen = false;
          }

          if (!freedoPlayer.value) {
            // 复位相机到初始位置
            freedoStore.freedoApi?.reset(4);
          }
          break;
      default:
          ""
    }
  }
}

async function onPlayerReady() {
  console.log('飞渡 API 准备就绪 (Player.vue)')
  loading.value = false

  // 清除连接超时定时器
  if (connectionTimer) {
    clearTimeout(connectionTimer);
    connectionTimer = null;
  }

  // 如果是首次进入，播放导览动画
  if (!freedoStore.hasPlayedIntro) {
    // 安全地进入全屏
    try {
      if (freedoPlayer.value && document.visibilityState === 'visible') {
        freedoPlayer.value.fullscreen = true;
      }
    } catch (error) {
      console.warn('进入全屏失败:', error);
    }
    // 播放导览动画
    const play =  await freedoStore.playFreedoAnimation('入场动画');
    if (play) {
      freedoStore.setHasPlayedIntro(true)
    }

  } else {
    // 如果已经播放过，直接显示UI
    showUI.value = true
  }
}

function calculate() {
  const xScale = window.innerWidth / WIDTH
  const yScale = window.innerHeight / HEIGHT

  if (xScale < yScale) {
    const offsetY = (window.innerHeight - HEIGHT * xScale) / 2
    scalable.value.left = '0px'
    scalable.value.top = `${offsetY}px`
    scalable.value.transform = `scale(${xScale})`
  } else {
    const offsetX = (window.innerWidth - WIDTH * yScale) / 2
    scalable.value.left = `${offsetX}px`
    scalable.value.top = '0px'
    scalable.value.transform = `scale(${yScale})`
  }
}

calculate()
onMounted(() => {
  window.addEventListener('resize', calculate)

  if (freedoStore.hasPlayedIntro) {
    showUI.value = true
  } else {
    // 设置连接超时定时器
    connectionTimer = window.setTimeout(() => {
      showUI.value = true
    }, CONNECTION_TIMEOUT)
  }

  connectToFreedo()

  setTimeout(() => {
    loading.value = false
  }, CONNECTION_TIMEOUT)
})

onUnmounted(() => {
  window.removeEventListener('resize', calculate)
  
  // 清除连接超时定时器
  connectionTimer && clearTimeout(connectionTimer)

  // 确保停止自动移动
  if (freedoStore.isAutoMoving) {
    freedoStore.stopAutoCameraMovement();
  } else {
    freedoStore.clearAutoCameraMovementTimer();
  }

  freedoStore.freedoApi?.destroy()
})
</script>

<template>
  <div 
    class="outlet" 
    v-loading="loading" 
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)" 
    :style="scalable"
  >
    <div id="player"></div>
    <template v-if="showUI">
      <div class="layout-top" @contextmenu.prevent @selectstart.prevent @dragstart.prevent>
        <Header />
      </div>
      <RouterView :key="$route.fullPath" />
    </template>
    <div class="version">{{ version }}</div>
  </div>
</template>

<style lang="scss" scoped>
// SCSS Mixin - 禁用用户交互
@mixin disable-user-interaction {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

.outlet {
  position: absolute;
  transform-origin: top left;
  z-index: 99;
  background: #1B1B1B;

  .version {
    position: fixed;
    z-index: 9999;
    display: none;
  }
  
  #player {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 0;
  }

  @keyframes slideInFromTop {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

   .layout-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 138px;
    background: linear-gradient( 180deg, #000000 0%, rgba(0,0,0,0) 100%);
    animation: slideInFromTop 0.8s ease-out;
  }
  
  .api-error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: #f56c6c;
    padding: 20px;
    border-radius: 4px;
    z-index: 10;
    text-align: center;
    
    .el-button {
      margin-top: 10px;
    }
  }
  
  .loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: #ffffff;
    padding: 20px;
    border-radius: 4px;
    z-index: 10;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .el-icon {
      font-size: 24px;
      margin-bottom: 10px;
    }
  }
}
</style>