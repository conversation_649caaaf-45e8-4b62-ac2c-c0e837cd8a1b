<script lang="ts" setup>

import type { RouterOption } from '~/router/layout/types'

const props = defineProps<{
  base: string;
  options: any[];
}>()



function resolveURL(option: RouterOption) {
  // 始终返回完整的子菜单路径，以防止取消选中
  const url = `${props.base}/${option.path}`;
  // 替换多个斜杠为单个斜杠
  return url.replace(/\/\/+/g, '/');
}

</script>

<template>
  <div class="submenu">
    <RouterLink v-for="option in options" class="route-item" :key="option.name" :title="option.name"
      :to="resolveURL(option)">
      <span>{{ option.name }}</span>
    </RouterLink>
  </div>
  <RouterView />
</template>

<style lang="scss" scoped>
.submenu {
  pointer-events: all;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 48px;
  gap: 12px;
  z-index: 9;
}

.route-item {
  box-sizing: border-box;
  font-family: PangMenZhengdao;
  width: 126px;
  height: 36px;
  background-image: url('~/assets/images/header/sub-menu.svg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 20px;
  color: #D0D0D0;
  letter-spacing: 2.5px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:not(.router-link-active):hover {
    filter: brightness(1.5);
  }

  &:active {
    transform: scale(0.95);
  }

  &.router-link-active {
    background-image: url('~/assets/images/header/sub-menu-active.svg');
    color: #FFFFFF;
  }
}
</style>
