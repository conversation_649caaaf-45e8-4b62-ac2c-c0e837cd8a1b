import request from '~/router/request'
import { AxiosPromise } from 'axios'

export interface wbsParams {
  portalId?: string | number;
}

export interface wbsResponse {
  data: Record<string, any>
  [key: string]: any
}

// 获取wbs树
export function wbsTree(): AxiosPromise<wbsResponse> {
  return request({
    url: `/construction-package/wbs/node/tree`,
    method: 'get',
  })
}
//  获取项目部或者查询工地树
export function getSiteTree(data:any) {
  return request({
    url: `/smart-working/constructionSite/tree`,
    method: 'get',
    params: data,
  });
}