import axios from 'axios'
import storage from '~/utils/storage'
import { getSign } from '~/utils/sign'
import { verifyUrl } from '~/utils/verify'
import { ElMessage } from 'element-plus'

// create an axios instance
const service = axios.create({
  baseURL: 'https://mock.apifox.cn/', // url = base url + request url
  withCredentials: true,
  timeout: 60000, // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    // do something before request is sent
    // config.headers['Fawkes-Biz'] = SM4Encrypt(JSON.stringify(FawkesBiz))
    
    verifyUrl(config, 'sign')
      ? (config.params = getSign(config.params))
      : (config.headers['Fawkes-Auth'] = `${storage.get('access_token')}`)
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  (response) => {
    const res = response.data
    if (res.status || res.code == 200 || res.code == 8000000 || response.status == 200) {
      return res
    }  else if (response.config.responseType == 'blob') {
      return res
    }else {
      return Promise.reject(new Error(res.message || 'Error'))
    }
  },
  (error) => {
    console.log(error, 'error')
    if(error.status == 401){
      ElMessage.error(error.response.data.message || 'Error')
      return Promise.reject(new Error(error.response.data.message || 'Error')) 
    }
    return Promise.reject(error)
  }
)

export default service
