import axios from 'axios'
import storage from '~/utils/storage'
import { getSign } from '~/utils/sign'
import { verifyUrl } from '~/utils/verify'
import { ElMessage } from 'element-plus'
import router from '~/router'
import { SM4Encrypt }　from '~/utils/encryption/sm4'

// 防抖机制相关变量
let lastErrorTime = 0;
let lastErrorCode: number | string = 0;
const ERROR_THRESHOLD = 1000; // 相同错误提示的最小间隔时间（毫秒）

// create an axios instance
const service = axios.create({
  baseURL: '/api', // url = base url + request url
  withCredentials: true,
  timeout: 60000, // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => { 
    verifyUrl(config, 'sign')
      ? (config.params = getSign(config.params))
      : (config.headers['Fawkes-Auth'] = `${storage.get('access_token')}`)

    // 添加 Fawkes-Biz 处理逻辑
    const FawkesBiz: any = {};
    
    // 获取门户ID
    const portals = storage.get('portals');
    const portalList = portals ? JSON.parse(portals) : '';
    const portalId = portalList && portalList.length > 0 ? portalList[0]['id'] : ''
    if (portalId) {
      FawkesBiz.portalId = portalId;
    }

    // 使用SM4加密并添加到请求头
    config.headers['Fawkes-Biz'] = SM4Encrypt(JSON.stringify(FawkesBiz));
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  (response) => {
    const res = response.data
    
    // 1. 成功情况的处理 - 如果符合以下任一条件，则返回成功
    // 1.1 登录成功情况 - 含有access_token
    if (res && res.access_token) {
      return res;
    }
    
    // 1.2 成功响应情况 - 返回结果为空或只有数据无状态信息
    if (res && !res.code && !res.status && Object.keys(res).length > 0) {
      return res;
    }
    
    // 特殊处理tree接口 - 不管返回什么都直接返回结果
    if (response.config.url && response.config.url.includes('/wbs/node/tree')) {
      console.log('处理特殊tree接口:', res);
      return res;
    }
    
    // 1.3 成功响应情况 - 状态标识为成功
    if (res && (res.status === true || res.code === 200 || res.code === 8000000)) {
      return res;
    }
    
    // 1.4 二进制数据类型直接返回
    if (response.config.responseType === 'arraybuffer' || 
        response.config.responseType === 'stream' || 
        response.config.responseType === 'blob' || 
        response.config.responseType === 'document') {
      return res;
    }
    
    // 2. 错误情况的处理
    // 首先确保有响应数据
    if (!res) {
      return Promise.reject(new Error('无响应数据'));
    }
    
    // 错误信息
    const errorMessage = res.message || '系统异常';
    const errorCode = res.code || -1;
    const currentTime = Date.now();
    
    // 2.1 处理未登录错误 - 特殊处理，总是跳转登录
    if (errorCode === -8000140) {
      // 使用防抖机制避免重复提示
      if (errorCode !== lastErrorCode || (currentTime - lastErrorTime) > ERROR_THRESHOLD) {
        ElMessage({
          message: errorMessage || '未登录，无权限访问', 
          type: 'error',
          duration: 3000
        });
        
        // 更新防抖状态
        lastErrorTime = currentTime;
        lastErrorCode = errorCode;
        
        // 跳转到登录页面
        router.push({ path: '/login', query: { refresh: 'true' } });
      }
      
      return Promise.reject(new Error(errorMessage || '未登录，无权限访问'));
    } 
    // 2.2 处理一般错误 - 有明确的错误状态
    else if (!res.status || res.status === false) {
      // 使用防抖机制避免重复提示
      if (errorCode !== lastErrorCode || (currentTime - lastErrorTime) > ERROR_THRESHOLD) {
        ElMessage({
          message: errorMessage, 
          type: 'error',
          duration: 3000,
          showClose: true
        });
        
        // 更新防抖状态
        lastErrorTime = currentTime;
        lastErrorCode = errorCode;
      }
      
      return Promise.reject(new Error(errorMessage || '操作失败'));
    }
    // 2.3 其他情况 - 无明确错误状态但有错误消息
    else if (res.message) {
      // 使用防抖机制避免重复提示
      if (errorCode !== lastErrorCode || (currentTime - lastErrorTime) > ERROR_THRESHOLD) {
        ElMessage.error(errorMessage);
        
        // 更新防抖状态
        lastErrorTime = currentTime;
        lastErrorCode = errorCode;
      }
      
      return Promise.resolve(undefined);
    }
    
    // 3. 默认返回响应（如果前面的条件都不满足）
    return res;
  },
  (error) => {
    console.log(error, 'error')
    if(error.status == 401 || (error.response && error.response.status == 401)){
      // HTTP 401错误也需要防抖
      const currentTime = Date.now();
      const currentCode = 401; // HTTP状态码作为错误码
      
      if (currentCode !== lastErrorCode || (currentTime - lastErrorTime) > ERROR_THRESHOLD) {
        ElMessage.error(error.response?.data?.message || '未登录，无权限访问')
        // 更新防抖状态
        lastErrorTime = currentTime;
        lastErrorCode = currentCode;
        
        // 跳转到登录页面
        router.push({ path: '/login', query: { refresh: 'true' } })
      }
      
      return Promise.reject(new Error(error.response?.data?.message || '未登录，无权限访问')) 
    }
    return Promise.reject(error)
  }
)

export default service
