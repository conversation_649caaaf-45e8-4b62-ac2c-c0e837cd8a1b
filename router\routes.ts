import { RouteRecordRaw } from 'vue-router'
import Player from './layout/Player.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    component: Player,
    redirect: '/login',
    children: [
      {
        path: 'home',
        name: '首页',
        component: () => import('./home/<USER>'),
      },
      {
        path: 'demo',
        name: '示例',
        component: () => import('./demo/index.vue'),
      },
      {
        path: '/construction',
        name: '智慧建设',
        components: {
          default: () => import('./layout/Outlet.vue'),
        },
        redirect: () => {
          return {
            path: '/construction/build/secure',
          }
        },
        children: [
          {
            path: 'overview',
            name: '建设总览',
            component: () => import('./construction/overview/index.vue'),
          },
          {
            path: 'build',
            name: '数智建管',
            component: () =>
              import(
                './construction/smartgc/index.vue'
              ),
            redirect: '/construction/build/secure',
            children: [
              {
                path: 'secure',
                name: '安全',
                component: () =>
                  import(
                    './construction/smartgc/Security/index.vue'
                  ),
                meta: {
                  pointGroups: ['安全'],
                  streamingLevels: ['Cxnp_Base', 'Cxnp_Base_Foliage', 'Cxnp_Base_Anquan'],
                },
              },
              {
                path: 'quality',
                name: '质量',
                component: () =>
                  import(
                    './construction/smartgc/Quality/index.vue'
                  ),
                meta: {
                  pointGroups: ['质量'],
                  streamingLevels: ['Cxnp_Base', 'Cxnp_Base_Foliage', 'Cxnp_Base_Zhiliang'],
                },
              },
              {
                path: 'investment',
                name: '投资',
                component: () =>
                  import(
                    './construction/smartgc/Investment/index.vue'
                  ),
                meta: {
                  pointGroups: ['投资'],
                },
              },
              {
                path: 'progress',
                name: '进度',
                component: () =>
                  import(
                    './construction/smartgc/Progress/index.vue'
                  ),
                meta: {
                  pointGroups: ['进度'],
                },
              },
              {
                path: 'file',
                name: '文件',
                component: () =>
                  import(
                    './construction/smartgc/File/index.vue'
                  ),
                meta: {
                  pointGroups: ['文件'],
                },
              },
            ],
          },
          {
            path: 'redsite',
            name: '智慧工地',
            component: () => import('./construction/redsite/index.vue'),
            redirect: '/construction/redsite/video',
            meta: { isRedsite: true },
            children: [
              {
                path: 'video',
                name: '视频监控',
                component: () =>
                  import(
                    './construction/redsite/Video/index.vue'
                  ),
                meta: {
                  pointGroups: ['视频监控'],
                  streamingLevels: ['Cxnp_Base', 'Cxnp_Base_Foliage', 'Cxnp_Base_Anquan'],
                },
              }, {
                path: 'environment',
                name: '环境监测',
                component: () =>
                  import(
                    './construction/redsite/Environment/index.vue'
                  ),
                meta: {
                  pointGroups: ['环境监测'],
                  streamingLevels: ['Cxnp_Base', 'Cxnp_Base_Foliage', 'Cxnp_Base_Anquan'],
                },
              }, {
                path: 'personnel',
                name: '人员管理',
                component: () =>
                  import(
                    './construction/redsite/Personnel/index.vue'
                  ),
                meta: {
                  pointGroups: ['人员管理'],
                  streamingLevels: ['Cxnp_Base', 'Cxnp_Base_Foliage', 'Cxnp_Base_Anquan'],
                },
              },
            ]
          },
        ],
      },
      // {
      //   path: '/overview',
      //   name: '工程概况',
      //   component: () => import('./overview/Projects.vue'),
      //   children: [
      //     {
      //       path: '',
      //       redirect: '/overview/SouthDrainageProject',
      //     },
      //     {
      //       path: 'SouthDrainageProject',
      //       name: '南排工程',
      //       component: () =>
      //         import('./overview/components/SouthDrainageProject.vue'),
      //       meta: {
      //         streamingLevels: ['Cxnp_Base','Cxnp_Base_Foliage', 'Cxnp_Base_Nanpaigongcheng'],
      //       },
      //     },
      //     {
      //       path: 'NorthSouthProject',
      //       name: '南北线工程',
      //       component: () =>
      //         import('./overview/components/NorthSouthProject.vue'),
      //       meta: {
      //         streamingLevels: ['Cxnp_Base','Cxnp_Base_Foliage', 'Cxnp_Base_Nanbeixiangongcheng'],
      //       },
      //     },
      //     {
      //       path: 'WesternrouteProject',
      //       name: '西线工程',
      //       component: () =>
      //         import('./overview/components/WesternrouteProject.vue'),
      //       meta: {
      //         streamingLevels: ['Cxnp_Base','Cxnp_Base_Foliage', 'Cxnp_Base_Xixiangongcheng'],
      //       },
      //     },
      //     {
      //       path: 'EngineeringBenefit',
      //       name: '工程效益',
      //       component: () =>
      //         import('./overview/components/EngineeringBenefit.vue'),
      //       meta: {
      //         streamingLevels: ['Cxnp_Base','Cxnp_Base_Foliage', 'Cxnp_Base_Gongchengxiaoyi'],
      //       },
      //     },
      //     {
      //       path: 'TechnicalHighlight',
      //       name: '技术亮点',
      //       component: () =>
      //         import('./overview/components/TechnicalHighlight.vue'),
      //       meta: {
      //         streamingLevels: ['Cxnp_Base','Cxnp_Base_Foliage', 'Cxnp_Base_Jishuyaodian'],
      //       },
      //     },
      //   ],
      // },
    ],
  },
  {
    path: '/login',
    name: '登录',
    component: () => import('./auth/index.vue'),
  },
]
export default routes
