<script lang="ts" setup>
import { defineLoader, registerSymbol } from '@jugar/svg-icon'
import { watchEffect } from 'vue'
import { SvgIconProps } from './types'

const props = defineProps<SvgIconProps>()

defineLoader((fullname: string) => {
  const [dir, name] = fullname.split(':')
  return new URL(`../../svg/${dir}/${name}.svg?url`, import.meta.url).href
})
watchEffect(() => registerSymbol(props.name))
</script>

<template>
  <svg class="svg-icon" :height="size" :width="size">
    <use :xlink:href="'#' + props.name" :fill="color" />
  </svg>
</template>

<style scoped>
.svg-icon {
  overflow: hidden;
  vertical-align: -0.15em;
  fill: currentColor;
}
</style>
