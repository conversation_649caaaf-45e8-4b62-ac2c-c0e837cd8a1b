import { ref, computed, shallowRef } from 'vue'
import { useFreedoStore } from '~/stores/freedoStore'

// 全局单例
const freedoError = ref<string | null>(null);

/**
 * 初始化飞渡模型
 * @param options 初始化选项
 */
export async function initFreedo(options: {
  domId: string;
  host?: string;
  useHttps?: boolean;
  iid?: string;
  reset?: boolean; // 是否在初始化时重置模型
  ui?: object; 
  apiOptions?: object;
  onReady?: () => void;
  onEvent?: (event: any) => void;
  events?: object;
}) {
  const freedoStore = useFreedoStore();
  const host = options.host;
  
  try {
    // 创建模型实例
    const playerInstance = new window.DigitalTwinPlayer(host, {
      domId: options.domId,
      useHttps: options.useHttps,
      iid: options.iid,
      reset: options.reset,
      ui: {
        startupInfo: false,
        // homeButton: true, // 回到初始位置按钮
        // fullscreenButton: true, // 全屏按钮
        statusIndicator: false, // 左上角闪烁的状态指示灯
        campass: false, // 是否显示指北针
        ...options.ui
      },
      apiOptions: {
        onReady: async () => {
          // 设置全局API引用
          const apiInstance = playerInstance.getAPI();
          
          // 更新store状态
          freedoStore.setFreedoReadyState(true);
          // 存储Api
          freedoStore.setFreedoApi(apiInstance);
          // 存储模型实例
          freedoStore.setFreedoPlayer(playerInstance);

          // 设置鼠标拾取掩码，确保能捕获所有鼠标事件
          if (apiInstance.settings && apiInstance.settings.setMousePickMask) {
            // 7: 启用点击(1)、移动(2)、悬停(4)全部功能
            await apiInstance.settings.setMousePickMask(7);
            console.log('已启用全部鼠标拾取功能');
            
            // 查询当前交互模式
            // await apiInstance.settings.getInteractionMode();

            // 获取地图样式
            // await apiInstance.settings.getMapMode()

            // 设置指北针位置
            await apiInstance.settings.setCampassPosition(1350, 100);
          }

          await freedoStore.getFreedoLayerTree();
          await freedoStore.getFreedoAnimationList();

          // TODO:设置相机可视范围
          // await apiInstance.camera.lookAtBBox(bbox, yaw, pitch);
          
          // 调用用户提供的onReady回调
          if (options.onReady) {
            options.onReady();
          }
        },
        onEvent: (event: any) => {
          // 记录所有事件类型
          console.log('onEvent类型:', event?.eventtype, event);
          //事件类型 参考交互事件类型枚举对象
          const eventType = event.eventtype;
          //图层类型
          const layerType = event.Type;
          //图层Id
          const layerId = event.Id || event.ID;
          //点击ActorId
          const objectId = event.ObjectID;
          //当前点击位置
          const objectLocation = event.MouseClickPoint;

          switch (eventType) {
           //鼠标左键点击时触发此事件
           case "LeftMouseButtonClick":
               console.log('触发事件类型：鼠标左键单击，eventType：' + eventType);
               freedoStore.setFreedoClickedObject(event);
               freedoStore.resetAutoCameraMovement();
               break;

           //鼠标悬停时触发此事件
           //注意需提前开启鼠标拾取：fdapi.settings.setMousePickMask(7);
           case "MouseHovered":
               console.log('触发事件类型：鼠标悬停，eventType：' + eventType);
               break;
               
           //鼠标移动时触发此事件
           //注意需提前开启鼠标拾取：fdapi.settings.setMousePickMask(7);
           case "MouseMoved":
               console.log('触发事件类型：鼠标移动，eventType：' + eventType);
               freedoStore.resetAutoCameraMovement();
               break;

          //相机开始移动时触发此监听事件
          //注意需先开启事件：fdapi.settings.setEnableCameraMovingEvent(true);
           case "CameraStartMove":
               console.log('触发事件类型：相机开始飞行，eventType：' + eventType);
               break;

          //相机正在移动时触发此监听事件
          //注意需先开启事件：fdapi.settings.setEnableCameraMovingEvent(true);
           case "CameraMoving":
               console.log('触发事件类型：相机正在飞行，eventType：' + eventType);
               break;

          //相机停止移动时触发此监听事件
          //注意需先开启事件：fdapi.settings.setEnableCameraMovingEvent(true);
           case "CameraStopMove":
               console.log('触发事件类型：相机停止飞行，eventType：' + eventType);
               break;
        
          //对象执行focus()或相机执行set()/lookAt()/lookAtBBox()方法时触发
           case "CameraChanged":
               console.log('触发事件类型：相机位置发生变化，eventType：' + eventType);
               freedoStore.freedoApi?.camera?.stop();
               break;
          
           //进入面剖切模式，编辑面剖切位置后触发事件并返回剖切结果 
           case "PlaneClipEdit":
               console.log('触发事件类型：编辑面剖切，eventType：' + eventType);
               break;
          
           //进入体剖切模式，编辑体剖切位置后触发事件并返回剖切结果 
           case "VolumeClipEdit":
               console.log('触发事件类型：编辑体剖切，eventType：'+ eventType);
               break;

          //进入测量模式后，测量完成时触发此事件并返回测量结果
           case "Measurement":
               console.log('触发事件类型：测量完成，eventType：' + eventType);
               break;
           
           //播放导览结束触发此事件
           //fdapi.camera.playAnimation(id)和导览对象播放导览结束__g.cameraTour.play(id)均触发此事件
          //  case "CameraTourFinished":
          //      console.log('触发事件类型：播放导览结束，eventType：' + eventType);
          //      break;

           default:
               ""
          }
          
          // 调用用户提供的onEvent回调
          if (options.onEvent) {
            options.onEvent(event);
          }
        },
        ...options.apiOptions
      },
      events: {
        onVideoLoaded: () => {
          console.log('onVideoLoaded');
        },
        onConnClose: () => {
          console.log('onConnClose');
        },
        ...options.events
      }
    });
  } catch (e: any) {
    freedoError.value = e.message || '初始化飞渡模型失败';
    console.error('初始化飞渡模型失败:', e);
  }
}

/**
 * 飞渡API Composable
 */
export function useFreedo() {
  const freedoStore = useFreedoStore();
  
  return {
    // 错误状态
    freedoError,
    
    // 初始化方法
    initFreedo,
    
    // 添加常用的属性引用，方便使用
    isFreedoReady: computed(() => freedoStore.isFreedoReady),
    freedoClickedObject: computed(() => freedoStore.freedoClickedObject),
    freedoPlayer: computed(() => freedoStore.freedoPlayer),
    freedoApi: computed(() => freedoStore.freedoApi),
    
    // Store引用
    freedoStore
  };
}