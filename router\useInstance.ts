/**
 * 全局注册的组件
 * 使用：
 * import useInstance from "~/router/hooksuseCurrentInstance";
 * const { globalProperties } = useInstance();
 * globalProperties.$message.success({});
 */
import { ComponentInternalInstance, getCurrentInstance } from 'vue';

export default function useInstance() {
  const instance = getCurrentInstance();

  if (!instance) {
    throw new Error('useInstance 必须在 setup 中调用');
  }

  const { appContext } = instance as ComponentInternalInstance;
  const globalProperties = appContext.config.globalProperties;

  return {
    globalProperties,
  };
}

