var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(u){var F=0;return function(){return F<u.length?{done:!1,value:u[F++]}:{done:!0}}};$jscomp.arrayIterator=function(u){return{next:$jscomp.arrayIteratorImpl(u)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(u,F,D){if(u==Array.prototype||u==Object.prototype)return u;u[F]=D.value;return u};$jscomp.getGlobal=function(u){u=["object"==typeof globalThis&&globalThis,u,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var F=0;F<u.length;++F){var D=u[F];if(D&&D.Math==Math)return D}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(u,F){var D=$jscomp.propertyToPolyfillSymbol[F];if(null==D)return u[F];D=u[D];return void 0!==D?D:u[F]};
$jscomp.polyfill=function(u,F,D,K){F&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(u,F,D,K):$jscomp.polyfillUnisolated(u,F,D,K))};$jscomp.polyfillUnisolated=function(u,F,D,K){D=$jscomp.global;u=u.split(".");for(K=0;K<u.length-1;K++){var P=u[K];if(!(P in D))return;D=D[P]}u=u[u.length-1];K=D[u];F=F(K);F!=K&&null!=F&&$jscomp.defineProperty(D,u,{configurable:!0,writable:!0,value:F})};
$jscomp.polyfillIsolated=function(u,F,D,K){var P=u.split(".");u=1===P.length;K=P[0];K=!u&&K in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var B=0;B<P.length-1;B++){var I=P[B];if(!(I in K))return;K=K[I]}P=P[P.length-1];D=$jscomp.IS_SYMBOL_NATIVE&&"es6"===D?K[P]:null;F=F(D);null!=F&&(u?$jscomp.defineProperty($jscomp.polyfills,P,{configurable:!0,writable:!0,value:F}):F!==D&&(void 0===$jscomp.propertyToPolyfillSymbol[P]&&(D=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[P]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(P):$jscomp.POLYFILL_PREFIX+D+"$"+P),$jscomp.defineProperty(K,$jscomp.propertyToPolyfillSymbol[P],{configurable:!0,writable:!0,value:F})))};$jscomp.initSymbol=function(){};$jscomp.iteratorPrototype=function(u){u={next:u};u[Symbol.iterator]=function(){return this};return u};$jscomp.underscoreProtoCanBeSet=function(){var u={a:!0},F={};try{return F.__proto__=u,F.a}catch(D){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(u,F){u.__proto__=F;if(u.__proto__!==F)throw new TypeError(u+" is not extensible");return u}:null;$jscomp.makeIterator=function(u){var F="undefined"!=typeof Symbol&&Symbol.iterator&&u[Symbol.iterator];return F?F.call(u):$jscomp.arrayIterator(u)};$jscomp.generator={};
$jscomp.generator.ensureIteratorResultIsObject_=function(u){if(!(u instanceof Object))throw new TypeError("Iterator result "+u+" is not an object");};$jscomp.generator.Context=function(){this.isRunning_=!1;this.yieldAllIterator_=null;this.yieldResult=void 0;this.nextAddress=1;this.finallyAddress_=this.catchAddress_=0;this.finallyContexts_=this.abruptCompletion_=null};
$jscomp.generator.Context.prototype.start_=function(){if(this.isRunning_)throw new TypeError("Generator is already running");this.isRunning_=!0};$jscomp.generator.Context.prototype.stop_=function(){this.isRunning_=!1};$jscomp.generator.Context.prototype.jumpToErrorHandler_=function(){this.nextAddress=this.catchAddress_||this.finallyAddress_};$jscomp.generator.Context.prototype.next_=function(u){this.yieldResult=u};
$jscomp.generator.Context.prototype.throw_=function(u){this.abruptCompletion_={exception:u,isException:!0};this.jumpToErrorHandler_()};$jscomp.generator.Context.prototype.return=function(u){this.abruptCompletion_={return:u};this.nextAddress=this.finallyAddress_};$jscomp.generator.Context.prototype.jumpThroughFinallyBlocks=function(u){this.abruptCompletion_={jumpTo:u};this.nextAddress=this.finallyAddress_};$jscomp.generator.Context.prototype.yield=function(u,F){this.nextAddress=F;return{value:u}};
$jscomp.generator.Context.prototype.yieldAll=function(u,F){u=$jscomp.makeIterator(u);var D=u.next();$jscomp.generator.ensureIteratorResultIsObject_(D);if(D.done)this.yieldResult=D.value,this.nextAddress=F;else return this.yieldAllIterator_=u,this.yield(D.value,F)};$jscomp.generator.Context.prototype.jumpTo=function(u){this.nextAddress=u};$jscomp.generator.Context.prototype.jumpToEnd=function(){this.nextAddress=0};
$jscomp.generator.Context.prototype.setCatchFinallyBlocks=function(u,F){this.catchAddress_=u;void 0!=F&&(this.finallyAddress_=F)};$jscomp.generator.Context.prototype.setFinallyBlock=function(u){this.catchAddress_=0;this.finallyAddress_=u||0};$jscomp.generator.Context.prototype.leaveTryBlock=function(u,F){this.nextAddress=u;this.catchAddress_=F||0};
$jscomp.generator.Context.prototype.enterCatchBlock=function(u){this.catchAddress_=u||0;u=this.abruptCompletion_.exception;this.abruptCompletion_=null;return u};$jscomp.generator.Context.prototype.enterFinallyBlock=function(u,F,D){D?this.finallyContexts_[D]=this.abruptCompletion_:this.finallyContexts_=[this.abruptCompletion_];this.catchAddress_=u||0;this.finallyAddress_=F||0};
$jscomp.generator.Context.prototype.leaveFinallyBlock=function(u,F){F=this.finallyContexts_.splice(F||0)[0];if(F=this.abruptCompletion_=this.abruptCompletion_||F){if(F.isException)return this.jumpToErrorHandler_();void 0!=F.jumpTo&&this.finallyAddress_<F.jumpTo?(this.nextAddress=F.jumpTo,this.abruptCompletion_=null):this.nextAddress=this.finallyAddress_}else this.nextAddress=u};$jscomp.generator.Context.prototype.forIn=function(u){return new $jscomp.generator.Context.PropertyIterator(u)};
$jscomp.generator.Context.PropertyIterator=function(u){this.object_=u;this.properties_=[];for(var F in u)this.properties_.push(F);this.properties_.reverse()};$jscomp.generator.Context.PropertyIterator.prototype.getNext=function(){for(;0<this.properties_.length;){var u=this.properties_.pop();if(u in this.object_)return u}return null};$jscomp.generator.Engine_=function(u){this.context_=new $jscomp.generator.Context;this.program_=u};
$jscomp.generator.Engine_.prototype.next_=function(u){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_.next,u,this.context_.next_);this.context_.next_(u);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.return_=function(u){this.context_.start_();var F=this.context_.yieldAllIterator_;if(F)return this.yieldAllStep_("return"in F?F["return"]:function(D){return{value:D,done:!0}},u,this.context_.return);this.context_.return(u);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.throw_=function(u){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_["throw"],u,this.context_.next_);this.context_.throw_(u);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.yieldAllStep_=function(u,F,D){try{var K=u.call(this.context_.yieldAllIterator_,F);$jscomp.generator.ensureIteratorResultIsObject_(K);if(!K.done)return this.context_.stop_(),K;var P=K.value}catch(B){return this.context_.yieldAllIterator_=null,this.context_.throw_(B),this.nextStep_()}this.context_.yieldAllIterator_=null;D.call(this.context_,P);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.nextStep_=function(){for(;this.context_.nextAddress;)try{var u=this.program_(this.context_);if(u)return this.context_.stop_(),{value:u.value,done:!1}}catch(F){this.context_.yieldResult=void 0,this.context_.throw_(F)}this.context_.stop_();if(this.context_.abruptCompletion_){u=this.context_.abruptCompletion_;this.context_.abruptCompletion_=null;if(u.isException)throw u.exception;return{value:u.return,done:!0}}return{value:void 0,done:!0}};
$jscomp.generator.Generator_=function(u){this.next=function(F){return u.next_(F)};this.throw=function(F){return u.throw_(F)};this.return=function(F){return u.return_(F)};this[Symbol.iterator]=function(){return this}};$jscomp.generator.createGenerator=function(u,F){F=new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(F));$jscomp.setPrototypeOf&&u.prototype&&$jscomp.setPrototypeOf(F,u.prototype);return F};
$jscomp.asyncExecutePromiseGenerator=function(u){function F(K){return u.next(K)}function D(K){return u.throw(K)}return new Promise(function(K,P){function B(I){I.done?K(I.value):Promise.resolve(I.value).then(F,D).then(B,P)}B(u.next())})};$jscomp.asyncExecutePromiseGeneratorFunction=function(u){return $jscomp.asyncExecutePromiseGenerator(u())};$jscomp.asyncExecutePromiseGeneratorProgram=function(u){return $jscomp.asyncExecutePromiseGenerator(new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(u)))};
$jscomp.polyfill("Array.prototype.includes",function(u){return u?u:function(F,D){var K=this;K instanceof String&&(K=String(K));var P=K.length;D=D||0;for(0>D&&(D=Math.max(D+P,0));D<P;D++){var B=K[D];if(B===F||Object.is(B,F))return!0}return!1}},"es7","es3");$jscomp.polyfill("Promise.prototype.finally",function(u){return u?u:function(F){return this.then(function(D){return Promise.resolve(F()).then(function(){return D})},function(D){return Promise.resolve(F()).then(function(){throw D;})})}},"es9","es3");
var acapi=function(u){function F(a){function b(c,e){return c.reduce(function(d,g){var h;Array.isArray(g)&&(h=b(g,e+1));return h>d?h:d},e)}return b(a,1)}function D(a){if(void 0!=a){var b=[];if(/^(rgb|RGB)/.test(a)){a=a.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");for(var c of a)b.push(Number((Number(c)/255).toFixed(6)))}else/^#/.test(a)?a.includes("#")?(a=a.replace("#",""),3==a.length&&(a=a[0]+""+a[0]+a[1]+a[1]+a[2]+a[2]),6!=a.length?(console.error("\u975e\u5341\u516d\u8fdb\u5236\u989c\u8272\u683c\u5f0f"),
b=void 0):(b=parseInt(a.substring(0,2),16),c=parseInt(a.substring(2,4),16),a=parseInt(a.substring(4,6),16),b=[b/255,c/255,a/255])):(console.error("\u975e\u5341\u516d\u8fdb\u5236\u989c\u8272\u683c\u5f0f"),b=void 0):a instanceof Array&&(b=a);3==b.length&&b.push(1);return b}}function K(a){if(void 0!=a){var b,c=F(a);2==c?b=[[a]]:3==c?b=[a]:4==c&&(b=a);if(b)for(let e of b)for(let d of e)for(let g of d)2==g.length&&g.push(0);return b}}function P(a){if(void 0!=a)return 1==F(a)?[a]:a}function B(a){return a instanceof
Array?a:[a]}function I(a){let b=[];if(a instanceof Array)for(let c in a)b[c]=a[c].toString();else b=[a.toString()];return b}function za(a){return!isNaN(parseFloat(a))&&isFinite(a)}function R(a){switch(typeof a){case "undefined":return!0;case "string":if(0===a.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case "boolean":if(!a)return!0;break;case "number":if(isNaN(a))return!0;break;case "object":if(null===a||0===a.length)return!0;for(var b in a)return!1;return!0}return!1}function qa(a,
b){for(a=a.toString();a.length<b;)a="0"+a;return a}function ua(){let a=new Date;return qa(a.getHours(),2)+":"+qa(a.getMinutes(),2)+":"+qa(a.getSeconds(),2)+"."+qa(a.getMilliseconds(),3)}function Ma(a,b){let c,e=0;return function(...d){const g=this,h=+new Date,q=b-(h-e);0>=q?(e=h,a.apply(g,d),c&&(clearTimeout(c),c=null)):c||(c=setTimeout(()=>{e=+new Date;c=null;a.apply(g,d)},q))}}function Na(a){switch(a){case ba.StartingProcess:return"starting process...";case ba.CheckingBusy:return"checking...";case ba.ProcessStartFailed:return"process start failed!";
case ba.ProcessStarted:return"process started";case ba.LoadingProject:return"loading project...";case ba.ProjectLoaded:return"project loaded";case ba.UserAccessDenied:return"user access denied";case ba.CheckingLicense:return"checking license..."}}function Aa(a){"string"===typeof a&&(a=document.getElementById(a));return a}function Oa(a){return S.isIOSDevice&&S.isInWeixinBrowser?!1:a==ja.abnormal||a==ja.instance_disconnected}function V(a,b){return void 0!==a&&null!==a?a:b}function W(){if(!(this instanceof
W))return new W;this.reg=Array(8);this.chunk=[];this.size=0;this.reset()}function Pa(a){var b=new W;a=b.sum(a);return b.toArray(a,function(c){return("0"+(c&255).toString(16)).slice(-2)}).join("")}function Ba(a){return a.keyCode===ea.Shift&&"ShiftRight"===a.code?ea.RightShift:a.keyCode===ea.Control&&"ControlRight"===a.code?ea.RightControl:a.keyCode===ea.Alt&&"AltRight"===a.code?ea.RightAlt:a.keyCode}var k={None:0,Camera_PlayAnimation:1,Camera_StopAnimation:2,Misc_EnterReportMode:3,Misc_ExitReportMode:4,
Misc_SetDateTime:5,Camera_GetCamera:6,Camera_SetState:7,Camera_Set:8,Camera_LookAtBBox:9,Coord_World2Screen:10,Coord_Screen2World:11,InfoTree_SetVisibility:12,InfoTree_Show:13,InfoTree_Hide:14,InfoTree_EnableXRay:15,InfoTree_DisableXRay:16,TileLayer_Add:17,HeatMap_Add:18,HeatMap_Update:19,HeatMap_AddPoints:20,HeatMap_RemovePoints:21,HeatMap_Delete:22,HeatMap_Show:23,HeatMap_Hide:24,HeatMap_Focus:25,HighlightArea_Add:26,HighlightArea_Delete:27,HighlightArea_Update:28,Polygon3D_Add:29,Polygon3D_Delete:30,
Misc_AddImageButton:31,Misc_DeleteImageButton:32,Misc_AddAnimatedImageButton:33,Settings_SetMainUIVisibility:34,Settings_SetMousePickMask:35,Tag_Add:36,Tag_Delete:37,Tag_Update:38,Tag_Clear:39,Tag_Focus:40,Beam_Add:41,Beam_Update:42,Beam_Delete:43,Beam_Clear:44,Beam_Focus:45,Beam_Show:350,Beam_Hide:351,Beam_ShowAll:352,Beam_HideAll:353,RadiationPoint_Add:46,RadiationPoint_Update:47,RadiationPoint_Delete:48,RadiationPoint_Clear:49,Polygon_Add:50,Polygon_Update:51,Polygon_Delete:52,Polygon_Clear:53,
Polygon_Highlight:54,Polygon_StopHighlight:325,Polygon_Glow:55,Polygon_Focus:56,Tag_FocusAll:57,Tag_Show:58,Tag_Hide:59,Tag_ShowAll:60,Tag_HideAll:61,RadiationPoint_Focus:62,RadiationPoint_FocusAll:63,RadiationPoint_Show:64,RadiationPoint_Hide:65,RadiationPoint_ShowAll:66,RadiationPoint_HideAll:67,Polygon_Show:68,Polygon_Hide:69,Polygon3D_Update:70,Polygon3D_Clear:71,Polygon3D_Focus:72,Polygon3D_Show:73,Polygon3D_Hide:74,Polygon3D_Highlight:75,Polygon3D_Glow:76,Polygon3D_StopGlow:77,HighlightArea_Clear:78,
HighlightArea_Focus:79,HighlightArea_Show:80,HighlightArea_Hide:81,Misc_GetVersion:82,TileLayer_Update:83,TileLayer_Delete:84,TileLayer_Focus:85,TileLayer_EnableXRay:86,TileLayer_DisableXRay:87,TileLayer_Show:88,TileLayer_Hide:89,Tag_Get:90,RadiationPoint_Get:91,Polygon_Get:92,Polygon3D_Get:93,HighlightArea_Get:94,TileLayer_Get:95,Beam_Get:96,HeatMap_Get:97,Settings_GetMapMode:98,Settings_SetMapMode:99,Settings_SetMapURL:100,Settings_SetWMTSLayerVisible:320,TileLayer_Actor_Show:101,TileLayer_Actor_Hide:102,
TileLayer_Actor_Focus:103,TileLayer_Actor_Highlight:104,TileLayer_Actor_StopHightlight:105,CustomObject_Add:106,CustomObject_Update:107,CustomObject_Delete:108,CustomObject_Get:109,CustomObject_Focus:110,CustomObject_Show:111,CustomObject_Hide:112,CustomObject_SetTintColor:326,HeatMap_Clear:113,CustomTag_Clear:39,CustomTag_Delete:37,CustomTag_Focus:40,CustomTag_FocusAll:57,CustomTag_Show:58,CustomTag_Hide:59,CustomTag_ShowAll:60,CustomTag_HideAll:61,CustomTag_Add:114,CustomTag_Update:115,CustomTag_Get:116,
Polyline_Add:117,Polyline_Update:118,Polyline_Delete:119,Polyline_Clear:120,Polyline_Focus:121,Polyline_Get:122,Polyline_Show:123,Polyline_Hide:124,Polyline_ShowAll:125,Polyline_HideAll:126,VideoProjection_Add:127,VideoProjection_Update:128,VideoProjection_Delete:129,VideoProjection_Show:130,VideoProjection_Hide:131,VideoProjection_Get:132,VideoProjection_Focus:133,Misc_PlayVideo:134,Misc_StopPlayVideo:135,Weather_SetParams:136,Weather_GetParams:137,Weather_SetDate:138,Weather_GetDate:139,ODLine_Add:140,
ODLine_Update:141,ODLine_Delete:142,ODLine_Clear:143,ODLine_Focus:144,ODLine_Get:145,ODLine_Show:146,ODLine_Hide:147,ODLine_ShowAll:148,ODLine_HideAll:149,Misc_SetWindowResolution:150,Misc_CallBPFunction:151,Settings_SetHighlightColor:152,InfoTree_ShowByGroupId:153,InfoTree_HideByGroupId:154,InfoTree_HighlightByGroupId:155,InfoTree_DeleteByGroupId:156,EditHelper_Start:157,EditHelper_Quit:158,EditHelper_Finish:159,EditHelper_SetParam:160,Settings_SetFovX:161,TileLayer_Actor_ShowAll:162,TileLayer_Actor_HideAll:163,
CameraTour_Add:164,CameraTour_Update:165,CameraTour_Play:166,CameraTour_Stop:167,CameraTour_Delete:168,Tag_PopupWindow_Show:169,Tag_PopupWindow_Hide:170,Tag_PopupWindow_ShowAll:171,Tag_PopupWindow_HideAll:172,Settings_SetOceanColor:173,Misc_ShowAllFoliages:174,Misc_HideAllFoliages:175,Tools_StartPolygonClip:176,Tools_StopClip:177,TileLayer_SetAllowClip:178,Panorama_Add:179,Panorama_Update:180,Panorama_Delete:181,Panorama_Clear:182,Panorama_Focus:183,Panorama_Get:184,Decal_Add:185,Decal_Update:186,
Decal_Delete:187,Decal_Clear:188,Decal_Focus:189,Decal_FocusAll:190,Decal_Get:191,Misc_PlayMovie:192,Misc_StopMovie:193,Tools_SetMeasurement:194,Tools_StartMeasurement:195,Tools_StopMeasurement:196,Viewshed_Add:197,Viewshed_Update:198,Viewshed_Focus:199,Viewshed_Delete:200,Viewshed_Clear:201,Viewshed_Get:202,TileLayer_SetStyle:203,Tools_LineIntersect:204,Coord_PCS2GCS:205,Coord_GCS2PCS:206,DynamicWater_Add:207,DynamicWater_Update:208,DynamicWater_Focus:209,DynamicWater_Delete:210,DynamicWater_Clear:211,
DynamicWater_Get:212,DynamicWater_Show:213,DynamicWater_Hide:214,Quit:215,TileLayer_Actor_Unhighlight:216,InfoTree_Get:217,Reset:218,SaveProject:225,CustomObject_Clear:219,CustomObject_Highlight:220,CustomObject_Unhighlight:221,CustomObject_StopHighlight:222,CustomObject_CallFunction:223,CustomObject_AddByTileLayer:224,Tools_StartPlaneClip:226,Tools_StopPlaneClip:228,Tools_StartVolumeClip:229,Tools_UpdateVolumeClip:230,Tools_StopVolumeClip:231,Tools_StartSkylineAnalysis:232,Tools_StopSkylineAnalysis:233,
Tools_ExportSkyline:234,Tools_StartViewshedAnalysis:235,Tools_StopViewshedAnalysis:236,Camera_Move:237,TileLayer_SetCollision:238,TileLayer_Modifier_Add:239,TileLayer_Modifier_Update:240,TileLayer_Modifier_Delete:241,TileLayer_Modifier_Clear:242,Misc_StartProcess:243,Settings_SetCampassVisible:244,Tools_StartGeometryEdit:245,Tools_StopGeometryEdit:246,Settings_SetEnableInteract:247,Camera_PauseAnimation:248,Camera_ResumeAnimation:249,Settings_SetInteractiveMode:250,CameraTour_Pause:251,CameraTour_Resume:252,
Settings_SetTerrainAlpha:253,CustomMesh_Add:254,CustomMesh_Delete:255,CustomMesh_Clear:256,CustomMesh_Update:257,CustomMesh_Get:258,CustomMesh_Focus:259,CustomMesh_Show:260,CustomMesh_Hide:261,CustomMesh_ShowAll:262,CustomMesh_HideAll:263,Settings_EnableCameraMovingEvent:264,Marker_Add:265,Marker_Update:266,Marker_Get:267,Marker_Delete:268,Marker_Clear:269,Marker_Focus:270,Marker_FocusAll:271,Marker_Show:272,Marker_Hide:273,Marker_ShowAll:274,Marker_HideAll:275,Marker_ShowPopupWindow:276,Marker_HidePopupWindow:277,
Marker_ShowAllPopupWindow:278,Marker_HideAllPopupWindow:279,TileLayer_SetViewportVisible:280,TileLayer_GetObjectIDs:281,Polygon3D_StopHighlight:282,Tools_StartFloodFill:283,Tools_StopFloodFill:284,FloodFill_Add:285,FloodFill_Delete:286,FloodFill_Clear:287,FloodFill_Update:288,FloodFill_Get:289,FloodFill_Focus:290,FloodFill_Show:291,FloodFill_Hide:292,FloodFill_ShowAll:293,FloodFill_HideAll:294,Cesium3DTile_Add:295,Cesium3DTile_Delete:296,Cesium3DTile_Clear:297,Cesium3DTile_Update:298,Cesium3DTile_Get:299,
Cesium3DTile_Focus:300,Cesium3DTile_Show:301,Cesium3DTile_Hide:302,Cesium3DTile_ShowAll:303,Cesium3DTile_HideAll:304,TileLayer_GetActorInfoFromDB:305,ShapeFileLayer_Add:306,ShapeFileLayer_Delete:307,ShapeFileLayer_Clear:308,ShapeFileLayer_Update:309,ShapeFileLayer_Get:310,ShapeFileLayer_Focus:311,ShapeFileLayer_Show:312,ShapeFileLayer_Hide:313,ShapeFileLayer_ShowAll:314,ShapeFileLayer_HideAll:315,ShapeFileLayer_OpenShapeFileLayer:316,Weather_SimulateTime:317,TileLayer_Actor_GetInfo:318,Camera_GetAnimationList:319,
TileLayer_CutPolygon_Add:321,TileLayer_CutPolygon_Update:322,TileLayer_CutPolygon_Delete:323,TileLayer_CutPolygon_Clear:324,Marker3D_Add:327,Marker3D_Delete:328,Marker3D_Clear:329,Marker3D_Update:330,Marker3D_Get:331,Marker3D_Focus:332,Marker3D_Show:333,Marker3D_Hide:334,Marker3D_ShowAll:335,Marker3D_HideAll:336,Camera_GetAnimationImage:337,Tools_ReplaceTexture:338,Tools_RestoreTexture:339,Light_Add:340,Light_Delete:341,Light_Update:343,Light_Get:344,Light_Clear:342,Light_Focus:345,Light_Show:346,
Light_Hide:347,Light_ShowAll:348,Light_HideAll:349,WaterMesh_Add:354,WaterMesh_Update:355,WaterMesh_Delete:356,WaterMesh_Clear:357,WaterMesh_Get:358,WaterMesh_Focus:359,WaterMesh_Show:360,WaterMesh_Hide:361,WaterMesh_ShowAll:362,WaterMesh_HideAll:363,CustomObject_StartMove:364,CustomObject_StopMove:365,CustomObject_OverrideMaterial:366,CustomObject_RestoreMaterial:367,Settings_SetReport:368,Settings_GetReport:369,Settings_SetControl:370,Settings_GetControl:371,Settings_SetPostProcess:372,Settings_GetPostProcess:373,
Settings_SetCamera:374,Settings_GetCamera:375,CustomObject_SetViewportVisible:376,WaterFlowField_Add:377,WaterFlowField_Update:378,WaterFlowField_Delete:379,WaterFlowField_Clear:380,WaterFlowField_Get:381,WaterFlowField_Focus:382,WaterFlowField_Show:383,WaterFlowField_Hide:384,TileLayer_SetViewHeightRange:385,Tools_StartVisiblityAnalysis:386,Tools_StopVisiblityAnalysis:387,Tools_StartViewDomeAnalysis:388,Tools_StopViewDomeAnalysis:389,Tools_StartCutFillAnalysis:390,Tools_StopCutFillAnalysis:391,Tools_StartSunshineAnalysis:392,
Tools_StopSunshineAnalysis:393,Tools_StartTerrainSlopeAnalysis:394,Tools_StopTerrainSlopeAnalysis:395,Tools_StartContourLineAnalysis:396,Tools_StopContourLineAnalysis:397,TileLayer_GetAllFlattenData:398,Marker_SetupPOIAttachment:399,HeatMap3D_Add:400,HeatMap3D_Update:401,HeatMap3D_Focus:402,HeatMap3D_Delete:403,HeatMap3D_Show:404,HeatMap3D_Hide:405,HeatMap3D_Get:406,HeatMap3D_Clear:407,ShapeFileLayer_HighlightFeature:408,ShapeFileLayer_UnHighlightFeature:409,ShapeFileLayer_FocusFeature:410,ShapeFileLayer_GetFeatureInfo:411,
Misc_EnterMultiViewport:412,Misc_ExitMultiViewport:413,Misc_SetActivateMultiViewport:414,Misc_GetActivateMultiViewport:415,Panorama_Enter:416,Panorama_Exit:417,Panorama_Switch:418,VTPKService_Get:419,VTPKService_Set:420,SimulateTest__:421,Settings_CursorAutoSync:422,InfoTree_Focus:423,Marker_ShowByGroupId:424,Marker_HideByGroupId:425,Marker_DeleteByGroupId:426,ImageryLayer_Init:427,ImageryLayer_Add:428,Camera_LockBBox:429,Camera_UnLockBBox:430,Settings_SetWMTSLayerOpacity:431,Camera_EnterEarthCapture:432,
Camera_ExitEarthCapture:433,Tools_AnalysisPopupAttributes:434,Tools_AnalysisCloseAttributes:435,ImageryLayer_Show:436,ImageryLayer_Hide:437,ImageryLayer_Delete:438,Settings_GetInteractiveMode:439,VideoProjection_Clear:440,Misc_UpdateMultiViewport:441,Misc_downloadPakFiles:442,Settings_SetCampassPosition:443,Polygon3D_ShowAll:444,Polygon3D_HideAll:445,GeoJSONLayer_Add:446,GeoJSONLayer_Show:447,GeoJSONLayer_Hide:448,GeoJSONLayer_Delete:449,Vehicle_Add:450,Vehicle_Update:451,Vehicle_AddWayPoints:452,
Vehicle_ClearWayPoints:453,Vehicle_Focus:454,Vehicle_Show:455,Vehicle_Hide:456,Vehicle_Get:457,Vehicle_Delete:458,Vehicle_Clear:459,Vehicle_MoveTo:460,Misc_ConvexHull2D:461,HeatMap_AddVoxels:462,TileLayer_GetCollision:463,TileLayer_SetPointSize:464,Misc_QueryActorOrMaterial:465,CustomObject_StartGlow:466,CustomObject_StopGlow:467,Coord_Transform:468,RegisterJsCommunication:469,UnRegisterJsCommunication:470,Vehicle_CallBatchFunction:471,Camera_FlyAround:472,Marker3D_ShowByGroupId:473,Marker3D_HideByGroupId:474,
Marker3D_DeleteByGroupId:475,Camera_EnterWorldAnimation:476,Camera_ExitWorldAnimation:477,TileLayer_SetDecalAttach:478,Settings_SetGameBoardVisible:479,Antenna_Add:480,Antenna_Update:481,Antenna_Delete:482,Antenna_Clear:483,Antenna_Get:484,Antenna_Focus:485,Antenna_Show:486,Antenna_Hide:487,Tools_FunctionNavBar:488,VectorField_Add:489,VectorField_Delete:490,VectorField_Clear:491,WebUIJSON_Get:492,WebUIJSON_Set:493,VectorField_Update:494,WebUI_OpenACP:495,HydrodynamicModel2_Add:496,HydrodynamicModel2_Update:497,
HydrodynamicModel2_Delete:498,HydrodynamicModel2_Clear:499,HydrodynamicModel2_Get:500,HydrodynamicModel2_Focus:501,HydrodynamicModel2_Show:502,HydrodynamicModel2_Hide:503,FiniteElement_Add:505,FiniteElement_Update:506,FiniteElement_Delete:507,FiniteElement_Clear:508,FiniteElement_Get:509,FiniteElement_Focus:510,FiniteElement_Show:511,FiniteElement_Hide:512,WebUI_Minimize:513,WebUI_Maximize:514,WebUI_Restore:515,Tools_HideFunctionNavBar:516,Fluid_Add:517,Fluid_Update:518,Fluid_Reset:519,Fluid_Delete:520,
Fluid_Clear:521,Fluid_Get:522,Fluid_Focus:523,Fluid_Show:524,Fluid_Hide:525,Fluid_AddSource:526,Fluid_RemoveSource:527,TileLayer_Clear:528,HeatMap3D_Query:529,WebUI_EnterFullScreen:530,WebUI_ExitFullScreen:531,SignalWave_Add:532,SignalWave_Update:533,SignalWave_Delete:534,SignalWave_Clear:535,SignalWave_Focus:536,SignalWave_Show:537,SignalWave_Hide:538,SignalWave_Get:539,HeatMap3D_SetViewPort:540,WaterFlowField_SetViewPort:541,VectorField_SetViewPort:542,Settings_SetCharacterRotation:543,River_Add:544,
River_Update:545,River_Delete:546,River_Clear:547,River_Get:548,River_Focus:549,River_Show:550,River_Hide:551,HydrodynamicModel_Add:544,HydrodynamicModel_Update:545,HydrodynamicModel_Delete:546,HydrodynamicModel_Clear:547,HydrodynamicModel_Get:548,HydrodynamicModel_Focus:549,HydrodynamicModel_Show:550,HydrodynamicModel_Hide:551,HydroDynamic1D_Add:544,HydroDynamic1D_Update:545,HydroDynamic1D_Delete:546,HydroDynamic1D_Clear:547,HydroDynamic1D_Get:548,HydroDynamic1D_Focus:549,HydroDynamic1D_Show:550,
HydroDynamic1D_Hide:551,HydroDynamic2D_Add:544,HydroDynamic2D_Update:545,HydroDynamic2D_Delete:546,HydroDynamic2D_Clear:547,HydroDynamic2D_Get:548,HydroDynamic2D_Focus:549,HydroDynamic2D_Show:550,HydroDynamic2D_Hide:551,Settings_GetWKT:552,SmoothMoveCustomObject:553,VectorField_Focus:554,Misc_ReloadPak:555,Misc_SetPakVisibility:557,TileLayer_GetDBTabID:558,GeoJSONLayer_Focus:559,Misc_ProjectAssetCount:560,Marker_SetViewPort:561,Misc_SwitchShortcutKey:562,Tools_GetFunctionNavBar:563,GeoJSONLayer_HighlightFeature:564,
GeoJSONLayer_UnHighlightFeature:565,GeoJSONLayer_FocusFeature:566,GeoJSONLayer_UnHighlightFeaturesByLayerId:568,ImageryLayer_Focus:567,VectorField_Show:570,VectorField_Hide:571,VectorField_Get:572,MarkerLayer_Add:573,MarkerLayer_Update:574,MarkerLayer_Delete:575,MarkerLayer_Clear:576,MarkerLayer_Focus:577,MarkerLayer_Show:578,MarkerLayer_ShowAll:579,MarkerLayer_Hide:580,MarkerLayer_HideAll:581,MarkerLayer_Get:582,MarkerLayer_FocusMarker:583,Settings_Update:585,GeoJSONLayer_Clear:584,GeoJSONLayer_Update:586,
Marker3D_CallFunction:587,TileLayer_TileLayerReceiveWMTSDecal:588,CustomObject_ShowByGroupId:590,CustomObject_HideByGroupId:591,TileLayer_VTPK_Add:592,TileLayer_VTPK_AddLapMap:593,TileLayer_VTPK_Visible:594,TileLayer_VTPK_Delete:595,ImageryLayer_SetOpacity:431,ImageryLayer_MoveImageLayerToAnotherBefore:596,ImageryLayer_MoveImageLayerPrimary:597,ImageryLayer_MoveImageLayerLast:598,ImageryLayer_VTPK_Add:592,ImageryLayer_VTPK_Visible:593,ImageryLayer_VTPK_Delete:595,InfoTree_GetProjectTreeBPFunction:599,
InfoTree_CallProjectTreeBPFunction:600,Camera_ExportImage:601,CustomObject_Pause:602,CustomObject_Resume:603,CustomObject_SetMoveRate:604,Vehicle2_Add:614,Vehicle2_Update:615,Vehicle2_Focus:616,Vehicle2_Show:617,Vehicle2_Hide:618,Vehicle2_Get:619,Vehicle2_Delete:620,Vehicle2_Clear:621,Vehicle2_MoveTo:622,Vehicle2_AddMarker:623,Vehicle2_UpdateMarker:624,Vehicle2_ClearMarker:625,Camera_CancelFollow:626,SplineMesh_Add:627,SplineMesh_Update:628,SplineMesh_Delete:629,SplineMesh_Clear:630,SplineMesh_Focus:631,
SplineMesh_Get:632,SplineMesh_Show:633,SplineMesh_Hide:634,SplineMesh_ShowAll:635,SplineMesh_HideAll:636,Marker_SetGatherStyle:638,Marker3D_Attach:639,SettingsPanel_setPak:640,Command_End:1E4},Ca=!1;(()=>{if(!Ca){Ca=!0;for(let a in k)k[k[a]]=a}})();class Qa{constructor(a,b){this.id=a;this.visible=b}}class Ra{constructor(a,b,c,e){this.id=a.toString();this.coordinate=b;this.radius=c;this.heatValue=e}}class Sa{constructor(a,b,c,e,d,g,h,q,l,f,m,t,x,r){this.id=a.toString();this.coordinate=b;this.imagePath=
c;this.imageSize=e;this.url=d;this.text=g;this.range=h||[1,1E5];this.showLine=!!q;this.textColor=l||[0,0,0,1];this.textBackgroundColor=f||[1,1,1,.85];this.textBorderColor=m||[0,0,0,0];this.textRange=t||1E5;this.hoverImagePath=x;this.autoHidePopupWindow=r||!0}}class Ta{constructor(a,b,c){this.url=a;this.width=b;this.height=c}}class Ua{constructor(a,b,c,e,d,g,h,q){this.id=a.toString();this.coordinate=b;this.contentURL=c.url;this.contentSize=[c.width,c.height];this.popupURL=e.url;this.popupSize=[e.width,
e.height];this.pivot=d;this.range=g;this.autoHidePopupWindow=h||!0;this.popupPos=q}}class Va{constructor(a,b,c,e,d,g){this.id=a.toString();this.coordinates=K(b);this.color=D(c);this.heightRange=e;this.intensity=d;this.depthTest=g||!0}}class Wa{constructor(a,b,c,e,d,g,h,q){this.id=a.toString();this.x=b;this.y=c;this.width=e;this.height=d;this.normalImage=g;this.hoverImage=h;this.tooltip=q}}class Xa{constructor(a,b,c,e,d,g,h,q,l){this.id=a.toString();this.x=b;this.y=c;this.width=e;this.height=d;this.imageSequecePath=
g;this.imageSequeceLength=h;this.loop=!!q;this.interactable=!!l}}class Ya{constructor(a,b,c,e,d,g,h){this.id=a.toString();this.duration=b;this.thickness=c;this.interval=e;this.velocity=d;this.color=D(g);this.coordinates=h}}class Za{constructor(a,b,c,e,d,g){this.id=a.toString();this.coordinate=b;this.radius=c;this.rippleNumber=e;this.color=D(d);this.brightness=g}}class $a{constructor(a,b,c,e,d,g,h,q){this.id=a.toString();this.style=b;this.coordinates=K(c);this.color=D(e);this.height=d;this.intensity=
g;this.tillingX=h;this.tillingY=q}}class ab{constructor(a,b,c,e,d,g,h,q,l,f){this.id=a.toString();this.color=D(b);this.coordinates=c;this.style=void 0==e?0:e;if(0>this.style||5<this.style)this.style=0;this.thickness=void 0==d?20:d;this.brightness=void 0==g?.5:g;this.flowRate=void 0==h?.5:h;this.tiling=q||0;this.depthTest=l||!0;this.shape=void 0==f?0:f}}class bb{constructor(a,b,c,e,d,g,h,q,l,f,m,t,x,r,y,v,A){this.id=a.toString();this.color=D(b);this.coordinates=c;this.flowRate=void 0==e?.5:e;this.brightness=
void 0==d?.5:d;this.bendDegree=void 0==g?.5:g;this.tiling=h||0;this.lineThickness=void 0==q?20:q;this.flowPointSizeScale=void 0==l?20:l;this.labelSizeScale=void 0==f?100:f;this.lineShape=void 0==m?1:m;this.lineStyle=t||0;this.flowShape=x||0;this.startPointShape=r||0;this.endPointShape=y||0;this.startLabelShape=v||0;this.endLabelShape=A||0;this.endLabelStyle=this.startLabelStyle=this.endPointStyle=this.startPointStyle=this.flowStyle=0}}class cb{constructor(a,b,c,e,d,g,h,q){this.id=a.toString();this.color=
D(b);this.coordinates=K(c);this.framecolor=D(e);this.framethickness=d;this.depthTest=g||!0;this.brightness=h;this.style=q;if(0>this.style||10<this.style)this.style=0}}class db{constructor(a,b,c,e,d,g){this.id=a.toString();this.imagePath=b;this.coordinate=c;this.yaw=e;this.groupId=d||"";this.userData=g||""}}class eb{constructor(a,b,c,e,d,g,h,q){this.id=a.toString();this.order=b;this.texturePath=c;this.location=e;this.rotation=d;this.scale=g;this.groupId=h;this.userData=q}}class fb{constructor(a,b,
c,e,d){this.id=a.toString();this.fileName=b;this.location=c;this.rotation=e;this.scale=d}}class gb{constructor(a,b,c,e,d,g,h){this.id=a;this.pakFilePath=b;this.assetPath=c;this.location=e;this.rotation=d;this.scale=g;this.smoothMotion=h}}class hb{constructor(a,b,c,e,d,g,h){this.id=a;this.tileLayerId=b;this.objectId=c;this.location=e;this.rotation=d;this.scale=g;this.smoothMotion=h}}class ib{constructor(a,b,c,e,d,g,h){this.id=a;this.videoURL=b;this.location=c;this.rotation=e;this.fov=d;this.aspectRatio=
g;this.distance=h}}class jb{constructor(a,b,c,e,d){this.id=a;this.coordinates=K(b);this.style=c;this.groupId=e;this.userData=d}}class kb{constructor(a,b,c,e,d){this.actorTag=a;this.objectName=b;this.functionName=c;this.paramType=e;this.paramValue=d}}class fa{constructor(a,b){this.tileLayerId=a;this.tileLayerActorNames=I(b)}}class lb{constructor(a,b,c,e){this.index=a;this.time=b;this.location=c;this.rotation=e}}class mb{constructor(a,b,c){this.id=a.toString();this.name=b.toString();this.keyFrames=
c}}class nb{constructor(){this.queue=[];this.dataMap=new Map}enqueue(a){if(null==a||void 0==a||!a.callbackIndex)return!1;this.queue.push(a.callbackIndex);this.dataMap.set(a.callbackIndex,a);return!0}dequeue(){if(0!=this.queue.length){const a=this.queue.shift();return this.dataMap.get(a)}}removeData(a){this.dataMap.has(a)&&this.dataMap.delete(a)}dataSize(){return this.dataMap.size}queueSize(){return this.queue.length}}class ob extends nb{constructor(a){super();this.onSendAPI=a;this.timer=null;this.isMainThreadBusy=
!1}onMainThreadBusy(a){this.isMainThreadBusy=a.busy}push(a){if(a.__noResponse)this.onSendAPI(a);else 0==this.dataSize()?(this.enqueue(a),this.callNext()):1E4<this.dataSize()?console.warn("The api queue has exceeded 10000, and subsequent calls will be ignored!"):this.enqueue(a)}callNext(a){a&&this.removeData(a);this.timer&&clearTimeout(this.timer);0!=this.dataSize()&&(a=this.dequeue())&&(this._callNextForTimeout(3E3),this.onSendAPI(a))}_callNextForTimeout(a){this.timer=setTimeout(()=>{this.isMainThreadBusy?
this._callNextForTimeout(5E3):this.callNext()},a)}}class M{constructor(a,b,c){this.int=a;this.type=b;this.colorProps=c;this._useBatchUpdate=!1;this._tempUpdateData=[]}_checkCommand(a){a=this.type+a;let b=k[a];void 0==b&&this.int.logWithColor("red",`Invalid command: ${a}`);return b}_convertFilePath(a){return this.int.resourcesPath?a.replace("@path:",this.int.resourcesPath+"/"):a}_processProps(a){a=B(a);for(var b of a)for(let e in b)if("string"==typeof b[e]&&b[e].startsWith("@path:")&&(b[e]=this._convertFilePath(b[e])),
"imagesArray"==e)for(let d=0;d<b[e].length;d++)b[e][d]=this._convertFilePath(b[e][d]);for(let e of a)"string"!=typeof e.id&&(e.id=e.id.toString());if(this.colorProps){b=this.colorProps.split("|");for(var c of a)for(let e of b)c.hasOwnProperty(e)&&(c[e]=D(c[e]))}if(-1!=["Polygon","Polygon3D","HighlightArea","DynamicWater"].indexOf(this.type))for(let e of a)e.coordinates=K(e.coordinates);for(let e of a)e.hasOwnProperty("userData")&&(c=e.userData,this._isJsonString(c)&&(e.userData=c.replace(/"/g,"~!@~!@~!@")))}_isJsonString(a){try{return"string"===
typeof a?(JSON.parse(a),!0):!1}catch(b){return!1}}_add(a,b){this._processProps(a);return this.int.call({command:this._checkCommand("_Add"),data:B(a)},b)}_update(a,b){this._processProps(a);return this.int.call({command:this._checkCommand("_Update"),data:B(a)},b)}_delete(a,b){return this.int.call({command:this._checkCommand("_Delete"),ids:I(a)},b)}_clear(a){return this.int.call0(this._checkCommand("_Clear"),a)}_get(a,b){return this.int.call({command:this._checkCommand("_Get"),ids:I(a)},b)}_focus(a,
b,c,e,d){"function"==typeof c&&(d=c,c=void 0);"function"==typeof e&&(d=e,e=null);return this.int.call({command:this._checkCommand("_Focus"),ids:I(a),distance:b||0,flyTime:c,rotation:e},d)}_focusAll(a,b,c,e){"function"==typeof b&&(e=b,b=void 0);"function"==typeof c&&(e=c,c=null);return this.int.call({command:this._checkCommand("_FocusAll"),ids:[],distance:a||0,flyTime:b,rotation:c},e)}_show(a,b){return this.int.call({command:this._checkCommand("_Show"),ids:I(a)},b)}_showAll(a){return this.int.call0(this._checkCommand("_ShowAll"),
a)}_hide(a,b){return this.int.call({command:this._checkCommand("_Hide"),ids:I(a)},b)}_hideAll(a){return this.int.call0(this._checkCommand("_HideAll"),a)}_updateOneProp(a,b,c,e){if(this._useBatchUpdate){e=!1;for(var d of this._tempUpdateData)if(d.id==a.toString()){e=!0;d[b]=c;break}e||(e={},e.id=a.toString(),e[b]=c,this._tempUpdateData.push(e))}else return d={},d.id=a.toString(),d[b]=c,this.update(d,e)}updateBegin(){this._useBatchUpdate=!0;this._tempUpdateData=[]}updateEnd(a){a=this.update(this._tempUpdateData,
a);this._tempUpdateData=null;this._useBatchUpdate=!1;return a}test(){alert("test")}}class pb extends M{constructor(a){super(a,"Beam","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setDuration(a,
b,c){return super._updateOneProp(a,"duration",b,c)}setThickness(a,b,c){return super._updateOneProp(a,"thickness",b,c)}setInterval(a,b,c){return super._updateOneProp(a,"interval",b,c)}setVelocity(a,b,c){return super._updateOneProp(a,"velocity",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}}class qb extends M{constructor(a){super(a,"CameraTour")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,
b)}delete(a,b){return super._delete(a,b)}play(a,b){return this.int.call({command:k.CameraTour_Play,ids:I(a)},b)}setMouseClickToPause(a,b,c){return this.int.call({command:k.CameraTour_Play,ids:I(a),mouseInput:b},c)}setTime(a,b,c){return this.int.call({command:k.CameraTour_Play,ids:I(a),playerTime:b},c)}pause(a){return this.int.call0(k.CameraTour_Pause,a)}resume(a){return this.int.call0(k.CameraTour_Resume,a)}stop(a){return this.int.call({command:k.CameraTour_Stop},a)}setUserData(a,b,c){return super._updateOneProp(a,
"userData",b,c)}setDuration(a,b,c){return super._updateOneProp(a,"duration",b,c)}setKeyFrames(a,b,c){return super._updateOneProp(a,"keyFrames",b,c)}setName(a,b,c){return super._updateOneProp(a,"name",b,c)}}class rb extends M{constructor(a){super(a,"Cesium3DTile")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,
b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setTileURL(a,b,c){return super._updateOneProp(a,"tileURL",b,c)}}class sb extends M{constructor(a){super(a,"CustomMesh","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,
b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setIndices(a,b,c){return super._updateOneProp(a,"indices",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",b,c)}}const va={OK:0,InvalidParameters:1,InternalError:2,ResourceNotFound:3,AcpProjWKTNotSet:4,CoordinateConversionFailed:5,IDExists:6,InvalidRequestType:7,InvalidRequestString:8,NoCommand:9,DataTypeNotSupport:10,InvalidOperation:11,ProjectNotOpened:12,CodeMax:65535},ra={MouseClick:1,MouseMove:2,MouseHover:4},
ca={V1:1,V2:2,V3:4,V4:8,All:255},Da={ClearObjects:1,ResetSettings:2,ResetCamera:4};class tb extends M{constructor(a){super(a,"CustomObject")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d,g){"function"==typeof c&&(g=c,c=void 0);"function"==typeof e&&(g=e,e=null);"function"==typeof d&&(g=d,d=0);1<d&&(e=2==d?[-45,0,0]:3==d?[0,0,0]:4==d?[-90,0,0]:5==d?[90,0,0]:6==d?[0,90,0]:7==d?[0,270,0]:[0,
0,0]);!R(a)&&-1==b&&R(c)&&R(e)&&R(d)&&(d=1,b=0);return this.int.call({command:k.CustomObject_Focus,ids:I(a),distance:b||0,flyTime:c,rotation:e||null,follow:d||0},g)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}addByTileLayer(a,b){if(a instanceof Array)for(let c of a)this._fixTileLayerProperty(c);else this._fixTileLayerProperty(a);return this.int.call({command:k.CustomObject_AddByTileLayer,data:B(a)},b)}_fixTileLayerProperty(a){a.tileLayerId instanceof
Array||(a.tileLayerId=B(a.tileLayerId));a.objectId instanceof Array||(a.objectId=B(a.objectId))}highlight(a,b){return this.int.call({command:k.CustomObject_Highlight,ids:I(a)},b)}unhighlight(a,b){return"undefined"==typeof a||"function"==typeof a?("function"==typeof a&&(b=a),this.int.call({command:k.CustomObject_StopHighlight},b)):this.int.call({command:k.CustomObject_Unhighlight,ids:I(a)},b)}glow(a,b){a=B(a);if(a instanceof Array)for(let c of a){let e;e=D(c.color);c.colors=[{color:[e[0]/2,e[1]/2,
e[2]/2],value:0},{color:c.color,value:1}]}else o.colors=[{color:o.color,value:0},{color:o.color,value:1}];return this.int.call({command:k.CustomObject_StartGlow,data:B(a)},b)}stopGlow(a,b){return this.int.call({command:k.CustomObject_StopGlow,ids:I(a)},b)}getBPFunction(a,b){return this.int.call({command:k.Misc_QueryActorOrMaterial,idOrPaths:B(a)},b)}callBPFunction(a,b,c,e,d,g){this.callFunction(a,b,c,e,d,g)}callFunction(a,b,c,e,d,g){return this.int.call({command:k.CustomObject_CallFunction,data:B({id:a.toString(),
functionName:b,paramType:c,paramValue:e,parameters:d})},g)}callBPFunction(a,b){this.callBatchBPFunction(a,b)}callBatchBPFunction(a,b){this.callFunction4CustomObjectArr(a,b)}callBatchFunction(a,b){this.callFunction4CustomObjectArr(a,b)}callFunction4CustomObjectArr(a,b){return this.int.call({command:k.CustomObject_CallFunction,data:B(a)},b)}setLocation(a,b,c,e){return"function"==typeof c?super._updateOneProp(a,"location",b,c):"number"==typeof c?this.int.call({command:k.CustomObject_Update,data:[{id:a,
location:b,smoothTime:c}]},e):super._updateOneProp(a,"location",b,e)}setSmoothTime(a,b,c){return super._updateOneProp(a,"smoothTime",b,c)}moveTo(a,b){return this.int.call({command:k.CustomObject_Update,data:B(a)},b)}setRotation(a,b,c){return super._updateOneProp(a,"rotation",b,c)}setLocalRotation(a,b,c){return super._updateOneProp(a,"localRotation",b,c)}setScale(a,b,c){return super._updateOneProp(a,"scale",b,c)}setSmoothMotion(a,b,c){return super._updateOneProp(a,"smoothMotion",b,c)}setTintColor(a,
b,c){a={ids:B(a),color:D(b)};return this.int.call({command:k.CustomObject_SetTintColor,data:B(a)},c)}overrideMaterial(a,b){return this.int.call({command:k.CustomObject_OverrideMaterial,data:B(a)},b)}restoreMaterial(a,b){return this.int.call({command:k.CustomObject_RestoreMaterial,ids:B(a)},b)}setViewportVisible(a,b,c){let e=[];e.push({viewportIndex:1,viewportVisible:!!(b&ca.V1)});e.push({viewportIndex:2,viewportVisible:!!(b&ca.V2)});e.push({viewportIndex:3,viewportVisible:!!(b&ca.V3)});e.push({viewportIndex:4,
viewportVisible:!!(b&ca.V4)});return this.int.call({command:k.CustomObject_SetViewportVisible,id:a,data:e},c)}startMove(a,b,c,e){return this.int.call({command:k.CustomObject_StartMove,id:a,coordinateType:b,data:B(c)},e)}pause(a,b){return this.int.call({command:k.CustomObject_Pause,ids:B(a)},b)}resume(a,b){return this.int.call({command:k.CustomObject_Resume,ids:B(a)},b)}setMoveRate(a,b){return this.int.call({command:k.CustomObject_SetMoveRate,data:B(a)},b)}stop(a,b){return this.int.call({command:k.CustomObject_StopMove,
ids:B(a)},b)}showByGroupId(a,b){return this.int.call({command:k.CustomObject_ShowByGroupId,groupIds:I(a)},b)}hideByGroupId(a,b){return this.int.call({command:k.CustomObject_HideByGroupId,groupIds:I(a)},b)}}class ub extends M{constructor(a){super(a,"CustomTag")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}focusAll(a,b,c,e){return super._focusAll(a,b,c,e)}show(a,
b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setAutoHidePopupWindow(a,b,c){return super._updateOneProp(a,"autoHidePopupWindow",b,c)}}class vb extends M{constructor(a){super(a,"Decal")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}focusAll(a,
b,c,e){return super._focusAll(a,b,c,e)}get(a,b){return super._get(a,b)}}class wb extends M{constructor(a){super(a,"DynamicWater")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}focusAll(a,b,c,e){return super._focusAll(a,b,c,e)}get(a,b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",K(b),c)}setMaterialPath(a,b,c){return super._updateOneProp(a,
"materialPath",b,c)}setStyle(a,b,c){return super._updateOneProp(a,"style",b,c)}}class xb extends M{constructor(a){super(a,"FiniteElement","")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class yb extends M{constructor(a){super(a,"FloodFill","color")}add(a,b){return super._add(a,
b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setRange(a,b,c,e){b instanceof Array&&c instanceof Array?0<b.length&&0<c.length?this.update({id:a,min:b,max:c},e):console.error("\u6570\u7ec4\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a."):
console.error("\u6570\u7ec4\u53c2\u6570\u683c\u5f0f\u4e0d\u6b63\u786e.")}setMin(a,b,c){return super._updateOneProp(a,"min",b,c)}setMax(a,b,c){return super._updateOneProp(a,"max",b,c)}setSeed(a,b,c){return super._updateOneProp(a,"seed",b,c)}setElevation(a,b,c){return super._updateOneProp(a,"elevation",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setPrecision(a,b,c){return super._updateOneProp(a,"precision",b,c)}}class zb extends M{constructor(a){super(a,"Fluid","color")}add(a,
b){this._fixMarkerData(a,"add");return super._add(a,b)}_fixMarkerProperty(a,b){b=[[[120,250,350,3],[.6,1,1,3E-5],[.117708,.294268,.470833,0],[1,1.2,1.15,.65],0],[[70,180,350,5],[.05,.25,.3,1.5E-4],[.15,.3,.45,0],[1,1.2,1.15,.65],0],[[140,180,300,2.5],[.14,.33,.36,2E-4],[.077778,.203,.333333,0],[.9,1,1.1,.6],0],[[120,180,300,2.3],[.21,.27,.32,2.5E-4],[.195513,.244391,.317709,0],[.8,1,1.1,.7],0],[[120,250,350,2.5],[.5,.9,1,4E-5],[.09,.18,.2,0],[.9,1,1,.75],0],[[120,250,350,1.5],[.5,.9,1,4E-5],[.09,
.18,.2,0],[.9,1,1,.75],0],[[80,280,400,4.5],[.15,.35,.3,7E-5],[.092187,.184375,.184375,0],[1.5,1.2,.8,.5],0],[[70,180,330,1.8],[.1,.3,.4,3E-4],[.1,.4,.4,4E-4],[.8,1,1,.9],.5],[[215,200,150,1.6],[.8,1,1.1,1.1E-4],[.358,.357187,.3043,1],[.75,.75,.653692,.8],0],[[220,280,250,6],[.38,.33,.2,2.5E-5],[.216667,.191617,.037043,0],[1,1,.8,.35],0],[[200,200,140,1],[.2,.2,.05,6E-4],[.2,.198243,.137327,.01],[.95,1,.85,.5],.25],[[200,20,20,2],[.3,.2,.05,5E-4],[.870833,.117,.074,0],[1.1,1,.7,.4],0],[[240,200,30,
12],[1,.75,.2,2E-4],[.5125,.461171,.145208,0],[1.1,1,.7,.6],0],[[70,170,350,8],[.1,.3,.45,1.7E-4],[.139727,.279454,.405208,1],[1,1.1,1.1,.7],.9],[[60,100,200,98],[.15,.2,.3,1.4E-5],[.25,.31,.34,0],[.95,1.12,1.2,.65],.25],[[100,160,350,6.75],[.3,.35,.35,4E-4],[.3,.35,.35,7E-4],[1,1,.9,.7],1],[[290,260,140,.8],[.95,.8,.6,4E-4],[.166667,.106061,.061033,3E-4],[1.15,1,.9,.6],0],[[250,280,220,1],[.8,.83,.83,2.9E-4],[.187552,.216667,.187552,0],[1,1,.8,.35],1],[[320,350,300,.8],[1.3,1.1,.6,3E-4],[.317708,
.317708,.166363,0],[.9,1,.6,.5],1],[[80,500,400,8],[.07,.3,.65,6E-5],[.3,.65,.65,0],[1,1,.9,.7],1],[[180,500,250,6],[.4,1,.7,4E-5],[.3736,.463542,.263357,0],[1,1.1,1,.8],1],[[460,600,270,2],[.2009,.30063,.500225,5.5E-4],[.461049,.475,.290938,0],[1,1.05,.85,.6],1],[[260,340,200,7],[.4,.53,.5,2.3E-4],[.461049,.475,.290938,0],[1,1.05,.85,.8],1],[[333,270,200,12],[.7,.8,.4,2E-4],[.461049,.475,.290938,0],[1,.9,.8,.8],1]];if(za(a.style)&&0<=a.style&&23>=a.style){var c=a.style;a.color1=b[c][0];a.color2=
b[c][1];a.color3=b[c][2];a.color4=b[c][3];a.color5=b[c][4]}else 23<a.style&&27>=a.style||(a.style=0,a.color1=b[0][0],a.color2=b[0][1],a.color3=b[0][2],a.color4=b[0][3],a.color5=b[0][4]);if(0<a.sources.length)for(b=0;b<a.sources.length;b++){c=a.sources[b].velocity;let e=c[1];-2>=c[0]&&(a.sources[b].velocity[0]=-2);2<=e&&(a.sources[b].velocity[1]=2)}}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){return super._update(a,
b)}pause(a,b){return this.update({id:B(a),active:!1},b)}continue(a,b){return this.update({id:B(a),active:!0},b)}reset(a,b){return this.int.call({command:k.Fluid_Reset,ids:B(a)},b)}addSource(a,b){return this.int.call({command:k.Fluid_AddSource,data:B(a)},b)}removeSource(a,b){return this.int.call({command:k.Fluid_RemoveSource,data:B(a)},b)}continueSource(a,b){if(a instanceof Array)for(let c of a)c.sources.active=!0;return this.update(a,b)}stopSource(a,b){if(a instanceof Array)for(let c of a)c.sources.active=
!1;return this.update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class Ab extends M{constructor(a){super(a,"GeoJSONLayer")}add(a,b){this.load(a,b)}update(a,b){a.sourceJson&&(a.url=a.sourceJson);"object"==typeof a.url&&(a.url=JSON.stringify(a.url));Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],
a.maxVisibleHeight=a.viewHeightRange[1]);return super._update(a,b)}load(a,b){a.sourceJson&&(a.url=a.sourceJson);"object"==typeof a.url&&(a.url=JSON.stringify(a.url));Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1]);return super._add(a,b)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}highlightFeature(a,
b,c){return this.int.call({command:k.GeoJSONLayer_HighlightFeature,data:B({id:a,featureIds:[b]})},c)}stopHighlightFeature(a,b,c){return this.int.call({command:k.GeoJSONLayer_UnHighlightFeature,data:B({id:a,featureIds:[b]})},c)}highlightFeatures(a,b){return this.int.call({command:k.GeoJSONLayer_HighlightFeature,data:B(a)},b)}stopHighlightFeatures(a,b){return this.int.call({command:k.GeoJSONLayer_UnHighlightFeature,data:B(a)},b)}stopAllHighlightFeaturesById(a,b){return this.int.call({command:k.GeoJSONLayer_UnHighlightFeaturesByLayerId,
ids:B(a)},b)}focusFeature(a,b,c,e,d,g){return this.int.call({command:k.GeoJSONLayer_FocusFeature,data:B({id:a,featureIds:[b]}),distance:c,flyTime:e,rotation:d},g)}setViewHeightRange(a,b,c,e){return this.int.call({command:k.GeoJSONLayer_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},e)}}class Bb extends M{constructor(a){super(a,"HeatMap")}_checkBBox(a){if(a instanceof Array){if(6!=a.length||a[0]>a[3]||a[1]>a[4]||a[2]>a[5])return!1}else return!1;return!0}add(a,b,c,e,d,g,h,q,l,f,m,t,x,r,
y){if(!this._checkBBox(b))return this.int.logWithColor("red","Invalid bbox value"),!1;if("undefined"==typeof d||"function"==typeof d)return"function"==typeof d&&(y=d),this.int.call({command:k.HeatMap_Add,id:a.toString(),bbox:b,range:c,data:B(e),style:-1},y);if(0==d||1==d)return this.int.call({command:k.HeatMap_Add,id:a.toString(),bbox:b,range:c,data:B(e),style:d,textureSize:g,opacityMode:h,opacityRange:q,blur:l,colors:f,blendMode:m,light:t,binaryFile:x,updateTime:r},y);"function"==typeof d&&(y=d);
return this.int.call({command:k.HeatMap_Add,id:a.toString(),bbox:b,range:c,data:B(e),style:-1},y)}update(a,b,c,e,d,g,h,q,l,f,m,t,x,r,y){a={command:k.HeatMap_Update,id:a.toString()};b&&(this._checkBBox(b)||this.int.logWithColor("red","Invalid bbox value"),a.bbox=b);c&&(a.range=c);e&&(a.data=B(e));"undefined"!=typeof d&&"function"!=typeof d&&(g&&(a.textureSize=g),h&&(a.opacityMode=h),q&&(a.opacityRange=q),l&&(a.blur=l),f&&(a.colors=f),m&&(a.blendMode=m),t&&(a.light=t),x&&(a.binaryFile=x),r&&(a.updateTime=
r));"function"==typeof d&&(y=d);return this.int.call(a,y)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}addPoints(a,b,c){return this.int.call({command:k.HeatMap_AddPoints,id:a,data:B(b)},c)}removePoints(a,b,c){return this.int.call({command:k.HeatMap_RemovePoints,id:a,pointIds:B(b)},c)}setRange(a,b,c){return this.int.call({command:k.HeatMap_Update,
id:a,range:b},c)}setBBox(a,b,c){return this._checkBBox(b)?this.int.call({command:k.HeatMap_Update,id:a,bbox:b},c):(this.int.logWithColor("red","Invalid bbox value"),!1)}}class Cb extends M{constructor(a){super(a,"HeatMap3D")}_checkBBox(a){if(a instanceof Array){if(6!=a.length||a[0]>a[3]||a[1]>a[4]||a[2]>a[5])return!1}else return!1;return!0}addByImages(a,b){this.add(a,b)}addByBinaryFiles(a,b){this.add(a,b)}addByHeatPoints(a,b){this.add(a,b)}addByVoxels(a,b){this.add(a,b)}addBySparseVoxels(a,b){this.add(a,
b)}addByTif(a,b){this.add(a,b)}add(a,b){return super._add(a,b)}addHeatPoints(a,b){this.addVoxels(a,b)}addVoxels(a,b){return this.int.call({command:k.HeatMap_AddVoxels,data:B(a)},b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}queryVoxel(a,b,c){return this.int.call({command:k.HeatMap3D_Query,
id:a,coordinate:b},c)}setDisplayMode(a,b,c){return this.int.call({command:k.HeatMap3D_Update,id:a,displayMode:b},c)}setViewportVisible(a,b,c){let e=[];e.push({viewportIndex:1,viewportVisible:!!(b&Viewport.V1)});e.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});e.push({viewportIndex:3,viewportVisible:!!(b&Viewport.V3)});e.push({viewportIndex:4,viewportVisible:!!(b&Viewport.V4)});return this.int.call({command:k.HeatMap3D_SetViewPort,id:a,data:e},c)}}class Db extends M{constructor(a){super(a,
"HighlightArea","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setHeightRange(a,b,c){return super._updateOneProp(a,"heightRange",
b,c)}setIntensity(a,b,c){return super._updateOneProp(a,"intensity",b,c)}setDepthTest(a,b,c){return super._updateOneProp(a,"depthTest",b,c)}}class Eb extends M{constructor(a){super(a,"HydrodynamicModel2")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setTime(a,
b){return this.update(a,b)}startPlay(a,b){if(a instanceof Array){for(let c of a)c.playing=!0;return this.update(a,b)}return this.update({id:a.id,displayMode:a.displayMode,time:a.time,playing:!0},b)}stopPlay(a,b){if(a instanceof Array){let c=[];for(let e of a)a={},a.id=e,a.playing=!1,c.push(a);return this.update(c,b)}return this.update({id:a,playing:!1},b)}pause(a,b){if(a instanceof Array){let c=[];for(let e of a)a={},a.id=e,a.paused=!0,c.push(a);return this.update(c,b)}return this.update({id:a,paused:!0},
b)}resume(a,b){if(a instanceof Array){let c=[];for(let e of a)a={},a.id=e,a.paused=!1,c.push(a);return this.update(c,b)}return this.update({id:a,paused:!1},b)}}class Fb extends M{constructor(a){super(a,"Antenna")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}
class Gb extends M{constructor(a){super(a,"HydroDynamic1D","arrowColor|waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class Hb extends M{constructor(a){super(a,"HydroDynamic2D","arrowColor|waterColor")}addByTif(a,b){this.add(a,b)}addBySdb(a,b){this.add(a,
b)}addByBin(a,b){this.add(a,b)}addByShp(a,b){this.add(a,b)}_fixMarkerProperty(a,b){"add"==b&&(a.waterDepthAttributeName&&(a.waterDepth=a.waterDepthAttributeName),a.uvAttributeName&&(a.flowField=a.uvAttributeName),a.sdb&&(a.sdbFilePath=a.sdb))}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}add(a,b){this._fixMarkerData(a,"add");return super._add(a,b)}update(a,b){this._fixMarkerData(a,"add");return super._update(a,b)}delete(a,b){return super._delete(a,
b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class Ib extends M{constructor(a){super(a,"HydrodynamicModel","arrowColor|waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,
b)}get(a,b){return super._get(a,b)}}class Jb extends M{constructor(a){super(a,"ImageryLayer")}init(a,b){return this.int.call({command:k.ImageryLayer_Init,xmlPath:a.xmlUrl,layerName:a.layerName,tileMatrixName:a.tileMatrixName,ogcEPSG:a.ogcEPSG,cachePath:a.cachePath,mapMode:a.mapMode,renderMode:a.renderMode},b)}add(a,b){const c=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){return a instanceof Array?new Promise(e=>$jscomp.asyncExecutePromiseGeneratorFunction(function*(){let d;
for(let g of a)if(d=yield c.addOne(g,b),0!=d.result){e(d);break}e(d)})):c.addOne(a,b)})}addOne(a,b){return this.int.call({command:k.ImageryLayer_Add,id:a.id,url:a.url,xmlPath:a.xmlPath,layerName:a.layerName,tileMatrixName:a.tileMatrixName,ogcEPSG:a.ogcEPSG},b)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}delete(a,b){return super._delete(a,b)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}setDrawOrder(a,b,c){return this.int.call({command:k.ImageryLayer_MoveImageLayerToAnotherBefore,
id1:a,id2:b},c)}setDrawTop(a,b){return this.int.call({command:k.ImageryLayer_MoveImageLayerPrimary,id1:a},b)}setDrawBottom(a,b){return this.int.call({command:k.ImageryLayer_MoveImageLayerLast,id1:a},b)}addVTPK(a,b){return this.int.call({command:k.ImageryLayer_VTPK_Add,id:a},b)}deleteVTPK(a,b){return this.int.call({command:k.ImageryLayer_VTPK_Delete,id:a},b)}setVTPKVisable(a,b,c){return this.int.call({command:k.ImageryLayer_VTPK_Visible,id:a,visible:b},c)}}class Kb extends M{constructor(a){super(a,
"Light","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}}class Lb extends M{constructor(a){super(a,"Marker","fontColor|fontOutlineColor|textBackgroundColor|lineColor")}add(a,b){this._fixMarkerData(a,
"add");return super._add(a,b)}_fixMarkerProperty(a,b){a.textColor&&(a.fontColor=a.textColor);a.url&&(a.popupURL=a.url);a.dispalyMode&&(a.displayMode=a.dispalyMode);R(a.displayMode)&&"add"==b&&(a.displayMode=4);a.popupURL&&!a.popupSize&&"add"==b&&(a.popupSize=[600,400]);0==a.showLine&&(a.lineSize=[0,0]);1!=a.showLine||null!=a.lineSize&&void 0!=a.lineSize||(a.lineSize=[2,100]);4==a.displayMode&&0<a.autoDisplayModeSwitchFirstRatio&&0<a.autoDisplayModeSwitchSecondRatio&&(a.autoDisplayModeSwitchRatio=
[a.autoDisplayModeSwitchFirstRatio,a.autoDisplayModeSwitchSecondRatio]);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){this._fixMarkerData(a,"update");return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}focusAll(a,
b,c,e){return super._focusAll(a,b,c,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}showPopupWindow(a,b){return this.int.call({command:k.Marker_ShowPopupWindow,ids:I(a)},b)}hidePopupWindow(a,b){return this.int.call({command:k.Marker_HidePopupWindow,ids:I(a)},b)}showAllPopupWindow(a){return this.int.call0(k.Marker_ShowAllPopupWindow,a)}hideAllPopupWindow(a){return this.int.call0(k.Marker_HideAllPopupWindow,
a)}get(a,b){return super._get(a,b)}setGroupId(a,b,c){return super._updateOneProp(a,"groupId",b,c)}setUserData(a,b,c){return super._updateOneProp(a,"userData",b,c)}setAnchors(a,b,c){return this.setScreenOffset(a,b,c)}setScreenOffset(a,b,c){return super._updateOneProp(a,"anchors",b,c)}setCoordinate(a,b,c){return super._updateOneProp(a,"coordinate",b,c)}setImagePath(a,b,c){return super._updateOneProp(a,"imagePath",b,c)}setImageSize(a,b,c){return super._updateOneProp(a,"imageSize",b,c)}setHoverImagePath(a,
b,c){return super._updateOneProp(a,"hoverImagePath",b,c)}setURL(a,b,c){return super._updateOneProp(a,"url",b,c)}setText(a,b,c){return super._updateOneProp(a,"text",b,c)}setTextOffset(a,b,c){return super._updateOneProp(a,"textOffset",b,c)}setFontSize(a,b,c){return super._updateOneProp(a,"fontSize",b,c)}setFontOutlineSize(a,b,c){return super._updateOneProp(a,"fontOutlineSize",b,c)}setRange(a,b,c){return super._updateOneProp(a,"range",b,c)}setTextRange(a,b,c){return super._updateOneProp(a,"textRange",
b,c)}setFontColor(a,b,c){return super._updateOneProp(a,"fontColor",b,c)}setFontOutlineColor(a,b,c){return super._updateOneProp(a,"fontOutlineColor",b,c)}setTextBackgroundColor(a,b,c){return super._updateOneProp(a,"textBackgroundColor",b,c)}setAutoHidePopupWindow(a,b,c){return super._updateOneProp(a,"autoHidePopupWindow",b,c)}setPopupURL(a,b,c){return super._updateOneProp(a,"popupURL",b,c)}setPopupSize(a,b,c){return super._updateOneProp(a,"popupSize",b,c)}setPopupOffset(a,b,c){return super._updateOneProp(a,
"popupOffset",b,c)}setLineSize(a,b,c){return super._updateOneProp(a,"lineSize",b,c)}setLineColor(a,b,c){return super._updateOneProp(a,"lineColor",b,c)}setLineOffset(a,b,c){return super._updateOneProp(a,"lineOffset",b,c)}setPriority(a,b,c){return super._updateOneProp(a,"priority",b,c)}setOcclusionCull(a,b,c){return super._updateOneProp(a,"occlusionCull",b,c)}setAttachCustomObject(a,b){return this.int.call({command:k.Marker_SetupPOIAttachment,data:B(a)},b)}setViewportVisible(a,b,c){let e=[];e.push({viewportIndex:1,
viewportVisible:!!(b&Viewport.V1)});e.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});e.push({viewportIndex:3,viewportVisible:!!(b&Viewport.V3)});e.push({viewportIndex:4,viewportVisible:!!(b&Viewport.V4)});return this.int.call({command:k.Marker_SetViewPort,id:a,data:e},c)}showByGroupId(a,b){return this.int.call({command:k.Marker_ShowByGroupId,ids:B(a)},b)}hideByGroupId(a,b){return this.int.call({command:k.Marker_HideByGroupId,ids:B(a)},b)}deleteByGroupId(a,b){return this.int.call({command:k.Marker_DeleteByGroupId,
ids:B(a)},b)}setClusterStyle(a,b){if(a)return this.int.call({command:k.Marker_SetGatherStyle,path:a.imagePath||"",drawSize:a.imageSize||[20,20],textSize:a.fontSize||12,textColor:D(a.fontColor)||[1,1,1,1]},b);console.error("\u6837\u5f0f\u5bf9\u8c61\u683c\u5f0f\u9519\u8bef!")}}class Mb extends M{constructor(a){super(a,"Marker3D","textColor|textOutlineColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,
b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}showByGroupId(a,b){return this.int.call({command:k.Marker3D_ShowByGroupId,groupIds:I(a)},b)}hideByGroupId(a,b){return this.int.call({command:k.Marker3D_HideByGroupId,groupIds:I(a)},b)}deleteByGroupId(a,b){return this.int.call({command:k.Marker3D_DeleteByGroupId,groupIds:I(a)},b)}getBPFunction(a,
b){return this.int.call({command:k.Misc_QueryActorOrMaterial,idOrPaths:B(a)},b)}callBPFunction(a,b){this.callBatchBPFunction(a,b)}callBatchBPFunction(a,b){return this.int.call({command:k.Marker3D_CallFunction,data:B(a)},b)}setViewHeightRange(a,b,c,e){return this.int.call({command:k.Marker3D_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},e)}setAttachCustomObject(a,b){return this.int.call({command:k.Marker3D_Attach,data:B(a)},b)}}class Nb extends M{constructor(a){super(a,"MarkerLayer",
"color|fontColor|fontOutlineColor|textBackgroundColor|lineColor")}add(a,b){this._fixMarkerProperty(a);return super._add(a,b)}_fixMarkerProperty(a){Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){this._fixMarkerProperty(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,
b,c,e,d){return super._focus(a,b,c,e,d)}focusByMarkerId(a,b,c,e,d,g){return this.int.call({command:k.MarkerLayer_FocusMarker,data:[{id:a,ids:[b]}],distance:c,flyTime:e,rotation:d},g)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}setViewHeightRange(a,b,c,e){return this.int.call({command:k.MarkerLayer_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},e)}}class Ob extends M{constructor(a){super(a,
"ODLine","color")}add(a,b){this._fixODlineData(a);return super._add(a,b)}_fixODlineProperty(a){a.intensity&&(a.brightness=a.intensity)}_fixODlineData(a){if(a instanceof Array)for(let b of a)this._fixODlineProperty(b);else this._fixODlineProperty(a)}update(a,b){this._fixODlineData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,
b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setFlowRate(a,b,c){return super._updateOneProp(a,"flowRate",b,c)}setBrightness(a,b,c){return super._updateOneProp(a,"brightness",b,c)}setTiling(a,b,c){return super._updateOneProp(a,"tiling",b,c)}setBendDegree(a,b,c){return super._updateOneProp(a,"bendDegree",b,c)}setLineThickness(a,
b,c){return super._updateOneProp(a,"lineThickness",b,c)}setflowPointSizeScale(a,b,c){return super._updateOneProp(a,"flowPointSizeScale",b,c)}setLabelSizeScale(a,b,c){return super._updateOneProp(a,"labelSizeScale",b,c)}setLineShape(a,b,c){return super._updateOneProp(a,"lineShape",b,c)}setLineStyle(a,b,c){return super._updateOneProp(a,"lineStyle",b,c)}setFlowShape(a,b,c){return super._updateOneProp(a,"flowShape",b,c)}setStartPointShape(a,b,c){return super._updateOneProp(a,"startPointShape",b,c)}setEndPointShape(a,
b,c){return super._updateOneProp(a,"endPointShape",b,c)}setStartLabelShape(a,b,c){return super._updateOneProp(a,"startLabelShape",b,c)}setEndLabelShape(a,b,c){return super._updateOneProp(a,"endLabelShape",b,c)}}class Pb extends M{constructor(a){super(a,"Panorama")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}get(a,b){return super._get(a,b)}enter(a,b){return this.int.call({command:k.Panorama_Enter,
ids:I(a)},b)}exit(a){return this.int.call({command:k.Panorama_Exit},a)}switchMode(a){return this.int.call({command:k.Panorama_Switch},a)}}class Qb extends M{constructor(a){super(a,"Polygon","color|frameColor")}add(a,b){this._fixPolygonData(a);return super._add(a,b)}_fixPolygonProperty(a){a.brightness=0==a.intensity||1==a.intensity?a.intensity:1;11==a.style&&(a.style=0,a.brightness=1,a.intensity=1);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixPolygonData(a){if(a instanceof
Array)for(let b of a)this._fixPolygonProperty(b);else this._fixPolygonProperty(a)}update(a,b){this._fixPolygonData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}glow(a,b,c){return this.int.call({command:k.Polygon_Glow,ids:I(a),duration:b},c)}highlight(a,b){return this.int.call({command:k.Polygon_Highlight,
ids:I(a)},b)}stopHighlight(a,b){return this.int.call({command:k.Polygon_StopHighlight,ids:I(a)},b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",K(b),c)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setDepthTest(a,b,c){return super._updateOneProp(a,"depthTest",b,c)}setViewHeightRange(a,b,c,e){return this.int.call({command:k.Polygon_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},e)}}class Rb extends M{constructor(a){super(a,"Polygon3D","color")}add(a,
b){this._fixPolygonData(a);return super._add(a,b)}_fixPolygonProperty(a){11==a.style&&(a.style=9,a.intensity=1);17==a.style&&(a.style=11);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixPolygonData(a){if(a instanceof Array)for(let b of a)this._fixPolygonProperty(b);else this._fixPolygonProperty(a)}update(a,b){this._fixPolygonData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,
b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}highlight(a,b){return this.int.call({command:k.Polygon3D_Highlight,ids:I(a)},b)}stopHighlight(a,b){return this.int.call({command:k.Polygon3D_StopHighlight,ids:I(a)},b)}glow(a,b){if(a instanceof Array)for(let c of a){let e;e=D(c.color);c.colors=[{color:[e[0]/2,e[1]/2,e[2]/2],value:0},{color:c.color,
value:1}]}else o.colors=[{color:o.color,value:0},{color:o.color,value:1}];return this.int.call({command:k.Polygon3D_Glow,data:B(a)},b)}stopGlow(a,b){return this.int.call({command:k.Polygon3D_StopGlow,ids:I(a)},b)}setStyle(a,b,c){return super._updateOneProp(a,"style",b,c)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setHeight(a,b,c){return super._updateOneProp(a,"height",b,c)}setIntensity(a,b,c){return super._updateOneProp(a,
"intensity",b,c)}setTillingX(a,b,c){return super._updateOneProp(a,"tillingX",b,c)}setTillingY(a,b,c){return super._updateOneProp(a,"tillingY",b,c)}setViewHeightRange(a,b,c,e){return this.int.call({command:k.Polygon3D_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},e)}enableClip(a,b){let c=[];a=B(a);for(let e of a)c.push({id:e,bClip:!0});return this.int.call({command:k.Polygon3D_Update,data:c},b)}disableClip(a,b){let c=[];a=B(a);for(let e of a)c.push({id:e,bClip:!1});return this.int.call({command:k.Polygon3D_Update,
data:c},b)}}class Sb extends M{constructor(a){super(a,"Polyline","color")}add(a,b){this._fixPolylineData(a);return super._add(a,b)}_fixPolylineProperty(a){a.intensity&&(a.brightness=a.intensity);8==a.style&&(a.style=4,a.brightness=1,a.intensity=1);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixPolylineData(a){if(a instanceof Array)for(let b of a)this._fixPolylineProperty(b);else this._fixPolylineProperty(a)}update(a,b){this._fixPolylineData(a);
return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setStyle(a,b,c){return super._updateOneProp(a,"style",b,c)}setThickness(a,b,c){return super._updateOneProp(a,"thickness",
b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setFlowRate(a,b,c){return super._updateOneProp(a,"flowRate",b,c)}setBrightness(a,b,c){return super._updateOneProp(a,"brightness",b,c)}setShape(a,b,c){return super._updateOneProp(a,"shape",b,c)}setDepth(a,b,c){return super._updateOneProp(a,"depthTest",b,c)}setViewHeightRange(a,b,c,e){return this.int.call({command:k.Polyline_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},e)}}class Tb extends M{constructor(a){super(a,"RadiationPoint",
"color")}add(a,b){this._fixRadiationPointData(a);return super._add(a,b)}_fixRadiationPointProperty(a){a.intensity&&(a.brightness=a.intensity)}_fixRadiationPointData(a){if(a instanceof Array)for(let b of a)this._fixRadiationPointProperty(b);else this._fixRadiationPointProperty(a)}update(a,b){this._fixRadiationPointData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}focusAll(a,b,c,e){return super._focusAll(a,
b,c,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinate(a,b,c){return super._updateOneProp(a,"coordinate",b,c)}setRadius(a,b,c){return super._updateOneProp(a,"radius",b,c)}setRippleNumber(a,b,c){return super._updateOneProp(a,"rippleNumber",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",D(b),c)}setBrightness(a,b,c){return super._updateOneProp(a,"brightness",
b,c)}setAutoHeight(a,b,c){return super._updateOneProp(a,"autoHeight",b,c)}}class Ub extends M{constructor(a){super(a,"River","arrowColor|waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class Vb extends M{constructor(a){super(a,"ShapeFileLayer","color")}add(a,
b){return super._add(a,b)}update(a,b){return super._update(a,b)}_fix_property(a){if(a instanceof Array){let b=[];for(let c of a)R(c.cacheAllField)&&(c.cacheAllField=!1,b.push(c));a=b}else R(a.cacheAllField)&&(a.cacheAllField=!1);return a}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}get(a,b){return super._get(a,b)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}open(a,
b){return this.int.call({command:k.ShapeFileLayer_OpenShapeFileLayer,data:B(a)},b)}highlightFeature(a,b,c){return this.int.call({command:k.ShapeFileLayer_HighlightFeature,data:B({shpId:a,featureIds:[b]})},c)}stopHighlightFeature(a,b,c){return this.int.call({command:k.ShapeFileLayer_UnHighlightFeature,data:B({shpId:a,featureIds:[b]})},c)}highlightFeatures(a,b){return this.int.call({command:k.ShapeFileLayer_HighlightFeature,data:B(a)},b)}stopHighlightFeatures(a,b){return this.int.call({command:k.ShapeFileLayer_UnHighlightFeature,
data:B(a)},b)}focusFeature(a,b,c,e,d,g){return this.int.call({command:k.ShapeFileLayer_FocusFeature,data:B({shpId:a,featureIds:[b]}),distance:c,flyTime:e,rotation:d},g)}getFeature(a,b){return this.int.call({command:k.ShapeFileLayer_GetFeatureInfo,data:B(a)},b)}}class Wb extends M{constructor(a){super(a,"SignalWave","olor")}add(a,b){this._fixMarkerData(a,"add");return super._add(a,b)}_fixMarkerProperty(a,b){if(0<=a.alpha&&1>=a.alpha)if(0==a.alpha)a.alpha=.9;else if(1==a.alpha)a.alpha=.8;else{b=a.alpha;
var c=[0,1],e=[.9,.8];b=b<c[0]||b>c[1]?NaN:(b-c[0])*(e[1]-e[0])/(c[1]-c[0])+e[0];a.alpha=b}else a.alpha=1}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){this._fixMarkerData(a,"update");return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,
b)}}class Xb extends M{constructor(a){super(a,"SplineMesh")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}}class Yb extends M{constructor(a){super(a,"Tag","textColor|textBackgroundColor|textBorderColor")}add(a,
b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}focusAll(a,b,c,e){return super._focusAll(a,b,c,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinate(a,b,c){return super._updateOneProp(a,"coordinate",b,c)}setImagePath(a,b,c){return super._updateOneProp(a,
"imagePath",b,c)}setImageSize(a,b,c){return super._updateOneProp(a,"imageSize",b,c)}setText(a,b,c){return super._updateOneProp(a,"text",b,c)}setRange(a,b,c){return super._updateOneProp(a,"range",b,c)}setTextColor(a,b,c){return super._updateOneProp(a,"textColor",b,c)}setTextBackgroundColor(a,b,c){return super._updateOneProp(a,"textBackgroundColor",b,c)}setTextBorderColor(a,b,c){return super._updateOneProp(a,"textBorderColor",b,c)}setShowLine(a,b,c){return super._updateOneProp(a,"showLine",b,c)}setAutoHidePopupWindow(a,
b,c){return super._updateOneProp(a,"autoHidePopupWindow",b,c)}setURL(a,b,c){return super._updateOneProp(a,"url",b,c)}showPopupWindow(a,b){return this.int.call({command:k.Tag_PopupWindow_Show,ids:I(a)},b)}hidePopupWindow(a,b){return this.int.call({command:k.Tag_PopupWindow_Hide,ids:I(a)},b)}showAllPopupWindow(a){return this.int.call0(k.Tag_PopupWindow_ShowAll,a)}hideAllPopupWindow(a){return this.int.call0(k.Tag_PopupWindow_HideAll,a)}}class Zb{constructor(){this._where=this._fields=this._id=null}get id(){return this._id}set id(a){this._id=
a}get fields(){return this._fields}set fields(a){this._fields=a}get where(){return this._where}set where(a){this._where=a}get limit(){return this._limit}set limit(a){this._limit=a}get offset(){return this._offset}set offset(a){this._offset=a}}class $b extends M{constructor(a){super(a,"TileLayer")}add(a,b){this._fixMarkerProperty(a);return super._add(a,b)}_fixMarkerProperty(a){Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixMarkerData(a,
b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){this._fixMarkerProperty(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}clear(a){return super._clear(a)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}getAllFlattenInfo(a){return this.int.call({command:k.TileLayer_GetAllFlattenData},a)}getActorInfoFromDB(a,b){return this.int.call({command:k.TileLayer_GetActorInfoFromDB,
data:B(a)},b)}enableXRay(a,b,c){return this.int.call({command:k.TileLayer_EnableXRay,ids:I(a),color:D(b)},c)}disableXRay(a,b){return this.int.call({command:k.TileLayer_DisableXRay,ids:I(a)},b)}enableClip(a,b){let c=[];a=B(a);for(let e of a)c.push({id:e,allowClip:!0});return this.int.call({command:k.TileLayer_SetAllowClip,data:c},b)}disableClip(a,b){let c=[];a=B(a);for(let e of a)c.push({id:e,allowClip:!1});return this.int.call({command:k.TileLayer_SetAllowClip,data:c},b)}setFileName(a,b,c){return super._updateOneProp(a,
"fileName",b,c)}setLocation(a,b,c){return super._updateOneProp(a,"location",b,c)}setTranslation(a,b,c){return this.setLocation(a,b,c)}setRotation(a,b,c){return super._updateOneProp(a,"rotation",b,c)}setScale(a,b,c){return super._updateOneProp(a,"scale",b,c)}showActor(a,b,c){a=new fa(a,b);return this.int.call({command:k.TileLayer_Actor_Show,data:B(a)},c)}showActors(a,b){let c=[];if(a instanceof Array)for(let d=0;d<a.length;d++){var e=a[d];e={tileLayerId:e.id,tileLayerActorNames:I(e.objectIds)};c.push(e)}else a=
{tileLayerId:a.id,tileLayerActorNames:I(a.objectIds)},c.push(a);return this.int.call({command:k.TileLayer_Actor_Show,data:c},b)}hideActor(a,b,c){a=new fa(a,b);return this.int.call({command:k.TileLayer_Actor_Hide,data:B(a)},c)}hideActors(a,b){let c=[];if(a instanceof Array)for(let d=0;d<a.length;d++){var e=a[d];e={tileLayerId:e.id,tileLayerActorNames:I(e.objectIds)};c.push(e)}else a={tileLayerId:a.id,tileLayerActorNames:I(a.objectIds)},c.push(a);return this.int.call({command:k.TileLayer_Actor_Hide,
data:c},b)}focusActor(a,b,c,e,d,g){a=new fa(a,b);return this.int.call({command:k.TileLayer_Actor_Focus,data:B(a),distance:c,flyTime:e,rotation:d},g)}focusActors(a,b,c,e,d){a=new fa(a.id,a.objectIds);return this.int.call({command:k.TileLayer_Actor_Focus,data:B(a),distance:b,flyTime:c,rotation:e},d)}highlightActor(a,b,c){a=new fa(a,b);return this.int.call({command:k.TileLayer_Actor_Highlight,data:B(a)},c)}stopHighlightActor(a,b,c){let e=void 0==a;"function"==typeof a&&(c=a,e=!0);if(e)return this.stopHighlightActors(c);
if(R(a)||R(b))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");else return this.stopHighlightActors({id:a,objectIds:[b]},c)}highlightActors(a,b){return this.int.call({command:k.TileLayer_Actor_Highlight,data:this._fix_highlight_actors_data(a)},b)}stopHighlightActors(a,b){let c=!a;"function"==typeof a&&(b=a,c=!0);return c?this.int.call0(k.TileLayer_Actor_StopHightlight,b):this.int.call({command:k.TileLayer_Actor_Unhighlight,data:this._fix_highlight_actors_data(a)},b)}_fix_highlight_actors_data(a){if(R(a))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");
else{var b=[];if(a instanceof Array)for(let c=0;c<a.length;c++){let e=new fa(a[c].id,a[c].objectIds);b.push(e)}else a=new fa(a.id,a.objectIds),b.push(a);return b}}stopHighlightAllActors(a){return this.int.call0(k.TileLayer_Actor_StopHightlight,a)}showAllActors(a,b){return this.int.call({command:k.TileLayer_Actor_ShowAll,ids:I(a)},b)}hideAllActors(a,b){return this.int.call({command:k.TileLayer_Actor_HideAll,ids:I(a)},b)}setStyle(a,b,c,e,d,g,h,q){return this.int.call({command:k.TileLayer_SetStyle,ids:I(a),
style:b,color:D(c),saturation:e,brightness:d,contrast:g,contrastbase:h},q)}setHeatMapStyle(a,b,c){return this.int.call({command:k.TileLayer_SetStyle,ids:I(a),style:7,colors:{gradient:!0,invalidColor:[1,1,1,0],colorStops:b}},c)}setCollision(a,b,c,e,d,g){a=I(a);let h=[];for(let q of a)h.push({id:q,enableCollision:b,enableMouseRoma:c,enableFunctionInteract:e,enableCharacterCollision:d});return this.int.call({command:k.TileLayer_SetCollision,data:h},g)}getCollision(a,b){return this.int.call({command:k.TileLayer_GetCollision,
ids:I(a)},b)}addModifiers(a,b){return this.int.call({command:k.TileLayer_Modifier_Add,data:B(a)},b)}addModifierByShapeFile(a,b){return this.int.call({command:k.TileLayer_Modifier_Add,data:B(a)},b)}addModifier(a,b,c,e,d){this.addModifiers([{id:a,tileLayerId:b,coordinates:c,ententBufferSize:e}])}updateModifier(a,b,c,e,d){return this.int.call({command:k.TileLayer_Modifier_Update,data:B([{id:a,tileLayerId:b,coordinates:c,ententBufferSize:e}])},d)}deleteModifier(a,b,c){return this.int.call({command:k.TileLayer_Modifier_Delete,
id:a,tileLayerId:b},c)}clearModifier(a,b){return this.int.call({command:k.TileLayer_Modifier_Clear,ids:I(a)},b)}setViewportVisible(a,b,c){let e=[];e.push({viewportIndex:1,viewportVisible:!!(b&ca.V1)});e.push({viewportIndex:2,viewportVisible:!!(b&ca.V2)});e.push({viewportIndex:3,viewportVisible:!!(b&ca.V3)});e.push({viewportIndex:4,viewportVisible:!!(b&ca.V4)});return this.int.call({command:k.TileLayer_SetViewportVisible,id:a,data:e},c)}getObjectIDs(a,b){return this.int.call({command:k.TileLayer_GetObjectIDs,
ids:I(a)},b)}getActorInfo(a,b){return this.int.call({command:k.TileLayer_Actor_GetInfo,data:B(a)},b)}addHoleByShapeFile(a,b){return this.int.call({command:k.TileLayer_CutPolygon_Add,data:B(a)},b)}addHole(a,b){return this.int.call({command:k.TileLayer_CutPolygon_Add,data:B(a)},b)}updateHole(a,b,c,e,d){return this.int.call({command:k.TileLayer_CutPolygon_Update,data:B({id:a,tileLayerId:b,coordinates:c,isReverseCut:e})},d)}deleteHole(a,b,c){return this.int.call({command:k.TileLayer_CutPolygon_Delete,
id:a,tileLayerId:b},c)}clearHole(a,b){return this.int.call({command:k.TileLayer_CutPolygon_Clear,ids:I(a)},b)}setViewHeightRange(a,b,c,e){return this.int.call({command:k.TileLayer_SetViewHeightRange,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},e)}setPointCloudSize(a,b,c){return this.int.call({command:k.TileLayer_SetPointSize,data:[{tileLayerId:a,size:b}]},c)}enableDecal(a,b){this.setEnableDecal(a,b)}setEnableDecal(a,b){return this.int.call({command:k.TileLayer_SetDecalAttach,data:a},b)}enableImageLayerDecal(a,
b){return this.int.call({command:k.TileLayer_TileLayerReceiveWMTSDecal,data:a},b)}getDBTabID(a,b){return this.int.call({command:k.TileLayer_GetDBTabID,ids:B(a)},b)}createQuery(){return new Zb}query(a,b){const c=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){if(!a||!a.id)return c.int.logWithColor("red","Invalid queryOption!"),Promise.reject();let e=yield c.getDBTabID(a.id);return e.result==va.OK&&e.data&&0!=e.data.length?c.int.sendApiToCS({type:"api",command:"PG_Query",id:e.data[0],
fields:a.fields,where:a.where,limit:a.limit,offset:a.offset},b):(c.int.logWithColor("red","Unable to get dbTabId based on tilelayerId!"),Promise.reject())})}updateRecord(a,b,c,e){const d=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){if(!b||!c)return d.int.logWithColor("red","Invalid parameters!"),Promise.reject();let g=yield d.getDBTabID(a);return g.result==va.OK&&g.data&&0!=g.data.length?d.int.sendApiToCS({type:"api",command:"PG_Update",id:g.data[0],newValMap:b,where:c},e):
(d.int.logWithColor("red","Unable to get dbTabId based on tilelayerId!"),Promise.reject())})}}class ac extends M{constructor(a){super(a,"VectorField")}add(a,b){this._fixMarkerData(a,"add");return super._add(a,b)}_fixMarkerProperty(a,b){"add"==b&&a.binFilePath&&(a.vetorFieldFilePath=a.binFilePath)}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,
b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setViewportVisible(a,b,c){let e=[];e.push({viewportIndex:1,viewportVisible:!!(b&Viewport.V1)});e.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});e.push({viewportIndex:3,viewportVisible:!!(b&Viewport.V3)});e.push({viewportIndex:4,viewportVisible:!!(b&Viewport.V4)});return this.int.call({command:k.VectorField_SetViewPort,id:a,data:e},c)}}class bc extends M{constructor(a){super(a,
"Vehicle","color")}add(a,b){this._fixVehicleData(a);return super._add(a,b)}_fixVehicleData(a){if(a instanceof Array)for(let b of a)this._fixVehicleProperty(b);else this._fixVehicleProperty(a)}_fixVehicleProperty(a){if(void 0===a.delay||null===a.delay||""===a.delay)a.delay=.5;0<a.colorType&&R(a.color)?a.initFunctionInfos=[{functionName:"setVehicleColor",parameters:[{paramType:2,paramValue:a.colorType}]}]:a.colorType=0;a.color&&(a.initFunctionInfos=[{functionName:"setColor",parameters:[{paramType:6,
paramValue:D(a.color)}]}])}update(a,b){this._fixVehicleData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d,g,h,q){return this.int.call({command:k.Vehicle_Focus,ids:B(a),distance:c||0,flyTime:e||2,rotation:d||[0,0,0],distanceRotation:g||[0,0,0],followEnabled:b||!1,offset:h||[0,0,0],sensitivity:.02},q)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setWayPoint(a,b){return this.int.call({command:k.Vehicle_AddWayPoints,
data:B(a)},b)}moveTo(a,b){return this.int.call({command:k.Vehicle_MoveTo,data:B(a)},b)}start(a,b){if(R(a))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");else{if(0<=a.timeStamp)a.active=!0;else if(a instanceof Array)for(let c of a)c.active=!0;else{console.error("\u53c2\u6570\u975e\u6cd5!");return}return this.int.call({command:k.Vehicle_Update,data:B(a)},b)}}pause(a,b){a=this._fixIds(a,!1);if(!R(a))return this.int.call({command:k.Vehicle_Update,data:a},b)}resume(a,b){a=this._fixIds(a,!0);if(!R(a))return this.int.call({command:k.Vehicle_Update,
data:a},b)}stop(a,b){a=this._fixIds(a,!1);if(!R(a))return this.int.call({command:k.Vehicle_Update,data:a},b)}_fixIds(a,b){let c=[];if(R(a))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");else{if(a instanceof Array){let e=!1;for(let d of a){if("string"!=typeof d){e=!0;break}c.push({id:d,active:b})}if(e){console.error("\u53c2\u6570\u975e\u6cd5!");return}return c}if("string"!==typeof a||R(a))console.error("\u53c2\u6570\u975e\u6cd5!");else return c.push({id:a,active:b}),c}}callBatchFunction(a,
b){return this.int.call({command:k.Vehicle_CallBatchFunction,data:B(a)},b)}}class cc extends M{constructor(a){super(a,"Vehicle2","color")}add(a,b){this._fixVehicleData(a);return super._add(a,b)}_fixVehicleData(a){if(a instanceof Array)for(let b of a)this._fixVehicleProperty(b);else this._fixVehicleProperty(a)}_fixVehicleProperty(a){a.timeMode=1;if(void 0===a.delay||null===a.delay||""===a.delay)a.delay=.5;0<a.colorType&&R(a.color)?a.initFunctionInfos=[{functionName:"setVehicleColor",parameters:[{paramType:2,
paramValue:a.colorType}]}]:a.colorType=0;a.color&&(a.initFunctionInfos=[{functionName:"setColor",parameters:[{paramType:6,paramValue:D(a.color)}]}])}update(a,b){this._fixVehicleData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}focus(a,b,c,e,d,g){R(e)&&(e=[0,0,0]);return this.int.call({command:k.Vehicle2_Focus,ids:B(a),followEnabled:!1,distance:b||
5,flyTime:c||2,viewPitch:e[0]||0,viewYaw:e[1]||0,offset:d||[0,0,0],sensitivity:.02},g)}setFollow(a,b,c,e,d,g,h,q){return this.int.call({command:k.Vehicle2_Focus,ids:B(a),distance:b||0,flyTime:c||2,viewPitch:e||0,viewYaw:d||0,sensitivity:g||.02,viewControl:h||!1,followEnabled:!0},q)}moveTo(a,b){return this.int.call({command:k.Vehicle2_MoveTo,data:B(a)},b)}showTextLabel(a,b,c){return R(b)?this.int.call({command:k.Vehicle2_AddMarker,ids:B(a),fontSize:12,fontOutlineSize:1,textBackgroundColor:[1,1,1,1],
fontColor:[1,1,1,1],fontOutlineColor:[0,0,0,1],lineSize:1,lineColor:[0,0,0,1],lineOffset:[0,0]},c):this.int.call({command:k.Vehicle2_AddMarker,ids:B(a),text:b.text||B(a),fontSize:b.fontSize||12,fontOutlineSize:b.fontOutlineSize||1,textBackgroundColor:b.textBackgroundColor||[1,1,1,1],fontColor:b.fontColor||[1,1,1,1],fontOutlineColor:b.fontOutlineColor||[0,0,0,1],lineSize:b.lineSize||1,lineColor:b.lineColor||[0,0,0,1],lineOffset:b.lineOffset||[0,0]},c)}hideTextLabel(a,b){return this.int.call({command:k.Vehicle2_ClearMarker,
ids:B(a)},b)}callBatchFunction(a,b){return this.int.call({command:k.Vehicle_CallBatchFunction,data:B(a)},b)}}class dc extends M{constructor(a){super(a,"VideoProjection","frustumColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setVideoURL(a,b,c){return super._updateOneProp(a,
"videoURL",b,c)}setLocation(a,b,c){return super._updateOneProp(a,"location",b,c)}setRotation(a,b,c){return super._updateOneProp(a,"rotation",b,c)}setFovy(a,b,c){return super._updateOneProp(a,"fov",b,c)}setAspectRatio(a,b,c){return super._updateOneProp(a,"aspectRatio",b,c)}setDistance(a,b,c){return super._updateOneProp(a,"distance",b,c)}setDepthCulling(a,b,c){return super._updateOneProp(a,"depthCulling",b,c)}setFrustumColor(a,b,c){return super._updateOneProp(a,"frustumColor",b,c)}}class ec extends M{constructor(a){super(a,
"WaterFlowField")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setViewportVisible(a,b,c){let e=[];e.push({viewportIndex:1,viewportVisible:!!(b&Viewport.V1)});e.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});e.push({viewportIndex:3,viewportVisible:!!(b&
Viewport.V3)});e.push({viewportIndex:4,viewportVisible:!!(b&Viewport.V4)});return this.int.call({command:k.WaterFlowField_SetViewPort,id:a,data:e},c)}}class fc extends M{constructor(a){super(a,"WaterMesh","waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,e,d){return super._focus(a,b,c,e,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}get(a,
b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setIndices(a,b,c){return super._updateOneProp(a,"indices",b,c)}setNormals(a,b,c){return super._updateOneProp(a,"normals",b,c)}setWaterColor(a,b,c){return super._updateOneProp(a,"waterColor",b,c)}setWaterSpeed(a,b,c){return super._updateOneProp(a,"waterSpeed",b,c)}setWaterUVRepeat(a,b,c){return super._updateOneProp(a,"waterUVRepeat",b,c)}setWaterDirection(a,b,c){return super._updateOneProp(a,"waterDirection",
b,c)}setWaterWaveScale(a,b,c){return super._updateOneProp(a,"waveScale",b,c)}}var Ea="en",wa={},Fa={VersionMismatch:["\u8b66\u544a\uff1a\n--------------------------------------------------\n    ac.min.js\u6587\u4ef6\u7248\u672c\uff08{0}\uff09\u548c\u4e91\u6e32\u67d3\u670d\u52a1\u5668\u7248\u672c\uff08{1}\uff09\u4e0d\u4e00\u81f4\uff0c\n    \u53ef\u80fd\u9020\u6210\u63a5\u53e3\u8c03\u7528\u9519\u8bef\u6216\u5d29\u6e83\uff0c\u8bf7\u786e\u8ba4!","Warning:\n--------------------------------------------- -----\n The version of the ac.min.js file ({0}) and the version of the cloud rendering server ({1}) are inconsistent,\n may cause interface call errors or crashes, please confirm!"],
Disconnect:["\u8fde\u63a5\u65ad\u5f00","Disconnect"],TimeConsuming:["\u8017\u65f6\uff1a","TimeConsuming: "],RequestTime:["\u8bf7\u6c42\u65f6\u95f4\uff1a","RequestTime: "],ResponseTime:["\u54cd\u5e94\u65f6\u95f4\uff1a","ResponseTime: "],MessageLength:["\u6d88\u606f\u957f\u5ea6\uff1a","MessageLength: "],DomLoading:["DOM\u5c1a\u5728\u52a0\u8f7d\u4e2d\uff0c\u6b64\u65f6\u4e0d\u80fd\u521d\u59cb\u5316DigitalTwinPlayer!","DOM is still loading, cannot initialize DigitalTwinPlayer at this time!"],CannotChangeProject:["\u5f53\u524d\u5b9e\u4f8b\u5df2\u8bbe\u7f6e\u9501\u5b9a\u5de5\u7a0b\uff0c\u65e0\u6cd5\u901a\u8fc7API\u63a5\u53e3\u5207\u6362\u5de5\u7a0b",
"The current instance has been set to lock the project, and the project cannot be switched through the API interface"],InstanceNotExist:["\u5bf9\u5e94\u7684\u5b9e\u4f8b\u4e0d\u5b58\u5728\uff0c\u8bf7\u786e\u8ba4","The corresponding instance does not exist, please confirm"],ProjectNotExist:["\u5bf9\u5e94\u7684\u5de5\u7a0b\u4e0d\u5b58\u5728\uff0c\u8bf7\u786e\u8ba4","The corresponding project does not exist, please confirm"],JSQueue:["JS\u961f\u5217\uff1a","JS queue:"],BackQueue:["\u540e\u53f0\u961f\u5217\uff1a",
"Background queue:"],Execting:["\u6b63\u5728\u6267\u884c\uff1a","Executing: "],PleaseWait:["\uff0c\u8bf7\u7a0d\u5019...",", Please wait..."],DisconnectForIdle:["\u957f\u65f6\u95f4\u672a\u4f7f\u7528\u8fde\u63a5\u5df2\u65ad\u5f00","Disconnected after a long time of inactivity"],NodeInfo:["\u6e32\u67d3\u8282\u70b9\u4fe1\u606f\uff1a","Render node information:"],Host:["\u4e3b\u673a\u540d\u79f0\uff1a","Host: "],HostAddress:["\u4e3b\u673a\u5730\u5740\uff1a","Host address:"],Project:["\u5de5\u7a0b\uff1a",
"Project: "],ProjectId:["\u5de5\u7a0bID\uff1a","ProjectId: "],IID:["\u5b9e\u4f8bID\uff1a","IID: "],Adaptive:["\u81ea\u9002\u5e94\uff1a","Adaptive: "],LimitMaxRes:["\u9650\u5236\u6700\u5927\u5206\u8fa8\u7387\uff1a","Limit max resolution: "],Resolution:["\u5206\u8fa8\u7387\uff1a","Resolution: "],Destroyed:["destroyed - \u7528\u6237\u9500\u6bc1","destroyed - user destroyed"],Reconnect5s:["\u5c06\u57285\u79d2\u540e\u91cd\u65b0\u8fde\u63a5...","Will reconnect in 5 seconds..."],RestartAndRetry:["\u5f53\u524d\u5b9e\u4f8b\u6b63\u5fd9\u65e0\u6cd5\u8fde\u63a5\uff01\n\u662f\u5426\u7acb\u5373\u91cd\u542f\u8be5\u5b9e\u4f8b\u4ee5\u8fdb\u884c\u8fde\u63a5\uff1f",
"The current instance is busy and cannot be connected! \nDo you want to restart this instance now to connect?"],EnterFullscreen:["\u8fdb\u5165\u5168\u5c4f","enter fullscreen"],ExitFullscreen:["\u9000\u51fa\u5168\u5c4f","exit fullscreen"],TriggerSysTouch:["\u89e6\u53d1\u4e86\u7cfb\u7edf\u89e6\u6478\u64cd\u4f5c\uff0c\u5c06\u624b\u52a8\u53d1\u9001TouchEnd\u4e8b\u4ef6","The system touch operation is triggered, and the TouchEnd event will be sent manually"],RestartInstNow:["\u7acb\u5373\u91cd\u542f\u5b9e\u4f8b",
"Reboot instance"],LeftClickTip:["\u9f20\u6807\u5de6\u952e\u70b9\u51fb\uff1a\u56de\u5230\u4fdd\u5b58\u7684\u4f4d\u7f6e\u6216\u8005\u5de5\u7a0b\u521d\u59cb\u4f4d\u7f6e","Click with the left mouse button: return to the saved position or the initial position of the project"],RightClickTip:["\u9f20\u6807\u53f3\u952e\u70b9\u51fb\uff1a\u4fdd\u5b58\u5f53\u524d\u7684\u76f8\u673a\u4f4d\u7f6e","Right mouse click: save the current camera position"],MiddleClickTip:["\u9f20\u6807\u4e2d\u952e\u70b9\u51fb\uff1a\u5c06\u5f53\u524d\u4f4d\u7f6e\u8bbe\u7f6e\u4e3a\u5de5\u7a0b\u521d\u59cb\u4f4d\u7f6e\u5e76\u8df3\u8f6c",
"Middle mouse click: set the current position as the initial position of the project and jump"],Close:["\u5173\u95ed","Close"],Connections:["\u8fde\u63a5\u4e2a\u6570","Connections"],ConnInfo:["\u8fde\u63a5\u4fe1\u606f\uff1a","ConnInfo:"],Duration:["\u8fd0\u884c\u65f6\u957f\uff1a","Duration:"],Received:["\u63a5\u6536\u6570\u636e\uff1a","Received:"],ReceivedFrames:["\u63a5\u6536\u5e27\u6570\uff1a","Frames:"],Dropped:["\u4e22\u5305\u4e22\u5e27\uff1a","Dropped:"],DroppedTip:["\u4e22\u5305\u6570\u91cf / \u4e22\u5e27\u6570\u91cf",
"Number of dropped packets / Number of dropped frames"],DecodeTimeTip:["\u6d4f\u89c8\u5668\u89e3\u7801\u65f6\u95f4","browser decoding time"],DecodingTime:["\u89e3\u7801\u65f6\u95f4\uff1a","DecTime:"],DecodeFrames:["\u89e3\u7801\u5e27\u6570\uff1a","DecFrame:"],DecodeFramesTip:["\u5df2\u89e3\u7801\u7684\u5e27\u6570 / \u5df2\u89e3\u7801\u7684\u5173\u952e\u5e27\u6570\u91cf","Decoded Frames / Decoded Keyframes"],Bitrate:["\u7801\u7387\uff1a","Bitrate:"],FPS:["\u5e27\u7387\uff1a","FPS:"],QP:["QP\uff1a",
"QP:"],FPSTip:["\u6e32\u67d3\u5e27\u7387/\u89c6\u9891\u5e27\u7387","Rendering frame rate/video frame rate"],MaxQP:["QP\u4e0a\u9650\uff1a","MaxQP:"],MaxQPTip:["\u503c\u8d8a\u5927\uff0c\u753b\u8d28\u8d8a\u7c97\u7cd9\uff0c\u9700\u8981\u7684\u5e26\u5bbd\u8d8a\u5c0f","The larger the value, the rougher the picture quality and the smaller the required bandwidth"],MaxBitrate:["\u7801\u7387\u4e0a\u9650\uff1a","MaxBitrate:"]};String.prototype.format=function(){if(0==arguments.length)return this;for(var a=this,
b=0;b<arguments.length;b++)a=a.replace(new RegExp("\\{"+b+"\\}","g"),arguments[b]);return a};class H{}H.onLanguageChangedCallbacks=[];H.setLanguage=a=>{Ea="zh"===a?"zh":"en";wa={};a="zh"==Ea;for(let b in Fa){let c=Fa[b];wa[b]=a?c[0]:c[1]}for(let b of H.onLanguageChangedCallbacks)"function"==typeof b&&b()};H.getString=a=>wa[a];class sa{static valid(){return"object"==typeof ue&&"object"==typeof ue.internal}static _check(){if(!this.valid())throw"This method must be called on the WebUI page or Marker pop-up page!";
}static execute(a){this._check();ue.internal.execute(a)}static getViewportSize(){this._check();return ue.internal.execute("viewportSize")}static setData(a,b){this._check();ue.internal.execute("setItem",a,b)}static getData(a){this._check();return ue.internal.execute("getItem",a)}static showTickWindow(a){this._check();return ue.internal.showtickwindow(a)}static close(){this._check();return ue.internal.closewindow()}static postEvent(a){this._check();ue.internal.postevent(a)}}"object"==typeof window&&
(window.FdExternal=sa);class gc{constructor(a){this.int=a;this._useOldDataFormat=!1}useOldDataFormat(a){this._useOldDataFormat=a||!0}get(a){return this.int.call0(k.Camera_GetCamera,a)}_setByArray(a,b,c){return this.set(a[0],a[1],a[2],a[3],a[4],b,c)}_setByObject(a,b,c){return this.set(a.x,a.y,a.z,a.pitch,a.yaw,b,c)}set(a,b,c,e,d,g,h){if(a instanceof Array&&5<=a.length)return this._setByArray(a,b,c);if("object"==typeof a&&a.hasOwnProperty("x"))return this._setByObject(a,b,c);"function"==typeof g&&(h=
g,g=void 0);if(this._useOldDataFormat){let q=e;e=d;d=q}return this.int.call({command:k.Camera_Set,coordinate:[a,b,c],pitch:e||0,yaw:d||0,flyTime:g},h)}lookAt(a,b,c,e,d,g,h,q){"function"==typeof h&&(q=h,h=void 0);if(this._useOldDataFormat){let l=d;d=g;g=l}return this.int.call({command:k.Camera_Set,coordinate:[a,b,c],distance:e,pitch:d||0,yaw:g||0,flyTime:h},q)}lookAtBBox(a,b,c,e,d){"function"==typeof e&&(d=e,e=void 0);if(this._useOldDataFormat){let g=b;b=c;c=g}return this.int.call({command:k.Camera_LookAtBBox,
bbox:a,pitch:b||0,yaw:c||0,flyTime:e},d)}playAnimation(a,b){return this.int.call({command:k.Camera_PlayAnimation,id:a},b)}stopAnimation(a){return this.int.call0(k.Camera_StopAnimation,a)}pauseAnimation(a){return this.int.call0(k.Camera_PauseAnimation,a)}resumeAnimation(a){return this.int.call0(k.Camera_ResumeAnimation,a)}getAnimationList(a){return this.int.call0(k.Camera_GetAnimationList,a)}getAnimationImage(a,b){return this.int.call({command:k.Camera_GetAnimationImage,name:a},b)}moveForward(a){return this.int.call({command:k.Camera_Move,
moveForward:!0},a)}moveBackward(a){return this.int.call({command:k.Camera_Move,moveBackward:!0},a)}moveLeft(a){return this.int.call({command:k.Camera_Move,moveLeft:!0},a)}moveRight(a){return this.int.call({command:k.Camera_Move,moveRight:!0},a)}moveUp(a){return this.int.call({command:k.Camera_Move,moveUp:!0},a)}moveDown(a){return this.int.call({command:k.Camera_Move,moveDown:!0},a)}turnLeft(a){return this.int.call({command:k.Camera_Move,turnLeft:!0},a)}turnRight(a){return this.int.call({command:k.Camera_Move,
turnRight:!0},a)}turnUp(a){return this.int.call({command:k.Camera_Move,turnUp:!0},a)}turnDown(a){return this.int.call({command:k.Camera_Move,turnDown:!0},a)}stop(a){return this.int.call({command:k.Camera_Move,stop:!0},a)}getEulerAngle(a,b){const c=360/(2*Math.PI);let e=b[0]-a[0],d=b[1]-a[1];return[Math.atan2(b[2]-a[2],Math.sqrt(e*e+d*d))*c,Math.atan2(e,d)*c-90,0]}lockByBBox(a,b){return this.int.call({command:k.Camera_LockBBox,bbox:a},b)}unlock(a){return this.int.call({command:k.Camera_UnLockBBox},
a)}flyAround(a,b,c,e,d){return this.int.call({command:k.Camera_FlyAround,coordinate:a,rotation:b||[0,90,0],distance:c||1E3,time:e||10},d)}enterWorld(a){return this.int.call({command:k.Camera_ExitWorldAnimation},a)}exitWorld(a){return this.int.call({command:k.Camera_EnterWorldAnimation},a)}cancelFollow(a){return this.int.call({command:k.Camera_CancelFollow},a)}}class hc{constructor(a){this.int=a}screen2World(a,b,c){return this.int.call({command:k.Coord_Screen2World,screenPosition:[a,b]},c)}world2Screen(a,
b,c,e){return this.int.call({command:k.Coord_World2Screen,worldlocation:[a,b,c]},e)}gcs2pcs(a,b,c){"function"==typeof b&&(c=b);R(b)&&(b=1);if(1==b)return this.int.call({command:k.Coord_GCS2PCS,coordinates:P(a)},c);2==b?this.transform(P(a),2,0,c):3==b?this.transform(P(a),3,0,c):console.error("\u672a\u77e5\u5750\u6807\u7cfb\u7c7b\u578b")}pcs2gcs(a,b,c){"function"==typeof b&&(c=b);R(b)&&(b=1);if(1==b)return this.int.call({command:k.Coord_PCS2GCS,coordinates:P(a)},c);2==b?this.transform(P(a),0,2,c):3==
b?this.transform(P(a),0,3,c):console.error("\u672a\u77e5\u5750\u6807\u7cfb\u7c7b\u578b")}transform(a,b,c,e){return this.int.call({command:k.Coord_Transform,coordinates:P(a),src:b,dest:c},e)}}class ic{constructor(a){this.int=a}start(a){return this.int.call0(k.EditHelper_Start,a)}cancel(a){return this.int.call0(k.EditHelper_Quit,a)}finish(a,b){return this.int.call({command:k.EditHelper_Finish,withOffset:a||!0},b)}setParam(a,b,c,e){return this.int.call({command:k.EditHelper_SetParam,lineType:a,buildType:b,
color:D(c)},e)}}class jc{constructor(a){this.int=a}setVisibility(a,b){return this.int.call({command:k.InfoTree_SetVisibility,layers:B(a)},b)}show(a,b){return this.int.call({command:k.InfoTree_Show,ids:I(a)},b)}hide(a,b){return this.int.call({command:k.InfoTree_Hide,ids:I(a)},b)}enableXRay(a,b,c){return this.int.call({command:k.InfoTree_EnableXRay,ids:B(a),color:D(b)},c)}disableXRay(a,b){return this.int.call({command:k.InfoTree_DisableXRay,ids:B(a)},b)}showByGroupId(a,b){return this.int.call({command:k.InfoTree_ShowByGroupId,
ids:I(a)},b)}hideByGroupId(a,b){return this.int.call({command:k.InfoTree_HideByGroupId,ids:I(a)},b)}highlightByGroupId(a,b){return this.int.call({command:k.InfoTree_HighlightByGroupId,ids:I(a)},b)}deleteByGroupId(a,b){return this.int.call({command:k.InfoTree_DeleteByGroupId,ids:I(a)},b)}get(a){return this.int.call0(k.InfoTree_Get,a)}focus(a,b){return this.int.call({command:k.InfoTree_Focus,ids:I(a)},b)}getBPFunction(a,b){return this.int.call({command:k.InfoTree_GetProjectTreeBPFunction,ids:B(a)},
b)}callBPFunction(a,b){return this.int.call({command:k.InfoTree_CallProjectTreeBPFunction,data:B(a)},b)}}class kc{constructor(a){this.int=a;this.apiVersion="6.1";this.apiVersionServer=""}isApiVersionMatched(){let a=this.apiVersionServer,b=this.int.getVersion();return"1.0.0"==this.apiVersionServer||a==b||a&&b&&a.substring(0,a.lastIndexOf("."))==b.substring(0,b.lastIndexOf("."))?!0:!1}addImageButtons(a,b){return this.int.call({command:k.Misc_AddImageButton,data:B(a)},b)}deleteImageButtons(a,b){return this.int.call({command:k.Misc_DeleteImageButton,
ids:I(a)},b)}addAnimatedImageButtons(a,b){return this.int.call({command:k.Misc_AddAnimatedImageButton,data:B(a)},b)}setApiVersionReceived(a){this.int.onApiVersionReceived=a}playVideo(a,b,c,e,d,g,h){return this.int.call({command:k.Misc_PlayVideo,data:[{id:a.toString(),position:[b,c],size:[e,d],url:g}]},h)}stopPlayVideo(a,b){return this.int.call({command:k.Misc_StopPlayVideo,ids:I(a)},b)}playMovie(a,b,c){"function"==typeof b&&(c=b,b=!1);return this.int.call({command:k.Misc_PlayMovie,loop:b,url:a},c)}stopMovie(a){return this.int.call({command:k.Misc_StopMovie},
a)}playVideoAlone(a,b,c){let e=[];e.push(`--URL=${a}`);"object"==typeof b&&(b.hasOwnProperty("mute")&&b.mute&&e.push("--Mute"),b.hasOwnProperty("x")&&-1!=b.x&&e.push(`--Left=${b.x}`),b.hasOwnProperty("y")&&-1!=b.y&&e.push(`--Top=${b.y}`),b.hasOwnProperty("cx")&&0!=b.cx&&e.push(`--Width=${b.cx}`),b.hasOwnProperty("cy")&&0!=b.cy&&e.push(`--Height=${b.cy}`),b.hasOwnProperty("title")&&b.title&&e.push(`--Title="${b.title}"`),b.hasOwnProperty("opacity")&&0!=b.opacity&&e.push(`--Opacity=${b.opacity}`),b.hasOwnProperty("style")&&
e.push(`--FormBorderStyle=${b.style}`),b.hasOwnProperty("hideBuffering")&&b.hideBuffering&&e.push("--HideBuffering"),b.hasOwnProperty("maximizeBox")&&b.maximizeBox&&e.push("--MaximizeBox"),b.hasOwnProperty("notTopmost")&&b.notTopmost&&e.push("--NotTopmost"));a=e.join(" ");return this.startProcess("$VlcPlayer",a,!0,c)}stopPlayVideoAlone(a,b){return this.int.test(2,a,b)}setWindowResolution(a,b,c){return this.int.call({command:k.Misc_SetWindowResolution,cx:a,cy:b,mode:0},c)}callBPFunction(a,b){return this.int.call({command:k.Misc_CallBPFunction,
data:B(a)},b)}enterReportMode(a){return this.int.call0(k.Misc_EnterReportMode)}exitReportMode(a){return this.int.call0(k.Misc_ExitReportMode,a)}showAllFoliages(a){return this.int.call0(k.Misc_ShowAllFoliages,a)}hideAllFoliages(a){return this.int.call0(k.Misc_HideAllFoliages,a)}startProcess(a,b,c,e){"function"==typeof c&&(e=c,c=!0);return this.int.call({command:k.Misc_StartProcess,appName:a,commandLine:b,visible:c},e)}enterMultiViewportMode(a,b,c,e){if(null==b||void 0==b)b="#DEA309";if(null==c||void 0==
c)c=2;return this.int.call({command:k.Misc_EnterMultiViewport,type:a,lineColor:D(b),lineThickness:c},e)}exitMultiViewportMode(a){return this.int.call({command:k.Misc_ExitMultiViewport},a)}setActiveViewport(a,b){return this.int.call({command:k.Misc_SetActivateMultiViewport,viewIndex:B(a)},b)}getActiveViewport(a){return this.int.call({command:k.Misc_GetActivateMultiViewport},a)}setMultiviewportInteractSync(a,b){this.setCameraFollow4Viewport(a,b)}setCameraFollow4Viewport(a,b){return this.int.call({command:k.Misc_UpdateMultiViewport,
moverTogether:a},b)}downloadPakFiles(a,b){return this.int.call({command:k.Misc_downloadPakFiles,ids:B(a)},b)}getConvexPolygon(a,b){return this.int.call({command:k.Misc_ConvexHull2D,data:B(a)},b)}getMaterial(a,b){return this.int.call({command:k.Misc_QueryActorOrMaterial,idOrPaths:B(a)},b)}getBPFunction(a,b){return this.int.call({command:k.Misc_QueryActorOrMaterial,idOrPaths:B(a)},b)}reloadPak(a){return this.int.call({command:k.Misc_ReloadPak},a)}projectAssetCount(a,b){return this.int.call({command:k.Misc_ProjectAssetCount,
type:a},b)}projectAssetCountAll(a){return this.int.call({command:k.Misc_ProjectAssetCount,type:0},a)}switchShortcutKey(a,b){return this.int.call({command:k.Misc_SwitchShortcutKey,"switch":a},b)}startPolygonClip(a,b,c){return this.int.tools.startPolygonClip(a,b,c)}stopClip(a){return this.int.tools.stopClip(a)}playAnimation(a,b){return this.int.camera.playAnimation(a,b)}stopAnimation(a){return this.int.camera.stopAnimation(a)}setDateTime(a,b,c,e,d,g,h){return this.int.weather.setDateTime(a,b,c,e,d,
g,h)}setQueryToolState(a,b){return this.int.call({"c.ommand":k.Settings_SetMousePickMask,mouseClick:a},b)}setCampassVisible(a,b){return this.int.settings.setCampassVisible(a,b)}setMainUIVisibility(a,b){return this.int.settings.setMainUIVisibility(a,b)}setMousePickMask(a,b){return this.int.settings.setMousePickMask(a,b)}}class lc{constructor(a){this.int=a}setMapMode(a,b,c){b=b||{};return this.int.call({command:k.Settings_SetMapMode,mode:a,serviceType:b.serviceType||0,serviceProvider:b.serviceProvider||
0,coordType:b.coordType||0,mapPoint:b.mapPoint||[0,0],longitude:b.longitude||0,latitude:b.latitude||0,cache:b.cache||":memory:",style:b.style||"mapbox://styles/mapbox/streets-v10",groundHeight:b.groundHeight||0,renderMode:b.renderMode||0,decalMode:b.decalMode||1,serverURL:b.serverURL,coordOrder:b.coordOrder,maxLevel:b.maxLevel},c)}getMapMode(a){return this.int.call0(k.Settings_GetMapMode,a)}setWMTSLayerVisible(a,b){this.setwmtsLayerVisible(a,b)}setwmtsLayerVisible(a,b){return this.int.call({command:k.Settings_SetWMTSLayerVisible,
data:B(a)},b)}setWMTSLayerOpacity(a,b){return this.int.call({command:k.Settings_SetWMTSLayerOpacity,data:B(a)},b)}setMapURL(a,b){return this.int.call({command:k.Settings_SetMapURL,url:a},b)}highlightColor(a,b){return this.setHighlightColor(a,b)}setHighlightColor(a,b){return this.int.call({command:k.Settings_SetHighlightColor,color:D(a)},b)}setFovX(a,b){return this.int.call({command:k.Settings_SetFovX,value:a},b)}setOceanColor(a,b){return this.int.call({command:k.Settings_SetOceanColor,color:D(a)},
b)}setEnableInteract(a,b){if(this.int.player)this.int.player.setEnableInteract(a);else return this.int.call({command:k.Settings_SetEnableInteract,enableInteract:a},b)}setInteractiveMode(a,b){return this.int.call({command:k.Settings_SetInteractiveMode,mode:a},b)}setCharacterRoaming(a,b,c,e){return this.int.call({command:k.Settings_SetInteractiveMode,mode:1,location:a,rotation:b,targetArmLength:c},e)}getInteractiveMode(a){return this.int.call({command:k.Settings_GetInteractiveMode},a)}setCampassVisible(a,
b){return this.int.call({command:k.Settings_SetCampassVisible,visible:a},b)}setCampassPosition(a,b,c){return this.int.call({command:k.Settings_SetCampassPosition,position:[a,b]},c)}setMainUIVisibility(a,b){return this.int.call({command:k.Settings_SetMainUIVisibility,visible:a},b)}setMousePickMask(a,b){let c=!1,e=!1,d=!1;a&ra.MouseClick&&(c=!0);a&ra.MouseMove&&(e=!0);a&ra.MouseHover&&(d=!0);return this.int.call({command:k.Settings_SetMousePickMask,mouseClick:c,mouseMove:e,mouseHover:d},b)}setTerrainAlpha(a,
b){return this.int.call({command:k.Settings_SetTerrainAlpha,alpha:a},b)}setEnableCameraMovingEvent(a,b,c){R(b)&&(b=20);return this.int.call({command:k.Settings_EnableCameraMovingEvent,bEnable:a,monitorThreshold:b},c)}setLabelLayer(a,b){return this.int.call({command:k.VTPKService_Set,vtpk:a},b)}getLabelLayer(a){return this.int.call({command:k.VTPKService_Get},a)}removeLabelLayer(a){return this.int.call({command:k.VTPKService_Set,vtpk:""},a)}setRenderedCursorVisible(a,b){this.setCursorAutoSync(a,b)}setCursorAutoSync(a,
b){return this.int.call({command:k.Settings_CursorAutoSync,useSoftwareCursor:a},b)}setScreenControlsVisible(a,b){return this.int.call({command:k.Settings_SetGameBoardVisible,visible:a},b)}getProjectWKT(a){return this.int.call({command:k.Settings_GetWKT},a)}setCharacterRotation(a,b){return this.int.call({command:k.Settings_SetCharacterRotation,rotation:a},b)}setGroundHeight(a,b){this.setGlobalHeight(a,b)}setGlobalHeight(a,b){return this.int.call({command:k.Settings_Update,height:a},b)}setLabelLayerScale(a,
b){return this.int.call({command:k.Settings_Update,symbolSize:a},b)}setImageLayerEnableDecal(a,b){return this.int.call({command:k.Settings_Update,receiveDecalMode:a},b)}}class mc{constructor(a){this.int=a}setReportMode(a,b,c,e){return this.int.call({command:k.Settings_SetReport,alignment:a,playMode:b,moveInOtherView:c},e)}getReportMode(a){return this.int.call({command:k.Settings_GetReport},a)}setControlMode(a,b,c,e,d){return this.int.call({command:k.Settings_SetControl,speed:a,yawSpeed:b,rotateSelf:c,
useFemale:e},d)}getControlMode(a){return this.int.call({command:k.Settings_GetControl},a)}setPostProcessMode(a,b){let c=1E3,e=1E3;0<=a.terrainGlobalAlpha&&1>=a.terrainGlobalAlpha&&(c=1E3*a.terrainGlobalAlpha);0<=a.osgbGlobalAlpha&&1>=a.osgbGlobalAlpha&&(e=1E3*a.osgbGlobalAlpha);return this.int.call({command:k.Settings_SetPostProcess,globalIllumination:a.globalIllumination,chromaticAberration:a.chromaticAberration,ambientRadius:a.ambientRadius,ambientFadeDistance:a.ambientFadeDistance,exposureEnabled:a.exposureEnabled,
exposureCompensation:a.exposureCompensation,depthFiethSwitch:a.depthFiethSwitch,focalLength:a.focalLength,aperture:a.aperture,deepBlur:a.deepBlur,contrast:a.contrast,saturation:a.saturation,lensFlareIntensity:a.lensFlareIntensity,ambientIntensity:a.ambientIntensity,bloomIntensity:a.bloomIntensity,lutMode:a.lutMode,lutIntensity:a.lutIntensity,darkCorner:a.darkCorner,screenPercentage:a.screenPercentage,terrainGlobalAlpha:c,terrainGlobalLitStatus:a.terrainGlobalLitStatus,osgbGlobalLitStatus:a.osgbGlobalLitStatus,
osgbGlobalAlpha:e,antiAliasing:a.antiAliasing,tonemapper:a.tonemapper,postProcessEffects:a.postProcessEffects,dofMode:a.dofMode,wireThickness:a.wireThickness,receiveDecalMode:a.receiveDecalMode},b)}getPostProcessMode(a){return this.int.call({command:k.Settings_GetPostProcess},a)}setCameraMode(a,b,c,e,d){e&&"function"==typeof e&&(d=e);return this.int.call({command:k.Settings_SetCamera,nearClipPlane:a,fovH:b,minCamHeight:c,maxCamHeight:e},d)}getCameraMode(a){return this.int.call({command:k.Settings_GetCamera},
a)}setMapMode(a,b,c){b=b||{};return this.int.call({command:k.Settings_SetMapMode,mode:a,serviceType:b.serviceType||0,serviceProvider:b.serviceProvider||0,coordType:b.coordType||0,mapPoint:b.mapPoint||[0,0],longitude:b.longitude||0,latitude:b.latitude||0,cache:b.cache||":memory:",style:b.style||"mapbox://styles/mapbox/streets-v10",groundHeight:b.groundHeight||0,renderMode:b.renderMode||0,decalMode:b.decalMode||1,serverURL:b.serverURL,coordOrder:b.coordOrder,maxLevel:b.maxLevel},c)}getMapMode(a){return this.int.call0(k.Settings_GetMapMode,
a)}setPakFile(a,b){return this.int.call({command:k.SettingsPanel_setPak,bFolder:!1,pakFile:B(a)},b)}setPakFolder(a,b){return this.int.call({command:k.SettingsPanel_setPak,bFolder:!0,pakFolder:B(a)},b)}}class nc{constructor(a){this.int=a}startPolygonClip(a,b,c){return this.int.call({command:k.Tools_StartPolygonClip,data:B({id:"0",coordinates:K(a),toggleImageCut:b})},c)}startPlaneClip(a,b,c,e,d){return this.int.call({command:k.Tools_StartPlaneClip,location:a,rotation:b,isShowPlane:c,isEdit:e},d)}startVolumeClip(a,
b,c,e,d,g){return this.int.call({command:k.Tools_StartVolumeClip,bbox:a,rotation:d,value:b,isShowPlane:c,isEdit:e},g)}updateVolumeClip(a,b,c,e,d,g){return this.int.call({command:k.Tools_UpdateVolumeClip,bbox:a,rotation:d,value:b,isShowPlane:c,isEdit:e},g)}stopClip(a){return this.stopPolygonClip(a)}stopPolygonClip(a){return this.int.call0(k.Tools_StopClip,a)}stopPlaneClip(a){return this.int.call({command:k.Tools_StopPlaneClip},a)}stopVolumeClip(a){return this.int.call({command:k.Tools_StopVolumeClip},
a)}setMeasurement(a,b,c){b=b||{};return this.int.call({command:k.Tools_SetMeasurement,type:a,pointSize:b.pointSize,textSize:b.textSize,textColor:D(b.textColor),pointColor:D(b.pointColor),lineColor:D(b.lineColor),areaColor:D(b.areaColor),showCoordinateText:b.showCoordinateText},c)}startMeasurement(a){return this.int.call0(k.Tools_StartMeasurement,a)}stopMeasurement(a){return this.int.call0(k.Tools_StopMeasurement,a)}lineIntersect(a,b,c){return this.int.call({command:k.Tools_LineIntersect,data:[{start:a,
end:b}],highPrecision:!1,returnDetails:!0},c)}linesIntersect(a,b,c,e){return this.int.call({command:k.Tools_LineIntersect,data:B(a),highPrecision:b,returnDetails:c},e)}startGeometryEdit(a,b,c){return this.int.call({command:k.Tools_StartGeometryEdit,id:a,type:b},c)}stopGeometryEdit(a){return this.int.call0(k.Tools_StopGeometryEdit,a)}startSkylineAnalysis(a,b){a=a||{};a.outlineColor&&(a.outlineColor=D(a.outlineColor));a.sceneColor&&(a.sceneColor=D(a.sceneColor));a.skylineColor&&(a.skylineColor=D(a.skylineColor));
a.backgroundColor&&(a.backgroundColor=D(a.backgroundColor));if(a.tileLayers){a.tileLayers=B(a.tileLayers);for(let c of a.tileLayers)c.hasOwnProperty("color")&&(c.color=D(c.color))}a.command=k.Tools_StartSkylineAnalysis;return this.int.call(a,b)}stopSkylineAnalysis(a){return this.int.call0(k.Tools_StopSkylineAnalysis,a)}exportSkyline(a,b,c,e){c=c||{};c.skylineColor&&(c.skylineColor=D(c.skylineColor));c.backgroundColor&&(c.backgroundColor=D(c.backgroundColor));if(c.tileLayers){c.tileLayers=B(c.tileLayers);
for(let d of c.tileLayer)d.hasOwnProperty("color")&&(d.color=D(d.color))}return this.int.call({command:k.Tools_ExportSkyline,path:a,size:b,skylineColor:c.skylineColor,backgroundColor:c.backgroundColor},e)}startViewshedAnalysis(a,b){a=a||{};a.visibleColor&&(a.visibleColor=D(a.visibleColor));a.invisibleColor&&(a.invisibleColor=D(a.invisibleColor));a.command=k.Tools_StartViewshedAnalysis;return this.int.call(a,b)}stopViewshedAnalysis(a){return this.int.call0(k.Tools_StopViewshedAnalysis,a)}startVisiblityAnalysis(a,
b){a=a||{};a.visibleColor&&(a.visibleColor=D(a.visibleColor));a.invisibleColor&&(a.invisibleColor=D(a.invisibleColor));a.command=k.Tools_StartVisiblityAnalysis;return this.int.call(a,b)}stopVisiblityAnalysis(a){return this.int.call0(k.Tools_StopVisiblityAnalysis,a)}startViewDomeAnalysis(a,b){a=a||{};a.visibleColor&&(a.visibleColor=D(a.visibleColor));a.invisibleColor&&(a.invisibleColor=D(a.invisibleColor));a.command=k.Tools_StartViewDomeAnalysis;return this.int.call(a,b)}stopViewDomeAnalysis(a){return this.int.call0(k.Tools_StopViewDomeAnalysis,
a)}startCutFillAnalysis(a,b){a=a||{};a.cutLineColor&&(a.cutLineColor=D(a.cutLineColor));a.fillLineColor&&(a.fillLineColor=D(a.fillLineColor));a.cutPointColor&&(a.cutPointColor=D(a.cutPointColor));a.fillPointColor&&(a.fillPointColor=D(a.fillPointColor));a.gridColor&&(a.gridColor=D(a.gridColor));a.command=k.Tools_StartCutFillAnalysis;return this.int.call(a,b)}stopCutFillAnalysis(a){return this.int.call0(k.Tools_StopCutFillAnalysis,a)}startSunshineAnalysis(a,b){var c=/^(0?[0-9]|1[0-9]|[2][0-3]):(0?[0-9]|[1-5][0-9])$/;
if(null==a||void 0==a)console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a");else{let e=a.startTime,d=a.endTime;if(null==e||""==e||null==d||""==d)console.error("\u65f6\u95f4\u53c2\u6570\u683c\u5f0f\u4e0d\u6b63\u786e");else if(e.match(c)&&d.match(c))if(e.split(":"))if(a.startHour=parseInt(e.split(":")[0]),a.startMinute=parseInt(e.split(":")[1]),d.split(":"))if(a.endHour=parseInt(d.split(":")[0]),a.endMinute=parseInt(d.split(":")[1]),a.startHour>a.endHour)console.error("\u5f00\u59cb\u65f6\u95f4\u4e0d\u80fd\u5927\u4e8e\u7ed3\u675f\u65f6\u95f4");
else{if(a.startHour==a.endHour){if(a.startMinute==a.endMinute){console.error("\u5f00\u59cb\u65f6\u95f4\u4e0d\u80fd\u7b49\u4e8e\u7ed3\u675f\u65f6\u95f4");return}if(a.startMinute>a.endMinute){console.error("\u5f00\u59cb\u65f6\u95f4\u4e0d\u80fd\u5927\u4e8e\u7ed3\u675f\u65f6\u95f4");return}}!R(a.groundElevation)&&za(a.groundElevation)&&(a.undersideHeight=a.groundElevation,delete a.groundElevation);a.command=k.Tools_StartSunshineAnalysis;return this.int.call(a,b)}else console.error("\u53c2\u6570endTime\u65f6\u95f4\u683c\u5f0f\u4e0d\u6b63\u786e");
else console.error("\u53c2\u6570startTime\u65f6\u95f4\u683c\u5f0f\u4e0d\u6b63\u786e");else console.error("\u65f6\u95f4\u53c2\u6570\u683c\u5f0f\u4e0d\u6b63\u786e")}}stopSunshineAnalysis(a){return this.int.call0(k.Tools_StopSunshineAnalysis,a)}startTerrainSlopeAnalysis(a,b){a.command=k.Tools_StartTerrainSlopeAnalysis;a.arrowColor=D(a.arrowColor);return this.int.call(a,b)}stopTerrainSlopeAnalysis(a){return this.int.call0(k.Tools_StopTerrainSlopeAnalysis,a)}startContourLineAnalysis(a,b){a.command=k.Tools_StartContourLineAnalysis;
a.contourLineColor=D(a.contourLineColor);return this.int.call(a,b)}stopContourLineAnalysis(a){return this.int.call0(k.Tools_StopContourLineAnalysis,a)}startFloodFill(a,b){a=a||{};a.color&&(a.color=D(a.color));a.command=k.Tools_StartFloodFill;return this.int.call(a,b)}stopFloodFill(a){return this.int.call0(k.Tools_StopFloodFill,a)}replaceTextureByVideo(a,b,c){return this.int.call({command:k.Tools_ReplaceTexture,texturePackage:a,newTexture:b,type:1},c)}replaceTextureByImage(a,b,c){return this.int.call({command:k.Tools_ReplaceTexture,
texturePackage:a,newTexture:b,type:2},c)}replaceTextureByUrl(a,b,c){return this.int.call({command:k.Tools_ReplaceTexture,texturePackage:a,newTexture:b,type:3},c)}restoreTexture(a,b){return this.int.call({command:k.Tools_RestoreTexture,ids:B(a)},b)}showPanel(a,b,c){this.showAnalysisPanel(a,b,c)}hidePanel(a){this.hideAnalysisPanel(a)}showAnalysisPanel(a,b,c){return this.int.call({command:k.Tools_AnalysisPopupAttributes,type:a,position:b},c)}hideAnalysisPanel(a){return this.int.call({command:k.Tools_AnalysisCloseAttributes},
a)}showUIPanel(a,b,c){return this.int.call({command:k.Tools_FunctionNavBar,type:a,position:b},c)}hideUIPanel(a,b){return this.int.call({command:k.Tools_HideFunctionNavBar,type:a},b)}getUIPanel(a,b){return this.int.call({command:k.Tools_GetFunctionNavBar,type:a},b)}}class oc{constructor(a){this.int=a}getParams(a){return this.int.call0(k.Weather_GetParams,a)}_setParam(a,b){return this.int.call({command:k.Weather_SetParams,data:a},b)}setDateTime(a,b,c,e,d,g,h){return this.int.call({command:k.Weather_SetDate,
year:a,month:b,day:c,hour:e,minute:d,daynightLoop:g},h)}getDateTime(a){return this.int.call0(k.Weather_GetDate,a)}simulateTime(a,b,c,e){let d=0,g=0,h=0,q=0;a instanceof Array?(0<a.length&&(d=a[0]),1<a.length&&(g=a[1])):d=parseInt(a);b instanceof Array?(0<b.length&&(h=b[0]),1<b.length&&(q=b[1])):h=parseInt(b);return this.int.call({command:k.Weather_SimulateTime,startHour:d,startMinute:g,endHour:h,endMinute:q,duration:c},e)}setCloudThickness(a,b){return this.int.call({command:k.Weather_SetParams,lowCloudHeight:a},
b)}setCloudDensity(a,b){return this.int.call({command:k.Weather_SetParams,lowCloudDensity:a},b)}setCloudHeight(a,b){return this.int.call({command:k.Weather_SetParams,cloudHeight:a},b)}setRainParam(a,b,c,e,d,g,h){e&&"function"==typeof e&&(h=e);return this.int.call({command:k.Weather_SetParams,rainSnow:1,rainSnowStrength:a,rainSnowSpeed:b,raindropSize:c,rainSnowSize:c||null,rainSnowCamMotionAlignmentScale:d||null,rainSnowColor:e||null,rainSnowOvercastStrength:g||null},h)}setSnowParam(a,b,c,e,d,g,h){e&&
"function"==typeof e&&(h=e);return this.int.call({command:k.Weather_SetParams,rainSnow:2,rainSnowStrength:a,rainSnowSpeed:b,snowflakeSize:c,rainSnowSize:c||null,rainSnowCamMotionAlignmentScale:d||null,rainSnowColor:e||null,rainSnowOvercastStrength:g||null},h)}disableRainSnow(a){return this.int.call({command:k.Weather_SetParams,rainSnow:0},a)}setFogParam(a,b,c,e,d,g){return this.int.call({command:k.Weather_SetParams,fogDensity:a,fogColor:b,fogHeightFalloff:c,fogStartDistance:e,fogOpacity:d},g)}setSunIntensity(a,
b){return this.int.call({command:k.Weather_SetParams,sunIntensity:a},b)}setMoonIntensity(a,b){return this.int.call({command:k.Weather_SetParams,moonIntensity:a},b)}setAmbientLightIntensity(a,b){return this.int.call({command:k.Weather_SetParams,ambientLightIntensity:a},b)}setTemperature(a,b){return this.int.call({command:k.Weather_SetParams,temperature:a},b)}setShadowIntensity(a,b){return this.int.call({command:k.Weather_SetParams,shadowIntensity:a},b)}setShadowQuality(a,b){return this.int.call({command:k.Weather_SetParams,
shadowQuality:a},b)}setShadowDistance(a,b){return this.int.call({command:k.Weather_SetParams,shadowDistance:a},b)}setDarkMode(a,b){return this.int.call({command:k.Weather_SetParams,darkMode:!!a},b)}setSunSize(a,b){return this.int.call({command:k.Weather_SetParams,sunSize:a},b)}setMoonSize(a,b){return this.int.call({command:k.Weather_SetParams,moonSize:a},b)}setSkyVisibleMaxHeight(a,b){return this.int.call({command:k.Weather_SetParams,atmosphereVisibleHeight:a||1E5},b)}setCloudParam(a,b,c,e){return this.int.call({command:k.Weather_SetParams,
cloudsColor:D(a),cloudsAltitude:b,cloudShadowStrength:c},e)}setLowCloud(a,b,c,e,d,g){return this.int.call({command:k.Weather_SetParams,lowCloudCoverage:a,lowCloudDensity:b,lowCloudHeight:c,lowCloudWindSpeed:e,lowCloudWindDirection:d},g)}setHighCloud(a,b,c,e,d,g,h){return this.int.call({command:k.Weather_SetParams,highCloudLayerCoverage:a,highCloudWindSpeed:b,highCloudWindDirection:c,cirrusCloudDensity:e,cirrostratusCloudDensity:d,cirrocumulusCloudDensity:g},h)}}var xa=null;class ta{constructor(a,
b){a||(xa=this);this.isReady=!1;this.options=b||{};(this.player=this.options.player)||H.setLanguage();"function"==typeof ta.__onApiConstructed&&ta.__onApiConstructed(this);if(this.isCalledInCEF=sa.valid())this.isReady=this.isConnected=!0;this.callbackMap=new Map;this.websocket=null;this.callbackIndex=0;this.tickMarkerId="___tick_delegate_marker___";!this.player&&a&&(this.url=`ws://${a}`,this.connectWebSocket());this.apiQueue=new ob(c=>this.sendApi(c))}getPlayer(){return this.player}setHost(a,b){this.url=
`ws://${a}:${b}`}connectWebSocket(){if(!this.player){this.log(`Connecting: ${this.url}`);if("undefined"==typeof window)this.websocket=new (require("ws"))(this.url);else{if(!("WebSocket"in window)){console.error("Not Support WebSocket!");return}this.websocket=new WebSocket(this.url)}this.websocket.onopen=()=>this.onConnectionOpen();this.websocket.onmessage=a=>this.onConnectionMessage(a.data);this.websocket.onclose=a=>this.onConnectionClose(a);this.websocket.onerror=a=>this.onConnectionError(a)}}setEventCallback(a){this.options.onEvent=
a}destroy(){this.isDestroyed=!0;this.player&&this.player.destroy();this.websocket&&(this.websocket.close(),this.websocket=null)}reset(a,b){return this.call({command:k.Reset,resetType:a||Da.ClearObjects},b)}saveProject(a){return this.call0(k.SaveProject,a)}registerTick(a,b,c){b=b||{};"__execute__"!=a&&(b.func="tick");this.call({command:k.RegisterJsCommunication,id:this.tickMarkerId,url:a,func:b.func,x:b.x||4,y:b.y||4,width:b.width||400,height:b.height||300,visible:b.visible||!1},c)}removeTick(a){this.call({command:k.UnRegisterJsCommunication,
ids:[this.tickMarkerId]},a)}showTickWindow(a,b){this.isCalledInCEF?sa.showTickWindow(a):a?this.registerTick("__show__",{},b):this.registerTick("",{},b)}executeJsInTickPage(a,b){this.registerTick("__execute__",{func:a},b)}getVersion(){return"6.1.1126.19146"}checkApiReady(){if(!this.isReady)throw console.error("The interface call is not ready!"),"The interface call is not ready!";return!0}setEnableAliases(){this.ct=this.cameraTour;this.tl=this.tileLayer;this.ctag=this.customTag;this.hdm2=this.hydrodynamicModel2;
this.hdm=this.hydrodynamicModel;this.fe=this.finiteElement;this.line=this.polyline;this.ol=this.odline;this.p3d=this.polygon3d;this.hm=this.heatmap;this.hm3d=this.heatmap3d;this.co=this.customObject;this.ha=this.highlightArea;this.vp=this.videoProjection;this.dw=this.dynamicWater;this.ff=this.floodFill;this.c3d=this.cesium3DTileset;this.shapeFile=this.shp=this.shapeFileLayer;this.ata=this.antenna;this.sw=this.signalWave;this.imagery=this.imageryLayer;this.geoJSON=this.geoJSONLayer;this.vc=this.vehicle;
this.eh=this.editHelper;this.sp=this.settingsPanel}get camera(){if(!this.checkApiReady())return null;this._camera||(this._camera=new gc(this));return this._camera}get coord(){if(!this.checkApiReady())return null;this._coord||(this._coord=new hc(this));return this._coord}get infoTree(){if(!this.checkApiReady())return null;this._infoTree||(this._infoTree=new jc(this));return this._infoTree}get cameraTour(){if(!this.checkApiReady())return null;this._cameraTour||(this._cameraTour=new qb(this));return this._cameraTour}get tileLayer(){if(!this.checkApiReady())return null;
this._tileLayer||(this._tileLayer=new $b(this));return this._tileLayer}get tag(){if(!this.checkApiReady())return null;this._tag||(this._tag=new Yb(this));return this._tag}get marker(){if(!this.checkApiReady())return null;this._marker||(this._marker=new Lb(this));return this._marker}get marker3d(){if(!this.checkApiReady())return null;this._marker3d||(this._marker3d=new Mb(this));return this._marker3d}get customTag(){if(!this.checkApiReady())return null;this._customTag||(this._customTag=new ub(this));
return this._customTag}get radiationPoint(){if(!this.checkApiReady())return null;this._radiationPoint||(this._radiationPoint=new Tb(this));return this._radiationPoint}get customMesh(){if(!this.checkApiReady())return null;this._customMesh||(this._customMesh=new sb(this));return this._customMesh}get waterMesh(){if(!this.checkApiReady())return null;this._waterMesh||(this._waterMesh=new fc(this));return this._waterMesh}get waterFlowField(){if(!this.checkApiReady())return null;this._waterFlowField||(this._waterFlowField=
new ec(this));return this._waterFlowField}get vectorField(){if(!this.checkApiReady())return null;this._vectorField||(this._vectorField=new ac(this));return this._vectorField}get hydrodynamicModel2(){if(!this.checkApiReady())return null;this._hydrodynamicModel2||(this._hydrodynamicModel2=new Eb(this));return this._hydrodynamicModel2}get hydrodynamicModel(){if(!this.checkApiReady())return null;this._hydrodynamicModel||(this._hydrodynamicModel=new Ib(this));return this._hydrodynamicModel}get finiteElement(){if(!this.checkApiReady())return null;
this._finiteElement||(this._finiteElement=new xb(this));return this._finiteElement}get fluid(){if(!this.checkApiReady())return null;this._fluid||(this._fluid=new zb(this));return this._fluid}get polyline(){if(!this.checkApiReady())return null;this._polyline||(this._polyline=new Sb(this));return this._polyline}get odline(){if(!this.checkApiReady())return null;this._odline||(this._odline=new Ob(this));return this._odline}get polygon3d(){if(!this.checkApiReady())return null;this._polygon3d||(this._polygon3d=
new Rb(this));return this._polygon3d}get polygon(){if(!this.checkApiReady())return null;this._polygon||(this._polygon=new Qb(this));return this._polygon}get heatmap(){if(!this.checkApiReady())return null;this._heatmap||(this._heatmap=new Bb(this));return this._heatmap}get heatmap3d(){if(!this.checkApiReady())return null;this._heatmap3d||(this._heatmap3d=new Cb(this));return this._heatmap3d}get beam(){if(!this.checkApiReady())return null;this._beam||(this._beam=new pb(this));return this._beam}get highlightArea(){if(!this.checkApiReady())return null;
this._highlightArea||(this._highlightArea=new Db(this));return this._highlightArea}get customObject(){if(!this.checkApiReady())return null;this._customObject||(this._customObject=new tb(this));return this._customObject}get videoProjection(){if(!this.checkApiReady())return null;this._videoProjection||(this._videoProjection=new dc(this));return this._videoProjection}get panorama(){if(!this.checkApiReady())return null;this._panorama||(this._panorama=new Pb(this));return this._panorama}get decal(){if(!this.checkApiReady())return null;
this._decal||(this._decal=new vb(this));return this._decal}get dynamicWater(){if(!this.checkApiReady())return null;this._dynamicWater||(this._dynamicWater=new wb(this));return this._dynamicWater}get floodFill(){if(!this.checkApiReady())return null;this._floodFill||(this._floodFill=new yb(this));return this._floodFill}get cesium3DTileset(){if(!this.checkApiReady())return null;this._cesium3DTileset||(this._cesium3DTileset=new rb(this));return this._cesium3DTileset}get shapeFileLayer(){if(!this.checkApiReady())return null;
this._shapeFileLayer||(this._shapeFileLayer=new Vb(this));return this._shapeFileLayer}get light(){if(!this.checkApiReady())return null;this._light||(this._light=new Kb(this));return this._light}get antenna(){if(!this.checkApiReady())return null;this._antenna||(this._antenna=new Fb(this));return this._antenna}get signalWave(){if(!this.checkApiReady())return null;this._signalWave||(this._signalWave=new Wb(this));return this._signalWave}get river(){if(!this.checkApiReady())return null;this._river||(this._river=
new Ub(this));return this._river}get hydrodynamic1d(){if(!this.checkApiReady())return null;this._hydrodynamic1d||(this._hydrodynamic1d=new Gb(this));return this._hydrodynamic1d}get hydrodynamic2d(){if(!this.checkApiReady())return null;this._hydrodynamic2d||(this._hydrodynamic2d=new Hb(this));return this._hydrodynamic2d}get imageryLayer(){if(!this.checkApiReady())return null;this._imageryLayer||(this._imageryLayer=new Jb(this));return this._imageryLayer}get geoJSONLayer(){if(!this.checkApiReady())return null;
this._geoJSONLayer||(this._geoJSONLayer=new Ab(this));return this._geoJSONLayer}get vehicle(){if(!this.checkApiReady())return null;this._vehicle||(this._vehicle=new bc(this));return this._vehicle}get vehicle2(){if(!this.checkApiReady())return null;this._vehicle2||(this._vehicle2=new cc(this));return this._vehicle2}get markerLayer(){if(!this.checkApiReady())return null;this._markerLayer||(this._markerLayer=new Nb(this));return this._markerLayer}get splineMesh(){if(!this.checkApiReady())return null;
this._splineMesh||(this._splineMesh=new Xb(this));return this._splineMesh}get misc(){if(!this.checkApiReady())return null;this._misc||(this._misc=new kc(this));return this._misc}get tools(){if(!this.checkApiReady())return null;this._tools||(this._tools=new nc(this));return this._tools}get settings(){if(!this.checkApiReady())return null;this._settings||(this._settings=new lc(this));return this._settings}get weather(){if(!this.checkApiReady())return null;this._weather||(this._weather=new oc(this));
return this._weather}get editHelper(){if(!this.checkApiReady())return null;this._editHelper||(this._editHelper=new ic(this));return this._editHelper}get settingsPanel(){if(!this.checkApiReady())return null;this._settingsPanel||(this._settingsPanel=new mc(this));return this._settingsPanel}_getCallbackIndex(){return++this.callbackIndex}call0(a,b){return this.call({command:a},b)}call(a,b){if(!this.isConnected)return this.logWithColor("red","Not connected!"),b?void 0:Promise.reject("Not connected!");
if(void 0==a.command||a.command==k.None)return this.logWithColor("red","command is undefined or None"),b?void 0:Promise.reject("command is undefined or None");a.timestamp=Date.now();a.callbackIndex=this._getCallbackIndex();a.__command=k[a.command]||"Unknown";a.__version=this.getVersion();null===b&&(a.__noResponse=!0);let c=a.command+"_"+a.callbackIndex;b&&"function"==typeof b&&(this.callbackMap[c]=b);if(this.isCalledInCEF&&null===b)return delete a.__noResponse,this.callbackMap[c]=null,this.sendApi(a),
a;let e=JSON.stringify(a);this.log("");this.logWithColor("RoyalBlue",`Request: ${k[a.command]||"Unknown"}`,!0);this.logWithColor("green","\uff08"+(new Date(a.timestamp)).toLocaleTimeString()+"\uff09");this.logWithColor("gray",`${e}`);if(b||null===b)this.apiQueue.push(a);else return new Promise(d=>{this.callbackMap[c]=d;this.apiQueue.push(a)})}sendApi(a){this.isCalledInCEF?sa.execute(JSON.stringify(a)):this.player?this.player.sendApi(a):this.sendStringByWS(a)}sendApiToCS(a,b){if(!this.player)return this.logWithColor("red",
"This method can only be called in the Cloud environment!"),Promise.reject("This method can only be called in the Cloud environment!");a.timestamp=Date.now();a.callbackIndex=this._getCallbackIndex();let c=JSON.stringify(a);this.log("");this.logWithColor("RoyalBlue",`Request: ${a.command}`,!0);this.logWithColor("green","\uff08"+(new Date(a.timestamp)).toLocaleTimeString()+"\uff09");this.logWithColor("gray",`${c}`);let e=a.command+"_"+a.callbackIndex;if(b&&"function"==typeof b)this.callbackMap[e]=b,
this.player.sendApi(a,!0);else return new Promise(d=>{this.callbackMap[e]=d;this.player.sendApi(a,!0)})}sendStringByWS(a){if(a){a=JSON.stringify(a);var b=a.length;if(16777216>=b)this.websocket.send(a);else{var c=Math.ceil(b/16777216),e=[];for(let d=0;d<c;d++)e[d]=(0==d?"__MI_SECTION_START__":d==c-1?"__MI_SECTION_END__":"__MI_SECTION__")+a.substr(16777216*d,d==c-1?b-16777216*d:16777216);for(let d of e)this.websocket.send(d)}}}viewHome(a){switch(a){case 0:this.savedCamera?this.camera.set(this.savedCamera,
0):this.initialCameraPosition&&this.camera.set(this.initialCameraPosition,0);break;case 1:this.savedCamera=null;this.viewHome(0);break;case 2:this.camera.get(b=>this.savedCamera=b.camera)}}onReady(){this.isReady=!0;if("function"==typeof this.options.onReady)this.options.onReady();this.call({command:k.Misc_GetVersion});if(this.player)this.player.onApiReady()}log(a,b,c){if("function"==typeof this.options.onLog)this.options.onLog(a,b,c)}logWithColor(a,b,c){this.log(b,c,a)}onConnectionOpen(){this.isConnected=
!0;this.logWithColor("blue","Connected!")}onConnectionClose(a){this.isConnected=!1;this.logWithColor("red",`Connection closed! code: ${a.code||"-"}, reason: ${1006==a.code?H.getString("Disconnect"):a.reason||a.message||"-"}`);this.log("");this.logWithColor("SpringGreen","Reconnecting...");this.isDestroyed||this.player||this.connectWebSocket()}onConnectionError(a){console.error("WebSocket error observed")}onConnectionMessage(a,b){a=a.replace(/~!@~!@~!@/g,'\\"');this.log("");let c=null;try{c=JSON.parse(a)}catch(d){this.isCalledInCEF?
(document.writeln(d.message),document.writeln("<hr>"),document.writeln(a)):(console.error(d.message),this.log("Response: [Unknown]"));return}if(c.command==k.Misc_GetVersion){this.misc.apiVersionServer=c.version;if(!this.misc.isApiVersionMatched()){var e=H.getString("VersionMismatch").format(this.getVersion(),this.misc.apiVersionServer);console.warn(e);"undefined"!=typeof window&&alert(e)}if("function"==typeof this.options.onApiVersion)this.options.onApiVersion(this.misc.apiVersionServer)}if(c.command){"string"==
typeof c.command?e=c.command:(this.apiQueue.callNext(c.callbackIndex),e=k[c.command]||"Unknown");let d=Date.now()-c.timestamp;this.logWithColor("RoyalBlue",`Response: ${e}`,!0);this.logWithColor("green",` (${H.getString("TimeConsuming")}${d}ms)`);this.logWithColor("gray",`${H.getString("RequestTime")}${(new Date(c.timestamp)).toLocaleTimeString()}`,!0);this.logWithColor("gray",`  ${H.getString("ResponseTime")}${(new Date(Date.now())).toLocaleTimeString()}`,!0);this.logWithColor("gray",`  ${H.getString("MessageLength")}${a.length}`);
a=c.command+"_"+c.callbackIndex;(e=this.callbackMap[a])?(e(c),delete this.callbackMap[a]):null===e&&"function"===typeof tick_next&&tick_next(c,b)}else if(c.eventtype)if(this.log("Response: Event"),"CompleteInitialization"==c.eventtype)this.log("The initialization is complete, now you can call the interfaces in onReady callback function."),this.initialCameraPosition=c.InitialCamera,c.ResourcesPath&&(this.resourcesPath=c.ResourcesPath,this.log("ResourcesPath:"+this.resourcesPath)),this.onReady();else if("NodeConfiguration"==
c.eventtype)this.resourcesPath=c.ResourcesPath,this.log("ResourcesPath:"+this.resourcesPath);else if("MainThreadBusy"==c.eventtype)this.apiQueue.onMainThreadBusy(c),this.player&&this.player.onMainThreadBusy(c);else{if("function"==typeof this.options.onEvent)this.options.onEvent(c)}else this.log("Response: [Unknown]");b=JSON.stringify(c,(d,g)=>g instanceof Array?JSON.stringify(g):g,"\t").replace(/"\[/g,"[").replace(/\]"/g,"]").replace(/\\"/g,'"').replace(/""/g,'"');this.logWithColor("gray",b)}quit(){return this.call0(k.Quit)}test(a,
b,c){return this.call({command:k.SimulateTest__,type:a,int32Val:b},c)}}const U={WS_Disconnected:0,WS_Connecting:1,WS_Connected:2,RTC_Opened:3,Video_LoadedMetaData:4,OnReady:5},ba={StartingProcess:0,CheckingBusy:1,ConfirmBusy:2,ProcessStartFailed:3,ProcessStarted:4,LoadingProject:5,ProjectLoaded:6,UserAccessDenied:7,CheckingLicense:8};class pc{constructor(){"undefined"!=typeof navigator&&(this.isUnix="Mac68K"==navigator.platform||"MacPPC"==navigator.platform||"Macintosh"==navigator.platform||"MacIntel"==
navigator.platform||-1!=navigator.platform.indexOf("Linux"),this.isChrome=-1!=navigator.userAgent.indexOf("Chrome"),this.isMobileDevice=/Android|Linux|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent),this.isIOSDevice=/iPhone|iPad|iPod/i.test(navigator.userAgent),this.isSafari=/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent),this.isAndroidDevice=/Android/i.test(navigator.userAgent),this.isInWeixinBrowser)}fullscreen(a){a&&(a.requestFullscreen?a.requestFullscreen():
a.mozRequestFullScreen?a.mozRequestFullScreen():a.msRequestFullscreen?a.msRequestFullscreen:a.webkitRequestFullscreen&&a.webkitRequestFullscreen())}exitFullscreen(){document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen||(document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen&&document.webkitCancelFullScreen())}isFullscreen(){return document.fullscreen||document.mozFullScreen||document.webkitIsFullScreen}isFunction(a){return"function"==typeof a}}
var S=new pc;const ja={abnormal:1006,invalid_message:1008,out_of_control:4E3,instance_disconnected:4001,instance_not_found:4002,instance_start_failed:4003,webrtc_connection_error:4004,one_client_allowed:4005,timeout:4006,iid_required:4007,locked:4008,invalid_project:4009,no_free_instance:4010,kicked:4100,syncing_data:4101,instance_killed_by_user:4102,invalid_password:4103,nodeservice_stopped:4105,instance_is_busy:4107,ip_no_access:4108,unknown_client:4109,num_of_instances_exceeded:4110,instance_not_auth:4111,
permission_denied:4112,instance_was_preempted:4113,no_username_provided:4114,user_does_not_exist:4115,not_logged_in:4116};V.EMPTY_OBJECT=Object.freeze({});class la{}la.doParamsCompatibility=a=>{a.apiOptions=a.apiOptions||{};a.events=a.events||{};a.ui=a.ui||{};"undefined"!=typeof a.showMarker&&(a.showStatus=a.showMarker,delete a.showMarker);"undefined"!=typeof a.actionEventHander&&(a.onaction=a.actionEventHander,delete a.actionEventHander);"undefined"!=typeof a.useBuiltinCursor&&(a.useBuiltinCursors=
a.useBuiltinCursor,delete a.useBuiltinCursor);"undefined"!=typeof a.keyEventReceiver&&(a.keyEventTarget=a.keyEventReceiver,delete a.keyEventReceiver);"undefined"!=typeof a.ui.debugTouchPanel&&(a.ui.debugEventsPanel=a.ui.debugTouchPanel,delete a.ui.debugTouchPanel);var b=(c,e)=>{let d=a[c];d&&(a.events[e]=d,delete a[c])};b("onclose","onConnClose");b("onloaded","onVideoLoaded");b("onvideostatus","onRtcStatsReport");b("onaction","mouseKeyListener");b=(c,e)=>{let d=a.events.mouseKeyListener[c];d&&(a.events.mouseKeyListener[e]=
d,delete a.events.mouseKeyListener[c])};a.events.mouseKeyListener&&(b("onmouseenter","onMouseEnter"),b("onmouseleave","onMouseLeave"),b("onmousemove","onMouseMove"),b("onmousedown","onMouseDown"),b("onmouseup","onMouseUp"),b("onkeydown","onKeyDown"),b("onkeyup","onKeyUp"),b("onkeypress","onKeyPress"));b=(c,e)=>{"undefined"!=typeof a[c]&&(a.ui[e]=a[c],delete a[c])};b("showStartupInfo","startupInfo");b("showStatus","statusButton");b("showFullscreenButton","fullscreenButton");b("showHomeButton","homeButton");
b("showTaskList","taskListBar")};la.setDefaultParamValues=a=>{a.enableEventSync=V(a.enableEventSync,!1);a.disableResizeObserver=V(a.disableResizeObserver,!1);a.enableApiCallLog=V(a.enableApiCallLog,!1);a.receiveRenderEvents=V(a.receiveRenderEvents,!0);a.registerEvents=V(a.registerEvents,!0);a.keyEventTarget=V(a.keyEventTarget,"video");a.useBuiltinCursors=V(a.useBuiltinCursors,!0);a.ui.startupInfo=V(a.ui.startupInfo,!0);a.ui.statusIndicator=V(a.ui.statusIndicator,!0);a.ui.statusButton=V(a.ui.statusButton,
!1);a.ui.fullscreenButton=V(a.ui.fullscreenButton,!1);a.ui.homeButton=V(a.ui.homeButton,!1);a.ui.taskListBar=V(a.ui.taskListBar,1);a.ui.debugEventsPanel=V(a.ui.debugEventsPanel,!1);a.offer=1};la.processParams=a=>{la.doParamsCompatibility(a);la.setDefaultParamValues(a)};W.prototype.reset=function(){this.reg[0]=1937774191;this.reg[1]=1226093241;this.reg[2]=388252375;this.reg[3]=3666478592;this.reg[4]=2842636476;this.reg[5]=372324522;this.reg[6]=3817729613;this.reg[7]=2969243214;this.chunk=[];this.size=
0};W.prototype.strToBytes=function(a){for(var b,c,e=[],d=0;d<a.length;d++){b=a.charCodeAt(d);c=[];do c.push(b&255),b>>=8;while(b);e=e.concat(c.reverse())}return e};W.prototype.write=function(a){a="string"===typeof a?this.strToBytes(a):a;this.size+=a.length;var b=64-this.chunk.length;if(a.length<b)this.chunk=this.chunk.concat(a);else for(this.chunk=this.chunk.concat(a.slice(0,b));64<=this.chunk.length;)this._compress(this.chunk),this.chunk=b<a.length?a.slice(b,Math.min(b+64,a.length)):[],b+=64};W.prototype.sum=
function(a,b){a&&(this.reset(),this.write(a));this._fill();for(a=0;a<this.chunk.length;a+=64)this._compress(this.chunk.slice(a,a+64));if("hex"==b)for(b="",a=0;8>a;a++)b+=this.reg[a].toString(16);else for(b=Array(32),a=0;8>a;a++){var c=this.reg[a];b[4*a+3]=(c&255)>>>0;c>>>=8;b[4*a+2]=(c&255)>>>0;c>>>=8;b[4*a+1]=(c&255)>>>0;c>>>=8;b[4*a]=(c&255)>>>0}this.reset();return b};W.prototype._compress=function(a){if(64>a)console.error("compress error: not enough data");else{var b=this._expand(a);a=this.reg.slice(0);
for(var c=0;64>c;c++){var e=this._rotl(a[0],12)+a[4]+this._rotl(this._t(c),c);e=(e&4294967295)>>>0;e=this._rotl(e,7);var d=(e^this._rotl(a[0],12))>>>0,g=this._ff(c,a[0],a[1],a[2]);g=g+a[3]+d+b[c+68];g=(g&4294967295)>>>0;d=this._gg(c,a[4],a[5],a[6]);d=d+a[7]+e+b[c];d=(d&4294967295)>>>0;a[3]=a[2];a[2]=this._rotl(a[1],9);a[1]=a[0];a[0]=g;a[7]=a[6];a[6]=this._rotl(a[5],19);a[5]=a[4];a[4]=(d^this._rotl(d,9)^this._rotl(d,17))>>>0}for(b=0;8>b;b++)this.reg[b]=(this.reg[b]^a[b])>>>0}};W.prototype._fill=function(){var a=
8*this.size,b=this.chunk.push(128)%64;for(8>64-b&&(b-=64);56>b;b++)this.chunk.push(0);for(b=0;4>b;b++)this.chunk.push(Math.floor(a/4294967296)>>>8*(3-b)&255);for(b=0;4>b;b++)this.chunk.push(a>>>8*(3-b)&255)};W.prototype._expand=function(a){for(var b=Array(132),c=0;16>c;c++)b[c]=a[4*c]<<24,b[c]|=a[4*c+1]<<16,b[c]|=a[4*c+2]<<8,b[c]|=a[4*c+3],b[c]>>>=0;for(a=16;68>a;a++)c=b[a-16]^b[a-9]^this._rotl(b[a-3],15),c=c^this._rotl(c,15)^this._rotl(c,23),b[a]=(c^this._rotl(b[a-13],7)^b[a-6])>>>0;for(a=0;64>a;a++)b[a+
68]=(b[a]^b[a+4])>>>0;return b};W.prototype._rotl=function(a,b){b%=32;return(a<<b|a>>>32-b)>>>0};W.prototype._t=function(a){if(0<=a&&16>a)return 2043430169;if(16<=a&&64>a)return 2055708042;console.error("invalid j for constant Tj")};W.prototype._ff=function(a,b,c,e){if(0<=a&&16>a)return(b^c^e)>>>0;if(16<=a&&64>a)return(b&c|b&e|c&e)>>>0;console.error("invalid j for bool function FF");return 0};W.prototype._gg=function(a,b,c,e){if(0<=a&&16>a)return(b^c^e)>>>0;if(16<=a&&64>a)return(b&c|~b&e)>>>0;console.error("invalid j for bool function GG");
return 0};W.prototype.toArray=function(a,b){for(var c=[],e=0;e<a.length;e++){var d=a[e];b&&(d=b(d));c.push(d)}return c};class qc{constructor(a){this.options=a;this.checkParamsMap=new Map}resetInteractTimestamp(a){this.timeOfLastInteraction=a}connect(a){window.WebSocket=window.WebSocket||window.MozWebSocket;window.WebSocket?(this.websocket=new WebSocket(a),this.websocket.onopen=()=>{if(this.options.onopen)this.options.onopen();this.timerPing=setInterval(()=>{this.timeOfLastInteraction&&this.send({type:"ping",
time:this.timeOfLastInteraction})},3E3);"string"==typeof this.options.customString&&this.send({type:"customString",content:this.options.customString})},this.websocket.onmessage=b=>{if(b=JSON.parse(b.data)){switch(b.type){case "checkParamsResult":this.onCheckParamsResponse(b);return;case "detectResponse":this.send(b);return}if(this.options.onmessage)this.options.onmessage(b)}},this.websocket.onerror=b=>{if(this.options.onerror)this.options.onerror(b)},this.websocket.onclose=b=>{this.websocket=void 0;
this.timerPing&&(clearInterval(this.timerPing),this.timerPing=null);if(this.options.onclose)this.options.onclose(b)}):alert("Your browser does not support WebSocket")}isOpened(){return this.websocket&&1===this.websocket.readyState}send(a){a&&this.isOpened()&&this.websocket.send(JSON.stringify(a))}close(a){console.log(`call SignallingConnection.close: ${a||" "}`);this.isOpened()&&(this.websocket.close(a),this.websocket=null)}sendReady(){this.send({type:"ready"})}sendCustomString(a){"string"==typeof a&&
this.send({type:"customString",content:a})}sendCandidate(a){a.candidate&&a.candidate.candidate&&this.send({type:"iceCandidate",candidate:a.candidate})}sendOffer(a,b){this.send({type:"offer",sdp:a,hasVideo:b})}sendApi(a){"api"==(null==a?void 0:a.type)?this.send(a):console.error("Invalid API call format!")}sendInstancePassword(a){"string"==typeof a&&0<a.length&&this.send({type:"inst_sec",p:Pa(a)})}updateParams(a){this.send({type:"updateParams",data:a})}checkParams(a){a.timestamp=Date.now();return new Promise(b=>
{this.checkParamsMap[a.timestamp]=b;this.send({type:"checkParams",data:a});this.timerOfCheckParams=setTimeout(()=>{let c=this.checkParamsMap[a.timestamp];c&&(delete this.checkParamsMap[a.timestamp],c(null))},1E3)})}onCheckParamsResponse(a){let b=this.checkParamsMap[a.timestamp];b&&(clearTimeout(this.timerOfCheckParams),delete this.checkParamsMap[a.timestamp],b(a))}}(function(a){"object"===typeof u&&"undefined"!==typeof module?module.exports=a():"function"===typeof define&&define.amd?define([],a):
("undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:this).adapter=a()})(function(){return function(){function a(b,c,e){function d(q,l){if(!c[q]){if(!b[q]){var f="function"==typeof require&&require;if(!l&&f)return f(q,!0);if(g)return g(q,!0);l=Error("Cannot find module '"+q+"'");throw l.code="MODULE_NOT_FOUND",l;}l=c[q]={exports:{}};b[q][0].call(l.exports,function(m){return d(b[q][1][m]||m)},l,l.exports,a,b,c,e)}return c[q].exports}for(var g="function"==
typeof require&&require,h=0;h<e.length;h++)d(e[h]);return d}return a}()({1:[function(a,b,c){a=(0,a("./adapter_factory.js").adapterFactory)({window:"undefined"==typeof window?{}:window});b.exports=a},{"./adapter_factory.js":2}],2:[function(a,b,c){function e(m){if(m&&m.__esModule)return m;var t={};if(null!=m)for(var x in m)Object.prototype.hasOwnProperty.call(m,x)&&(t[x]=m[x]);t.default=m;return t}Object.defineProperty(c,"__esModule",{value:!0});c.adapterFactory=function(){var m=(0<arguments.length&&
void 0!==arguments[0]?arguments[0]:{}).window,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0},x=d.log,r=d.detectBrowser(m),y={browserDetails:r,commonShim:f,extractVersion:d.extractVersion,disableLog:d.disableLog,disableWarnings:d.disableWarnings};switch(r.browser){case "chrome":if(!g||!g.shimPeerConnection||!t.shimChrome){x("Chrome shim is not included in this adapter release.");break}if(null===r.version){x("Chrome shim can not determine version, not shimming.");
break}x("adapter.js shimming chrome.");y.browserShim=g;g.shimGetUserMedia(m);g.shimMediaStream(m);g.shimPeerConnection(m);g.shimOnTrack(m);g.shimAddTrackRemoveTrack(m);g.shimGetSendersWithDtmf(m);g.shimGetStats(m);g.shimSenderReceiverGetStats(m);g.fixNegotiationNeeded(m);f.shimRTCIceCandidate(m);f.shimConnectionState(m);f.shimMaxMessageSize(m);f.shimSendThrowTypeError(m);f.removeAllowExtmapMixed(m);break;case "firefox":if(!q||!q.shimPeerConnection||!t.shimFirefox){x("Firefox shim is not included in this adapter release.");
break}x("adapter.js shimming firefox.");y.browserShim=q;q.shimGetUserMedia(m);q.shimPeerConnection(m);q.shimOnTrack(m);q.shimRemoveStream(m);q.shimSenderGetStats(m);q.shimReceiverGetStats(m);q.shimRTCDataChannel(m);q.shimAddTransceiver(m);q.shimGetParameters(m);q.shimCreateOffer(m);q.shimCreateAnswer(m);f.shimRTCIceCandidate(m);f.shimConnectionState(m);f.shimMaxMessageSize(m);f.shimSendThrowTypeError(m);break;case "edge":if(!h||!h.shimPeerConnection||!t.shimEdge){x("MS edge shim is not included in this adapter release.");
break}x("adapter.js shimming edge.");y.browserShim=h;h.shimGetUserMedia(m);h.shimGetDisplayMedia(m);h.shimPeerConnection(m);h.shimReplaceTrack(m);f.shimMaxMessageSize(m);f.shimSendThrowTypeError(m);break;case "safari":if(!l||!t.shimSafari){x("Safari shim is not included in this adapter release.");break}x("adapter.js shimming safari.");y.browserShim=l;l.shimRTCIceServerUrls(m);l.shimCreateOfferLegacy(m);l.shimCallbacksAPI(m);l.shimLocalStreamsAPI(m);l.shimRemoteStreamsAPI(m);l.shimTrackEventTransceiver(m);
l.shimGetUserMedia(m);l.shimAudioContext(m);f.shimRTCIceCandidate(m);f.shimMaxMessageSize(m);f.shimSendThrowTypeError(m);f.removeAllowExtmapMixed(m);break;default:x("Unsupported browser!")}return y};b=a("./utils");var d=e(b);b=a("./chrome/chrome_shim");var g=e(b);b=a("./edge/edge_shim");var h=e(b);b=a("./firefox/firefox_shim");var q=e(b);b=a("./safari/safari_shim");var l=e(b);a=a("./common_shim");var f=e(a)},{"./chrome/chrome_shim":3,"./common_shim":6,"./edge/edge_shim":7,"./firefox/firefox_shim":11,
"./safari/safari_shim":14,"./utils":15}],3:[function(a,b,c){function e(f,m,t){m in f?Object.defineProperty(f,m,{value:t,enumerable:!0,configurable:!0,writable:!0}):f[m]=t;return f}function d(f){f.RTCPeerConnection.prototype.getLocalStreams=function(){var y=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};return Object.keys(this._shimmedLocalStreams).map(function(v){return y._shimmedLocalStreams[v][0]})};var m=f.RTCPeerConnection.prototype.addTrack;f.RTCPeerConnection.prototype.addTrack=
function(y,v){if(!v)return m.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var A=m.apply(this,arguments);this._shimmedLocalStreams[v.id]?-1===this._shimmedLocalStreams[v.id].indexOf(A)&&this._shimmedLocalStreams[v.id].push(A):this._shimmedLocalStreams[v.id]=[v,A];return A};var t=f.RTCPeerConnection.prototype.addStream;f.RTCPeerConnection.prototype.addStream=function(y){var v=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};y.getTracks().forEach(function(p){if(v.getSenders().find(function(w){return w.track===
p}))throw new DOMException("Track already exists.","InvalidAccessError");});var A=this.getSenders();t.apply(this,arguments);var n=this.getSenders().filter(function(p){return-1===A.indexOf(p)});this._shimmedLocalStreams[y.id]=[y].concat(n)};var x=f.RTCPeerConnection.prototype.removeStream;f.RTCPeerConnection.prototype.removeStream=function(y){this._shimmedLocalStreams=this._shimmedLocalStreams||{};delete this._shimmedLocalStreams[y.id];return x.apply(this,arguments)};var r=f.RTCPeerConnection.prototype.removeTrack;
f.RTCPeerConnection.prototype.removeTrack=function(y){var v=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};y&&Object.keys(this._shimmedLocalStreams).forEach(function(A){var n=v._shimmedLocalStreams[A].indexOf(y);-1!==n&&v._shimmedLocalStreams[A].splice(n,1);1===v._shimmedLocalStreams[A].length&&delete v._shimmedLocalStreams[A]});return r.apply(this,arguments)}}Object.defineProperty(c,"__esModule",{value:!0});c.shimGetDisplayMedia=c.shimGetUserMedia=void 0;var g="function"===typeof Symbol&&
"symbol"===typeof Symbol.iterator?function(f){return typeof f}:function(f){return f&&"function"===typeof Symbol&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},h=a("./getusermedia");Object.defineProperty(c,"shimGetUserMedia",{enumerable:!0,get:function(){return h.shimGetUserMedia}});var q=a("./getdisplaymedia");Object.defineProperty(c,"shimGetDisplayMedia",{enumerable:!0,get:function(){return q.shimGetDisplayMedia}});c.shimMediaStream=function(f){f.MediaStream=f.MediaStream||f.webkitMediaStream};
c.shimOnTrack=function(f){if("object"!==("undefined"===typeof f?"undefined":g(f))||!f.RTCPeerConnection||"ontrack"in f.RTCPeerConnection.prototype)l.wrapPeerConnectionEvent(f,"track",function(t){t.transceiver||Object.defineProperty(t,"transceiver",{value:{receiver:t.receiver}});return t});else{Object.defineProperty(f.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(t){this._ontrack&&this.removeEventListener("track",this._ontrack);this.addEventListener("track",
this._ontrack=t)},enumerable:!0,configurable:!0});var m=f.RTCPeerConnection.prototype.setRemoteDescription;f.RTCPeerConnection.prototype.setRemoteDescription=function(){var t=this;this._ontrackpoly||(this._ontrackpoly=function(x){x.stream.addEventListener("addtrack",function(r){var y=void 0;y=f.RTCPeerConnection.prototype.getReceivers?t.getReceivers().find(function(A){return A.track&&A.track.id===r.track.id}):{track:r.track};var v=new Event("track");v.track=r.track;v.receiver=y;v.transceiver={receiver:y};
v.streams=[x.stream];t.dispatchEvent(v)});x.stream.getTracks().forEach(function(r){var y=void 0;y=f.RTCPeerConnection.prototype.getReceivers?t.getReceivers().find(function(A){return A.track&&A.track.id===r.id}):{track:r};var v=new Event("track");v.track=r;v.receiver=y;v.transceiver={receiver:y};v.streams=[x.stream];t.dispatchEvent(v)})},this.addEventListener("addstream",this._ontrackpoly));return m.apply(this,arguments)}}};c.shimGetSendersWithDtmf=function(f){if("object"===("undefined"===typeof f?
"undefined":g(f))&&f.RTCPeerConnection&&!("getSenders"in f.RTCPeerConnection.prototype)&&"createDTMFSender"in f.RTCPeerConnection.prototype){var m=function(A,n){return{track:n,get dtmf(){void 0===this._dtmf&&(this._dtmf="audio"===n.kind?A.createDTMFSender(n):null);return this._dtmf},_pc:A}};if(!f.RTCPeerConnection.prototype.getSenders){f.RTCPeerConnection.prototype.getSenders=function(){this._senders=this._senders||[];return this._senders.slice()};var t=f.RTCPeerConnection.prototype.addTrack;f.RTCPeerConnection.prototype.addTrack=
function(A,n){var p=t.apply(this,arguments);p||(p=m(this,A),this._senders.push(p));return p};var x=f.RTCPeerConnection.prototype.removeTrack;f.RTCPeerConnection.prototype.removeTrack=function(A){x.apply(this,arguments);var n=this._senders.indexOf(A);-1!==n&&this._senders.splice(n,1)}}var r=f.RTCPeerConnection.prototype.addStream;f.RTCPeerConnection.prototype.addStream=function(A){var n=this;this._senders=this._senders||[];r.apply(this,[A]);A.getTracks().forEach(function(p){n._senders.push(m(n,p))})};
var y=f.RTCPeerConnection.prototype.removeStream;f.RTCPeerConnection.prototype.removeStream=function(A){var n=this;this._senders=this._senders||[];y.apply(this,[A]);A.getTracks().forEach(function(p){var w=n._senders.find(function(z){return z.track===p});w&&n._senders.splice(n._senders.indexOf(w),1)})}}else if("object"===("undefined"===typeof f?"undefined":g(f))&&f.RTCPeerConnection&&"getSenders"in f.RTCPeerConnection.prototype&&"createDTMFSender"in f.RTCPeerConnection.prototype&&f.RTCRtpSender&&!("dtmf"in
f.RTCRtpSender.prototype)){var v=f.RTCPeerConnection.prototype.getSenders;f.RTCPeerConnection.prototype.getSenders=function(){var A=this,n=v.apply(this,[]);n.forEach(function(p){return p._pc=A});return n};Object.defineProperty(f.RTCRtpSender.prototype,"dtmf",{get:function(){void 0===this._dtmf&&(this._dtmf="audio"===this.track.kind?this._pc.createDTMFSender(this.track):null);return this._dtmf}})}};c.shimGetStats=function(f){if(f.RTCPeerConnection){var m=f.RTCPeerConnection.prototype.getStats;f.RTCPeerConnection.prototype.getStats=
function(){var t=this,x=Array.prototype.slice.call(arguments),r=x[0],y=x[1];x=x[2];if(0<arguments.length&&"function"===typeof r)return m.apply(this,arguments);if(0===m.length&&(0===arguments.length||"function"!==typeof r))return m.apply(this,[]);var v=function(n){var p={};n.result().forEach(function(w){var z={id:w.id,timestamp:w.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[w.type]||w.type};w.names().forEach(function(C){z[C]=w.stat(C)});p[z.id]=z});return p},
A=function(n){return new Map(Object.keys(n).map(function(p){return[p,n[p]]}))};return 2<=arguments.length?m.apply(this,[function(n){y(A(v(n)))},r]):(new Promise(function(n,p){m.apply(t,[function(w){n(A(v(w)))},p])})).then(y,x)}}};c.shimSenderReceiverGetStats=function(f){if("object"===("undefined"===typeof f?"undefined":g(f))&&f.RTCPeerConnection&&f.RTCRtpSender&&f.RTCRtpReceiver){if(!("getStats"in f.RTCRtpSender.prototype)){var m=f.RTCPeerConnection.prototype.getSenders;m&&(f.RTCPeerConnection.prototype.getSenders=
function(){var y=this,v=m.apply(this,[]);v.forEach(function(A){return A._pc=y});return v});var t=f.RTCPeerConnection.prototype.addTrack;t&&(f.RTCPeerConnection.prototype.addTrack=function(){var y=t.apply(this,arguments);y._pc=this;return y});f.RTCRtpSender.prototype.getStats=function(){var y=this;return this._pc.getStats().then(function(v){return l.filterStats(v,y.track,!0)})}}if(!("getStats"in f.RTCRtpReceiver.prototype)){var x=f.RTCPeerConnection.prototype.getReceivers;x&&(f.RTCPeerConnection.prototype.getReceivers=
function(){var y=this,v=x.apply(this,[]);v.forEach(function(A){return A._pc=y});return v});l.wrapPeerConnectionEvent(f,"track",function(y){y.receiver._pc=y.srcElement;return y});f.RTCRtpReceiver.prototype.getStats=function(){var y=this;return this._pc.getStats().then(function(v){return l.filterStats(v,y.track,!1)})}}if("getStats"in f.RTCRtpSender.prototype&&"getStats"in f.RTCRtpReceiver.prototype){var r=f.RTCPeerConnection.prototype.getStats;f.RTCPeerConnection.prototype.getStats=function(){if(0<
arguments.length&&arguments[0]instanceof f.MediaStreamTrack){var y=arguments[0],v=void 0,A=void 0,n=void 0;this.getSenders().forEach(function(p){p.track===y&&(v?n=!0:v=p)});this.getReceivers().forEach(function(p){p.track===y&&(A?n=!0:A=p);return p.track===y});return n||v&&A?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):v?v.getStats():A?A.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return r.apply(this,
arguments)}}}};c.shimAddTrackRemoveTrackWithNative=d;c.shimAddTrackRemoveTrack=function(f){function m(p,w){var z=w.sdp;Object.keys(p._reverseStreams||[]).forEach(function(C){C=p._reverseStreams[C];z=z.replace(new RegExp(p._streams[C.id].id,"g"),C.id)});return new RTCSessionDescription({type:w.type,sdp:z})}function t(p,w){var z=w.sdp;Object.keys(p._reverseStreams||[]).forEach(function(C){C=p._reverseStreams[C];z=z.replace(new RegExp(C.id,"g"),p._streams[C.id].id)});return new RTCSessionDescription({type:w.type,
sdp:z})}if(f.RTCPeerConnection){var x=l.detectBrowser(f);if(f.RTCPeerConnection.prototype.addTrack&&65<=x.version)return d(f);var r=f.RTCPeerConnection.prototype.getLocalStreams;f.RTCPeerConnection.prototype.getLocalStreams=function(){var p=this,w=r.apply(this);this._reverseStreams=this._reverseStreams||{};return w.map(function(z){return p._reverseStreams[z.id]})};var y=f.RTCPeerConnection.prototype.addStream;f.RTCPeerConnection.prototype.addStream=function(p){var w=this;this._streams=this._streams||
{};this._reverseStreams=this._reverseStreams||{};p.getTracks().forEach(function(C){if(w.getSenders().find(function(E){return E.track===C}))throw new DOMException("Track already exists.","InvalidAccessError");});if(!this._reverseStreams[p.id]){var z=new f.MediaStream(p.getTracks());this._streams[p.id]=z;this._reverseStreams[z.id]=p;p=z}y.apply(this,[p])};var v=f.RTCPeerConnection.prototype.removeStream;f.RTCPeerConnection.prototype.removeStream=function(p){this._streams=this._streams||{};this._reverseStreams=
this._reverseStreams||{};v.apply(this,[this._streams[p.id]||p]);delete this._reverseStreams[this._streams[p.id]?this._streams[p.id].id:p.id];delete this._streams[p.id]};f.RTCPeerConnection.prototype.addTrack=function(p,w){var z=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var C=[].slice.call(arguments,1);if(1!==C.length||!C[0].getTracks().find(function(E){return E===p}))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.",
"NotSupportedError");if(this.getSenders().find(function(E){return E.track===p}))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{};this._reverseStreams=this._reverseStreams||{};(C=this._streams[w.id])?(C.addTrack(p),Promise.resolve().then(function(){z.dispatchEvent(new Event("negotiationneeded"))})):(C=new f.MediaStream([p]),this._streams[w.id]=C,this._reverseStreams[C.id]=w,this.addStream(C));return this.getSenders().find(function(E){return E.track===
p})};["createOffer","createAnswer"].forEach(function(p){var w=f.RTCPeerConnection.prototype[p],z=e({},p,function(){var C=this,E=arguments;return arguments.length&&"function"===typeof arguments[0]?w.apply(this,[function(G){G=m(C,G);E[0].apply(null,[G])},function(G){E[1]&&E[1].apply(null,G)},arguments[2]]):w.apply(this,arguments).then(function(G){return m(C,G)})});f.RTCPeerConnection.prototype[p]=z[p]});var A=f.RTCPeerConnection.prototype.setLocalDescription;f.RTCPeerConnection.prototype.setLocalDescription=
function(){if(!arguments.length||!arguments[0].type)return A.apply(this,arguments);arguments[0]=t(this,arguments[0]);return A.apply(this,arguments)};var n=Object.getOwnPropertyDescriptor(f.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(f.RTCPeerConnection.prototype,"localDescription",{get:function(){var p=n.get.apply(this);return""===p.type?p:m(this,p)}});f.RTCPeerConnection.prototype.removeTrack=function(p){var w=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.",
"InvalidStateError");if(!p._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(p._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};var z=void 0;Object.keys(this._streams).forEach(function(C){w._streams[C].getTracks().find(function(E){return p.track===E})&&(z=w._streams[C])});z&&(1===z.getTracks().length?this.removeStream(this._reverseStreams[z.id]):
z.removeTrack(p.track),this.dispatchEvent(new Event("negotiationneeded")))}}};c.shimPeerConnection=function(f){var m=l.detectBrowser(f);!f.RTCPeerConnection&&f.webkitRTCPeerConnection&&(f.RTCPeerConnection=f.webkitRTCPeerConnection);if(f.RTCPeerConnection){var t=0===f.RTCPeerConnection.prototype.addIceCandidate.length;53>m.version&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(r){var y=f.RTCPeerConnection.prototype[r],v=e({},r,function(){arguments[0]=new ("addIceCandidate"===
r?f.RTCIceCandidate:f.RTCSessionDescription)(arguments[0]);return y.apply(this,arguments)});f.RTCPeerConnection.prototype[r]=v[r]});var x=f.RTCPeerConnection.prototype.addIceCandidate;f.RTCPeerConnection.prototype.addIceCandidate=function(){return t||arguments[0]?78>m.version&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():x.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}}};c.fixNegotiationNeeded=function(f){var m=l.detectBrowser(f);l.wrapPeerConnectionEvent(f,
"negotiationneeded",function(t){var x=t.target;if(!(72>m.version||x.getConfiguration&&"plan-b"===x.getConfiguration().sdpSemantics)||"stable"===x.signalingState)return t})};var l=function(f){if(f&&f.__esModule)return f;var m={};if(null!=f)for(var t in f)Object.prototype.hasOwnProperty.call(f,t)&&(m[t]=f[t]);m.default=f;return m}(a("../utils.js"))},{"../utils.js":15,"./getdisplaymedia":4,"./getusermedia":5}],4:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});c.shimGetDisplayMedia=
function(e,d){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||!e.navigator.mediaDevices||("function"!==typeof d?console.error("shimGetDisplayMedia: getSourceId argument is not a function"):e.navigator.mediaDevices.getDisplayMedia=function(g){return d(g).then(function(h){var q=g.video&&g.video.width,l=g.video&&g.video.height;g.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:h,maxFrameRate:g.video&&g.video.frameRate||3}};q&&(g.video.mandatory.maxWidth=q);l&&
(g.video.mandatory.maxHeight=l);return e.navigator.mediaDevices.getUserMedia(g)})})}},{}],5:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});var e="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"===typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h};c.shimGetUserMedia=function(h){var q=h&&h.navigator;if(q.mediaDevices){var l=d.detectBrowser(h),f=function(r){if("object"!==("undefined"===
typeof r?"undefined":e(r))||r.mandatory||r.optional)return r;var y={};Object.keys(r).forEach(function(v){if("require"!==v&&"advanced"!==v&&"mediaSource"!==v){var A="object"===e(r[v])?r[v]:{ideal:r[v]};void 0!==A.exact&&"number"===typeof A.exact&&(A.min=A.max=A.exact);var n=function(w,z){return w?w+z.charAt(0).toUpperCase()+z.slice(1):"deviceId"===z?"sourceId":z};if(void 0!==A.ideal){y.optional=y.optional||[];var p={};"number"===typeof A.ideal?(p[n("min",v)]=A.ideal,y.optional.push(p),p={},p[n("max",
v)]=A.ideal):p[n("",v)]=A.ideal;y.optional.push(p)}void 0!==A.exact&&"number"!==typeof A.exact?(y.mandatory=y.mandatory||{},y.mandatory[n("",v)]=A.exact):["min","max"].forEach(function(w){void 0!==A[w]&&(y.mandatory=y.mandatory||{},y.mandatory[n(w,v)]=A[w])})}});r.advanced&&(y.optional=(y.optional||[]).concat(r.advanced));return y},m=function(r,y){if(61<=l.version)return y(r);if((r=JSON.parse(JSON.stringify(r)))&&"object"===e(r.audio)){var v=function(p,w,z){w in p&&!(z in p)&&(p[z]=p[w],delete p[w])};
r=JSON.parse(JSON.stringify(r));v(r.audio,"autoGainControl","googAutoGainControl");v(r.audio,"noiseSuppression","googNoiseSuppression");r.audio=f(r.audio)}if(r&&"object"===e(r.video)){var A=r.video.facingMode;A=A&&("object"===("undefined"===typeof A?"undefined":e(A))?A:{ideal:A});v=66>l.version;if(!(!A||"user"!==A.exact&&"environment"!==A.exact&&"user"!==A.ideal&&"environment"!==A.ideal||q.mediaDevices.getSupportedConstraints&&q.mediaDevices.getSupportedConstraints().facingMode&&!v)){delete r.video.facingMode;
var n=void 0;if("environment"===A.exact||"environment"===A.ideal)n=["back","rear"];else if("user"===A.exact||"user"===A.ideal)n=["front"];if(n)return q.mediaDevices.enumerateDevices().then(function(p){p=p.filter(function(z){return"videoinput"===z.kind});var w=p.find(function(z){return n.some(function(C){return z.label.toLowerCase().includes(C)})});!w&&p.length&&n.includes("back")&&(w=p[p.length-1]);w&&(r.video.deviceId=A.exact?{exact:w.deviceId}:{ideal:w.deviceId});r.video=f(r.video);g("chrome: "+
JSON.stringify(r));return y(r)})}r.video=f(r.video)}g("chrome: "+JSON.stringify(r));return y(r)},t=function(r){return 64<=l.version?r:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",
ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[r.name]||r.name,message:r.message,constraint:r.constraint||r.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}};q.getUserMedia=function(r,y,v){m(r,function(A){q.webkitGetUserMedia(A,y,function(n){v&&v(t(n))})})}.bind(q);if(q.mediaDevices.getUserMedia){var x=q.mediaDevices.getUserMedia.bind(q.mediaDevices);q.mediaDevices.getUserMedia=function(r){return m(r,function(y){return x(y).then(function(v){if(y.audio&&
!v.getAudioTracks().length||y.video&&!v.getVideoTracks().length)throw v.getTracks().forEach(function(A){A.stop()}),new DOMException("","NotFoundError");return v},function(v){return Promise.reject(t(v))})})}}}};var d=function(h){if(h&&h.__esModule)return h;var q={};if(null!=h)for(var l in h)Object.prototype.hasOwnProperty.call(h,l)&&(q[l]=h[l]);q.default=h;return q}(a("../utils.js")),g=d.log},{"../utils.js":15}],6:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});var e="function"===
typeof Symbol&&"symbol"===typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"===typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h};c.shimRTCIceCandidate=function(h){if(h.RTCIceCandidate&&!(h.RTCIceCandidate&&"foundation"in h.RTCIceCandidate.prototype)){var q=h.RTCIceCandidate;h.RTCIceCandidate=function(l){"object"===("undefined"===typeof l?"undefined":e(l))&&l.candidate&&0===l.candidate.indexOf("a=")&&(l=JSON.parse(JSON.stringify(l)),l.candidate=
l.candidate.substr(2));if(l.candidate&&l.candidate.length){var f=new q(l);l=d.default.parseCandidate(l.candidate);var m=Object.assign(f,l);m.toJSON=function(){return{candidate:m.candidate,sdpMid:m.sdpMid,sdpMLineIndex:m.sdpMLineIndex,usernameFragment:m.usernameFragment}};return m}return new q(l)};h.RTCIceCandidate.prototype=q.prototype;g.wrapPeerConnectionEvent(h,"icecandidate",function(l){l.candidate&&Object.defineProperty(l,"candidate",{value:new h.RTCIceCandidate(l.candidate),writable:"false"});
return l})}};c.shimMaxMessageSize=function(h){if(h.RTCPeerConnection){var q=g.detectBrowser(h);"sctp"in h.RTCPeerConnection.prototype||Object.defineProperty(h.RTCPeerConnection.prototype,"sctp",{get:function(){return"undefined"===typeof this._sctp?null:this._sctp}});var l=function(r){if(!r||!r.sdp)return!1;r=d.default.splitSections(r.sdp);r.shift();return r.some(function(y){return(y=d.default.parseMLine(y))&&"application"===y.kind&&-1!==y.protocol.indexOf("SCTP")})},f=function(r){r=r.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);
if(null===r||2>r.length)return-1;r=parseInt(r[1],10);return r!==r?-1:r},m=function(r){var y=65536;"firefox"===q.browser&&(y=57>q.version?-1===r?16384:2147483637:60>q.version?57===q.version?65535:65536:2147483637);return y},t=function(r,y){var v=65536;"firefox"===q.browser&&57===q.version&&(v=65535);r=d.default.matchPrefix(r.sdp,"a=max-message-size:");0<r.length?v=parseInt(r[0].substr(19),10):"firefox"===q.browser&&-1!==y&&(v=2147483637);return v},x=h.RTCPeerConnection.prototype.setRemoteDescription;
h.RTCPeerConnection.prototype.setRemoteDescription=function(){this._sctp=null;"chrome"===q.browser&&76<=q.version&&"plan-b"===this.getConfiguration().sdpSemantics&&Object.defineProperty(this,"sctp",{get:function(){return"undefined"===typeof this._sctp?null:this._sctp},enumerable:!0,configurable:!0});if(l(arguments[0])){var r=f(arguments[0]),y=m(r);r=t(arguments[0],r);var v=void 0;v=0===y&&0===r?Number.POSITIVE_INFINITY:0===y||0===r?Math.max(y,r):Math.min(y,r);y={};Object.defineProperty(y,"maxMessageSize",
{get:function(){return v}});this._sctp=y}return x.apply(this,arguments)}}};c.shimSendThrowTypeError=function(h){function q(f,m){var t=f.send;f.send=function(){var x=arguments[0];x=x.length||x.size||x.byteLength;if("open"===f.readyState&&m.sctp&&x>m.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+m.sctp.maxMessageSize+" bytes)");return t.apply(f,arguments)}}if(h.RTCPeerConnection&&"createDataChannel"in h.RTCPeerConnection.prototype){var l=h.RTCPeerConnection.prototype.createDataChannel;
h.RTCPeerConnection.prototype.createDataChannel=function(){var f=l.apply(this,arguments);q(f,this);return f};g.wrapPeerConnectionEvent(h,"datachannel",function(f){q(f.channel,f.target);return f})}};c.shimConnectionState=function(h){if(h.RTCPeerConnection&&!("connectionState"in h.RTCPeerConnection.prototype)){var q=h.RTCPeerConnection.prototype;Object.defineProperty(q,"connectionState",{get:function(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},
enumerable:!0,configurable:!0});Object.defineProperty(q,"onconnectionstatechange",{get:function(){return this._onconnectionstatechange||null},set:function(l){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange);l&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=l)},enumerable:!0,configurable:!0});["setLocalDescription","setRemoteDescription"].forEach(function(l){var f=q[l];
q[l]=function(){this._connectionstatechangepoly||(this._connectionstatechangepoly=function(m){var t=m.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;var x=new Event("connectionstatechange",m);t.dispatchEvent(x)}return m},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly));return f.apply(this,arguments)}})}};c.removeAllowExtmapMixed=function(h){if(h.RTCPeerConnection){var q=g.detectBrowser(h);if(!("chrome"===q.browser&&
71<=q.version||"safari"===q.browser&&605<=q.version)){var l=h.RTCPeerConnection.prototype.setRemoteDescription;h.RTCPeerConnection.prototype.setRemoteDescription=function(f){f&&f.sdp&&-1!==f.sdp.indexOf("\na=extmap-allow-mixed")&&(f.sdp=f.sdp.split("\n").filter(function(m){return"a=extmap-allow-mixed"!==m.trim()}).join("\n"));return l.apply(this,arguments)}}}};var d=(b=a("sdp"))&&b.__esModule?b:{default:b},g=function(h){if(h&&h.__esModule)return h;var q={};if(null!=h)for(var l in h)Object.prototype.hasOwnProperty.call(h,
l)&&(q[l]=h[l]);q.default=h;return q}(a("./utils"))},{"./utils":15,sdp:17}],7:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});c.shimGetDisplayMedia=c.shimGetUserMedia=void 0;var e=a("./getusermedia");Object.defineProperty(c,"shimGetUserMedia",{enumerable:!0,get:function(){return e.shimGetUserMedia}});var d=a("./getdisplaymedia");Object.defineProperty(c,"shimGetDisplayMedia",{enumerable:!0,get:function(){return d.shimGetDisplayMedia}});c.shimPeerConnection=function(l){var f=g.detectBrowser(l);
if(l.RTCIceGatherer&&(l.RTCIceCandidate||(l.RTCIceCandidate=function(x){return x}),l.RTCSessionDescription||(l.RTCSessionDescription=function(x){return x}),15025>f.version)){var m=Object.getOwnPropertyDescriptor(l.MediaStreamTrack.prototype,"enabled");Object.defineProperty(l.MediaStreamTrack.prototype,"enabled",{set:function(x){m.set.call(this,x);var r=new Event("enabled");r.enabled=x;this.dispatchEvent(r)}})}!l.RTCRtpSender||"dtmf"in l.RTCRtpSender.prototype||Object.defineProperty(l.RTCRtpSender.prototype,
"dtmf",{get:function(){void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new l.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null));return this._dtmf}});l.RTCDtmfSender&&!l.RTCDTMFSender&&(l.RTCDTMFSender=l.RTCDtmfSender);var t=(0,q.default)(l,f.version);l.RTCPeerConnection=function(x){x&&x.iceServers&&(x.iceServers=(0,h.filterIceServers)(x.iceServers,f.version),g.log("ICE servers after filtering:",x.iceServers));return new t(x)};l.RTCPeerConnection.prototype=t.prototype};c.shimReplaceTrack=
function(l){!l.RTCRtpSender||"replaceTrack"in l.RTCRtpSender.prototype||(l.RTCRtpSender.prototype.replaceTrack=l.RTCRtpSender.prototype.setTrack)};var g=function(l){if(l&&l.__esModule)return l;var f={};if(null!=l)for(var m in l)Object.prototype.hasOwnProperty.call(l,m)&&(f[m]=l[m]);f.default=l;return f}(a("../utils")),h=a("./filtericeservers"),q=(a=a("rtcpeerconnection-shim"))&&a.__esModule?a:{default:a}},{"../utils":15,"./filtericeservers":8,"./getdisplaymedia":9,"./getusermedia":10,"rtcpeerconnection-shim":16}],
8:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});c.filterIceServers=function(d,g){var h=!1;d=JSON.parse(JSON.stringify(d));return d.filter(function(q){if(q&&(q.urls||q.url)){var l=q.urls||q.url;q.url&&!q.urls&&e.deprecated("RTCIceServer.url","RTCIceServer.urls");var f="string"===typeof l;f&&(l=[l]);l=l.filter(function(m){return 0===m.indexOf("stun:")?!1:(m=m.startsWith("turn")&&!m.startsWith("turn:[")&&m.includes("transport=udp"))&&!h?h=!0:m&&!h});delete q.url;q.urls=f?l[0]:l;return!!l.length}})};
var e=function(d){if(d&&d.__esModule)return d;var g={};if(null!=d)for(var h in d)Object.prototype.hasOwnProperty.call(d,h)&&(g[h]=d[h]);g.default=d;return g}(a("../utils"))},{"../utils":15}],9:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});c.shimGetDisplayMedia=function(e){"getDisplayMedia"in e.navigator&&e.navigator.mediaDevices&&!(e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices)&&(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator))}},
{}],10:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});c.shimGetUserMedia=function(e){e=e&&e.navigator;var d=function(h){return{name:{PermissionDeniedError:"NotAllowedError"}[h.name]||h.name,message:h.message,constraint:h.constraint,toString:function(){return this.name}}},g=e.mediaDevices.getUserMedia.bind(e.mediaDevices);e.mediaDevices.getUserMedia=function(h){return g(h).catch(function(q){return Promise.reject(d(q))})}}},{}],11:[function(a,b,c){function e(l,f,m){f in l?Object.defineProperty(l,
f,{value:m,enumerable:!0,configurable:!0,writable:!0}):l[f]=m;return l}Object.defineProperty(c,"__esModule",{value:!0});c.shimGetDisplayMedia=c.shimGetUserMedia=void 0;var d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(l){return typeof l}:function(l){return l&&"function"===typeof Symbol&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l},g=a("./getusermedia");Object.defineProperty(c,"shimGetUserMedia",{enumerable:!0,get:function(){return g.shimGetUserMedia}});
var h=a("./getdisplaymedia");Object.defineProperty(c,"shimGetDisplayMedia",{enumerable:!0,get:function(){return h.shimGetDisplayMedia}});c.shimOnTrack=function(l){"object"===("undefined"===typeof l?"undefined":d(l))&&l.RTCTrackEvent&&"receiver"in l.RTCTrackEvent.prototype&&!("transceiver"in l.RTCTrackEvent.prototype)&&Object.defineProperty(l.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})};c.shimPeerConnection=function(l){var f=q.detectBrowser(l);if("object"===
("undefined"===typeof l?"undefined":d(l))&&(l.RTCPeerConnection||l.mozRTCPeerConnection)){!l.RTCPeerConnection&&l.mozRTCPeerConnection&&(l.RTCPeerConnection=l.mozRTCPeerConnection);53>f.version&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(r){var y=l.RTCPeerConnection.prototype[r],v=e({},r,function(){arguments[0]=new ("addIceCandidate"===r?l.RTCIceCandidate:l.RTCSessionDescription)(arguments[0]);return y.apply(this,arguments)});l.RTCPeerConnection.prototype[r]=
v[r]});if(68>f.version){var m=l.RTCPeerConnection.prototype.addIceCandidate;l.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?arguments[0]&&""===arguments[0].candidate?Promise.resolve():m.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}}var t={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},x=l.RTCPeerConnection.prototype.getStats;l.RTCPeerConnection.prototype.getStats=
function(){var r=Array.prototype.slice.call(arguments),y=r[1],v=r[2];return x.apply(this,[r[0]||null]).then(function(A){if(53>f.version&&!y)try{A.forEach(function(n){n.type=t[n.type]||n.type})}catch(n){if("TypeError"!==n.name)throw n;A.forEach(function(p,w){A.set(w,Object.assign({},p,{type:t[p.type]||p.type}))})}return A}).then(y,v)}}};c.shimSenderGetStats=function(l){if("object"===("undefined"===typeof l?"undefined":d(l))&&l.RTCPeerConnection&&l.RTCRtpSender&&!(l.RTCRtpSender&&"getStats"in l.RTCRtpSender.prototype)){var f=
l.RTCPeerConnection.prototype.getSenders;f&&(l.RTCPeerConnection.prototype.getSenders=function(){var t=this,x=f.apply(this,[]);x.forEach(function(r){return r._pc=t});return x});var m=l.RTCPeerConnection.prototype.addTrack;m&&(l.RTCPeerConnection.prototype.addTrack=function(){var t=m.apply(this,arguments);t._pc=this;return t});l.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}};c.shimReceiverGetStats=function(l){if("object"===("undefined"===
typeof l?"undefined":d(l))&&l.RTCPeerConnection&&l.RTCRtpSender&&!(l.RTCRtpSender&&"getStats"in l.RTCRtpReceiver.prototype)){var f=l.RTCPeerConnection.prototype.getReceivers;f&&(l.RTCPeerConnection.prototype.getReceivers=function(){var m=this,t=f.apply(this,[]);t.forEach(function(x){return x._pc=m});return t});q.wrapPeerConnectionEvent(l,"track",function(m){m.receiver._pc=m.srcElement;return m});l.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}};c.shimRemoveStream=
function(l){!l.RTCPeerConnection||"removeStream"in l.RTCPeerConnection.prototype||(l.RTCPeerConnection.prototype.removeStream=function(f){var m=this;q.deprecated("removeStream","removeTrack");this.getSenders().forEach(function(t){t.track&&f.getTracks().includes(t.track)&&m.removeTrack(t)})})};c.shimRTCDataChannel=function(l){l.DataChannel&&!l.RTCDataChannel&&(l.RTCDataChannel=l.DataChannel)};c.shimAddTransceiver=function(l){if("object"===("undefined"===typeof l?"undefined":d(l))&&l.RTCPeerConnection){var f=
l.RTCPeerConnection.prototype.addTransceiver;f&&(l.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];var m=arguments[1],t=m&&"sendEncodings"in m;t&&m.sendEncodings.forEach(function(y){if("rid"in y&&!/^[a-z0-9]{0,16}$/i.test(y.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in y&&!(1<=parseFloat(y.scaleResolutionDownBy)))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in y&&!(0<=parseFloat(y.maxFramerate)))throw new RangeError("max_framerate must be >= 0.0");
});var x=f.apply(this,arguments);if(t){var r=x.sender;t=r.getParameters();"encodings"in t||(t.encodings=m.sendEncodings,r.sendEncodings=m.sendEncodings,this.setParametersPromises.push(r.setParameters(t).then(function(){delete r.sendEncodings}).catch(function(){delete r.sendEncodings})))}return x})}};c.shimGetParameters=function(l){if("object"===("undefined"===typeof l?"undefined":d(l))&&l.RTCRtpSender){var f=l.RTCRtpSender.prototype.getParameters;f&&(l.RTCRtpSender.prototype.getParameters=function(){var m=
f.apply(this,arguments);return"sendEncodings"in this?Object.assign({},{encodings:this.sendEncodings},m):m})}};c.shimCreateOffer=function(l){if("object"===("undefined"===typeof l?"undefined":d(l))&&l.RTCPeerConnection){var f=l.RTCPeerConnection.prototype.createOffer;l.RTCPeerConnection.prototype.createOffer=function(){var m=this,t=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return f.apply(m,t)}).finally(function(){m.setParametersPromises=
[]}):f.apply(this,arguments)}}};c.shimCreateAnswer=function(l){if("object"===("undefined"===typeof l?"undefined":d(l))&&l.RTCPeerConnection){var f=l.RTCPeerConnection.prototype.createAnswer;l.RTCPeerConnection.prototype.createAnswer=function(){var m=this,t=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return f.apply(m,t)}).finally(function(){m.setParametersPromises=[]}):f.apply(this,arguments)}}};var q=function(l){if(l&&
l.__esModule)return l;var f={};if(null!=l)for(var m in l)Object.prototype.hasOwnProperty.call(l,m)&&(f[m]=l[m]);f.default=l;return f}(a("../utils"))},{"../utils":15,"./getdisplaymedia":12,"./getusermedia":13}],12:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});c.shimGetDisplayMedia=function(e,d){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||!e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=function(g){if(!g||!g.video)return g=new DOMException("getDisplayMedia without video constraints is undefined"),
g.name="NotFoundError",g.code=8,Promise.reject(g);!0===g.video?g.video={mediaSource:d}:g.video.mediaSource=d;return e.navigator.mediaDevices.getUserMedia(g)})}},{}],13:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});var e="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(g){return typeof g}:function(g){return g&&"function"===typeof Symbol&&g.constructor===Symbol&&g!==Symbol.prototype?"symbol":typeof g};c.shimGetUserMedia=function(g){var h=d.detectBrowser(g),
q=g&&g.navigator;g=g&&g.MediaStreamTrack;q.getUserMedia=function(x,r,y){d.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia");q.mediaDevices.getUserMedia(x).then(r,y)};if(!(55<h.version&&"autoGainControl"in q.mediaDevices.getSupportedConstraints())){var l=function(x,r,y){r in x&&!(y in x)&&(x[y]=x[r],delete x[r])},f=q.mediaDevices.getUserMedia.bind(q.mediaDevices);q.mediaDevices.getUserMedia=function(x){"object"===("undefined"===typeof x?"undefined":e(x))&&"object"===e(x.audio)&&
(x=JSON.parse(JSON.stringify(x)),l(x.audio,"autoGainControl","mozAutoGainControl"),l(x.audio,"noiseSuppression","mozNoiseSuppression"));return f(x)};if(g&&g.prototype.getSettings){var m=g.prototype.getSettings;g.prototype.getSettings=function(){var x=m.apply(this,arguments);l(x,"mozAutoGainControl","autoGainControl");l(x,"mozNoiseSuppression","noiseSuppression");return x}}if(g&&g.prototype.applyConstraints){var t=g.prototype.applyConstraints;g.prototype.applyConstraints=function(x){"audio"===this.kind&&
"object"===("undefined"===typeof x?"undefined":e(x))&&(x=JSON.parse(JSON.stringify(x)),l(x,"autoGainControl","mozAutoGainControl"),l(x,"noiseSuppression","mozNoiseSuppression"));return t.apply(this,[x])}}}};var d=function(g){if(g&&g.__esModule)return g;var h={};if(null!=g)for(var q in g)Object.prototype.hasOwnProperty.call(g,q)&&(h[q]=g[q]);h.default=g;return h}(a("../utils"))},{"../utils":15}],14:[function(a,b,c){function e(h){return h&&void 0!==h.video?Object.assign({},h,{video:g.compactObject(h.video)}):
h}Object.defineProperty(c,"__esModule",{value:!0});var d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"===typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h};c.shimLocalStreamsAPI=function(h){if("object"===("undefined"===typeof h?"undefined":d(h))&&h.RTCPeerConnection){"getLocalStreams"in h.RTCPeerConnection.prototype||(h.RTCPeerConnection.prototype.getLocalStreams=function(){this._localStreams||
(this._localStreams=[]);return this._localStreams});if(!("addStream"in h.RTCPeerConnection.prototype)){var q=h.RTCPeerConnection.prototype.addTrack;h.RTCPeerConnection.prototype.addStream=function(l){var f=this;this._localStreams||(this._localStreams=[]);this._localStreams.includes(l)||this._localStreams.push(l);l.getAudioTracks().forEach(function(m){return q.call(f,m,l)});l.getVideoTracks().forEach(function(m){return q.call(f,m,l)})};h.RTCPeerConnection.prototype.addTrack=function(l){for(var f=this,
m=arguments.length,t=Array(1<m?m-1:0),x=1;x<m;x++)t[x-1]=arguments[x];t&&t.forEach(function(r){f._localStreams?f._localStreams.includes(r)||f._localStreams.push(r):f._localStreams=[r]});return q.apply(this,arguments)}}"removeStream"in h.RTCPeerConnection.prototype||(h.RTCPeerConnection.prototype.removeStream=function(l){var f=this;this._localStreams||(this._localStreams=[]);var m=this._localStreams.indexOf(l);if(-1!==m){this._localStreams.splice(m,1);var t=l.getTracks();this.getSenders().forEach(function(x){t.includes(x.track)&&
f.removeTrack(x)})}})}};c.shimRemoteStreamsAPI=function(h){if("object"===("undefined"===typeof h?"undefined":d(h))&&h.RTCPeerConnection&&("getRemoteStreams"in h.RTCPeerConnection.prototype||(h.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in h.RTCPeerConnection.prototype))){Object.defineProperty(h.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(l){var f=this;this._onaddstream&&
(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly));this.addEventListener("addstream",this._onaddstream=l);this.addEventListener("track",this._onaddstreampoly=function(m){m.streams.forEach(function(t){f._remoteStreams||(f._remoteStreams=[]);if(!f._remoteStreams.includes(t)){f._remoteStreams.push(t);var x=new Event("addstream");x.stream=t;f.dispatchEvent(x)}})})}});var q=h.RTCPeerConnection.prototype.setRemoteDescription;h.RTCPeerConnection.prototype.setRemoteDescription=
function(){var l=this;this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(f){f.streams.forEach(function(m){l._remoteStreams||(l._remoteStreams=[]);if(!(0<=l._remoteStreams.indexOf(m))){l._remoteStreams.push(m);var t=new Event("addstream");t.stream=m;l.dispatchEvent(t)}})});return q.apply(l,arguments)}}};c.shimCallbacksAPI=function(h){if("object"===("undefined"===typeof h?"undefined":d(h))&&h.RTCPeerConnection){h=h.RTCPeerConnection.prototype;var q=h.createOffer,l=h.createAnswer,
f=h.setLocalDescription,m=h.setRemoteDescription,t=h.addIceCandidate;h.createOffer=function(r,y){var v=q.apply(this,[2<=arguments.length?arguments[2]:arguments[0]]);if(!y)return v;v.then(r,y);return Promise.resolve()};h.createAnswer=function(r,y){var v=l.apply(this,[2<=arguments.length?arguments[2]:arguments[0]]);if(!y)return v;v.then(r,y);return Promise.resolve()};var x=function(r,y,v){r=f.apply(this,[r]);if(!v)return r;r.then(y,v);return Promise.resolve()};h.setLocalDescription=x;x=function(r,y,
v){r=m.apply(this,[r]);if(!v)return r;r.then(y,v);return Promise.resolve()};h.setRemoteDescription=x;x=function(r,y,v){r=t.apply(this,[r]);if(!v)return r;r.then(y,v);return Promise.resolve()};h.addIceCandidate=x}};c.shimGetUserMedia=function(h){var q=h&&h.navigator;if(q.mediaDevices&&q.mediaDevices.getUserMedia){h=q.mediaDevices;var l=h.getUserMedia.bind(h);q.mediaDevices.getUserMedia=function(f){return l(e(f))}}!q.getUserMedia&&q.mediaDevices&&q.mediaDevices.getUserMedia&&(q.getUserMedia=function(f,
m,t){q.mediaDevices.getUserMedia(f).then(m,t)}.bind(q))};c.shimConstraints=e;c.shimRTCIceServerUrls=function(h){var q=h.RTCPeerConnection;h.RTCPeerConnection=function(l,f){if(l&&l.iceServers){for(var m=[],t=0;t<l.iceServers.length;t++){var x=l.iceServers[t];!x.hasOwnProperty("urls")&&x.hasOwnProperty("url")?(g.deprecated("RTCIceServer.url","RTCIceServer.urls"),x=JSON.parse(JSON.stringify(x)),x.urls=x.url,delete x.url,m.push(x)):m.push(l.iceServers[t])}l.iceServers=m}return new q(l,f)};h.RTCPeerConnection.prototype=
q.prototype;"generateCertificate"in h.RTCPeerConnection&&Object.defineProperty(h.RTCPeerConnection,"generateCertificate",{get:function(){return q.generateCertificate}})};c.shimTrackEventTransceiver=function(h){"object"===("undefined"===typeof h?"undefined":d(h))&&h.RTCTrackEvent&&"receiver"in h.RTCTrackEvent.prototype&&!("transceiver"in h.RTCTrackEvent.prototype)&&Object.defineProperty(h.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})};c.shimCreateOfferLegacy=
function(h){var q=h.RTCPeerConnection.prototype.createOffer;h.RTCPeerConnection.prototype.createOffer=function(l){if(l){"undefined"!==typeof l.offerToReceiveAudio&&(l.offerToReceiveAudio=!!l.offerToReceiveAudio);var f=this.getTransceivers().find(function(m){return"audio"===m.receiver.track.kind});!1===l.offerToReceiveAudio&&f?"sendrecv"===f.direction?f.setDirection?f.setDirection("sendonly"):f.direction="sendonly":"recvonly"===f.direction&&(f.setDirection?f.setDirection("inactive"):f.direction="inactive"):
!0!==l.offerToReceiveAudio||f||this.addTransceiver("audio");"undefined"!==typeof l.offerToReceiveVideo&&(l.offerToReceiveVideo=!!l.offerToReceiveVideo);f=this.getTransceivers().find(function(m){return"video"===m.receiver.track.kind});!1===l.offerToReceiveVideo&&f?"sendrecv"===f.direction?f.setDirection?f.setDirection("sendonly"):f.direction="sendonly":"recvonly"===f.direction&&(f.setDirection?f.setDirection("inactive"):f.direction="inactive"):!0!==l.offerToReceiveVideo||f||this.addTransceiver("video")}return q.apply(this,
arguments)}};c.shimAudioContext=function(h){"object"!==("undefined"===typeof h?"undefined":d(h))||h.AudioContext||(h.AudioContext=h.webkitAudioContext)};var g=function(h){if(h&&h.__esModule)return h;var q={};if(null!=h)for(var l in h)Object.prototype.hasOwnProperty.call(h,l)&&(q[l]=h[l]);q.default=h;return q}(a("../utils"))},{"../utils":15}],15:[function(a,b,c){function e(f,m,t){return(f=f.match(m))&&f.length>=t&&parseInt(f[t],10)}function d(f){return"[object Object]"!==Object.prototype.toString.call(f)?
f:Object.keys(f).reduce(function(m,t){var x="[object Object]"===Object.prototype.toString.call(f[t]),r=x?d(f[t]):f[t];x=x&&!Object.keys(r).length;if(void 0===r||x)return m;x=Object;var y=x.assign,v={};t in v?Object.defineProperty(v,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):v[t]=r;return y.call(x,m,v)},{})}function g(f,m,t){m&&!t.has(m.id)&&(t.set(m.id,m),Object.keys(m).forEach(function(x){x.endsWith("Id")?g(f,f.get(m[x]),t):x.endsWith("Ids")&&m[x].forEach(function(r){g(f,f.get(r),t)})}))}
Object.defineProperty(c,"__esModule",{value:!0});var h="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(f){return typeof f}:function(f){return f&&"function"===typeof Symbol&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f};c.extractVersion=e;c.wrapPeerConnectionEvent=function(f,m,t){if(f.RTCPeerConnection){f=f.RTCPeerConnection.prototype;var x=f.addEventListener;f.addEventListener=function(y,v){if(y!==m)return x.apply(this,arguments);var A=function(n){(n=t(n))&&
(v.handleEvent?v.handleEvent(n):v(n))};this._eventMap=this._eventMap||{};this._eventMap[m]||(this._eventMap[m]=new Map);this._eventMap[m].set(v,A);return x.apply(this,[y,A])};var r=f.removeEventListener;f.removeEventListener=function(y,v){if(y!==m||!this._eventMap||!this._eventMap[m]||!this._eventMap[m].has(v))return r.apply(this,arguments);var A=this._eventMap[m].get(v);this._eventMap[m].delete(v);0===this._eventMap[m].size&&delete this._eventMap[m];0===Object.keys(this._eventMap).length&&delete this._eventMap;
return r.apply(this,[y,A])};Object.defineProperty(f,"on"+m,{get:function(){return this["_on"+m]},set:function(y){this["_on"+m]&&(this.removeEventListener(m,this["_on"+m]),delete this["_on"+m]);y&&this.addEventListener(m,this["_on"+m]=y)},enumerable:!0,configurable:!0})}};c.disableLog=function(f){return"boolean"!==typeof f?Error("Argument type: "+("undefined"===typeof f?"undefined":h(f))+". Please use a boolean."):(q=f)?"adapter.js logging disabled":"adapter.js logging enabled"};c.disableWarnings=
function(f){if("boolean"!==typeof f)return Error("Argument type: "+("undefined"===typeof f?"undefined":h(f))+". Please use a boolean.");l=!f;return"adapter.js deprecation warnings "+(f?"disabled":"enabled")};c.log=function(){"object"!==("undefined"===typeof window?"undefined":h(window))||q||"undefined"!==typeof console&&"function"===typeof console.log&&console.log.apply(console,arguments)};c.deprecated=function(f,m){l&&console.warn(f+" is deprecated, please use "+m+" instead.")};c.detectBrowser=function(f){var m=
f.navigator,t={browser:null,version:null};if("undefined"===typeof f||!f.navigator)return t.browser="Not a browser.",t;m.mozGetUserMedia?(t.browser="firefox",t.version=e(m.userAgent,/Firefox\/(\d+)\./,1)):m.webkitGetUserMedia||!1===f.isSecureContext&&f.webkitRTCPeerConnection&&!f.RTCIceGatherer?(t.browser="chrome",t.version=e(m.userAgent,/Chrom(e|ium)\/(\d+)\./,2)):m.mediaDevices&&m.userAgent.match(/Edge\/(\d+).(\d+)$/)?(t.browser="edge",t.version=e(m.userAgent,/Edge\/(\d+).(\d+)$/,2)):f.RTCPeerConnection&&
m.userAgent.match(/AppleWebKit\/(\d+)\./)?(t.browser="safari",t.version=e(m.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=f.RTCRtpTransceiver&&"currentDirection"in f.RTCRtpTransceiver.prototype):t.browser="Not a supported browser.";return t};c.compactObject=d;c.walkStats=g;c.filterStats=function(f,m,t){var x=t?"outbound-rtp":"inbound-rtp",r=new Map;if(null===m)return r;var y=[];f.forEach(function(v){"track"===v.type&&v.trackIdentifier===m.id&&y.push(v)});y.forEach(function(v){f.forEach(function(A){A.type===
x&&A.trackId===v.id&&g(f,A,r)})});return r};var q=!0,l=!0},{}],16:[function(a,b,c){function e(m,t,x,r,y){t=f.writeRtpDescription(m.kind,t);t+=f.writeIceParameters(m.iceGatherer.getLocalParameters());t+=f.writeDtlsParameters(m.dtlsTransport.getLocalParameters(),"offer"===x?"actpass":y||"active");t+="a=mid:"+m.mid+"\r\n";t=m.rtpSender&&m.rtpReceiver?t+"a=sendrecv\r\n":m.rtpSender?t+"a=sendonly\r\n":m.rtpReceiver?t+"a=recvonly\r\n":t+"a=inactive\r\n";m.rtpSender&&(x=m.rtpSender._initialTrackId||m.rtpSender.track.id,
m.rtpSender._initialTrackId=x,r="msid:"+(r?r.id:"-")+" "+x+"\r\n",t=t+("a="+r)+("a=ssrc:"+m.sendEncodingParameters[0].ssrc+" "+r),m.sendEncodingParameters[0].rtx&&(t+="a=ssrc:"+m.sendEncodingParameters[0].rtx.ssrc+" "+r,t+="a=ssrc-group:FID "+m.sendEncodingParameters[0].ssrc+" "+m.sendEncodingParameters[0].rtx.ssrc+"\r\n"));t+="a=ssrc:"+m.sendEncodingParameters[0].ssrc+" cname:"+f.localCName+"\r\n";m.rtpSender&&m.sendEncodingParameters[0].rtx&&(t+="a=ssrc:"+m.sendEncodingParameters[0].rtx.ssrc+" cname:"+
f.localCName+"\r\n");return t}function d(m,t){var x=!1;m=JSON.parse(JSON.stringify(m));return m.filter(function(r){if(r&&(r.urls||r.url)){var y=r.urls||r.url;r.url&&!r.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var v="string"===typeof y;v&&(y=[y]);y=y.filter(function(A){return 0!==A.indexOf("turn:")||-1===A.indexOf("transport=udp")||-1!==A.indexOf("turn:[")||x?0===A.indexOf("stun:")&&14393<=t&&-1===A.indexOf("?transport=udp"):x=!0});delete r.url;r.urls=v?y[0]:y;return!!y.length}})}
function g(m,t){var x={codecs:[],headerExtensions:[],fecMechanisms:[]},r=function(v,A){v=parseInt(v,10);for(var n=0;n<A.length;n++)if(A[n].payloadType===v||A[n].preferredPayloadType===v)return A[n]},y=function(v,A,n,p){v=r(v.parameters.apt,n);A=r(A.parameters.apt,p);return v&&A&&v.name.toLowerCase()===A.name.toLowerCase()};m.codecs.forEach(function(v){for(var A=0;A<t.codecs.length;A++){var n=t.codecs[A];if(v.name.toLowerCase()===n.name.toLowerCase()&&v.clockRate===n.clockRate&&("rtx"!==v.name.toLowerCase()||
!v.parameters||!n.parameters.apt||y(v,n,m.codecs,t.codecs))){n=JSON.parse(JSON.stringify(n));n.numChannels=Math.min(v.numChannels,n.numChannels);x.codecs.push(n);n.rtcpFeedback=n.rtcpFeedback.filter(function(p){for(var w=0;w<v.rtcpFeedback.length;w++)if(v.rtcpFeedback[w].type===p.type&&v.rtcpFeedback[w].parameter===p.parameter)return!0;return!1});break}}});m.headerExtensions.forEach(function(v){for(var A=0;A<t.headerExtensions.length;A++){var n=t.headerExtensions[A];if(v.uri===n.uri){x.headerExtensions.push(n);
break}}});return x}function h(m,t,x){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][m].indexOf(x)}function q(m,t){var x=m.getRemoteCandidates().find(function(r){return t.foundation===r.foundation&&t.ip===r.ip&&t.port===r.port&&t.priority===r.priority&&t.protocol===r.protocol&&t.type===
r.type});x||m.addRemoteCandidate(t);return!x}function l(m,t){t=Error(t);t.name=m;t.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[m];return t}var f=a("sdp");b.exports=function(m,t){function x(n,p){p.addTrack(n);p.dispatchEvent(new m.MediaStreamTrackEvent("addtrack",{track:n}))}function r(n,p){p.removeTrack(n);p.dispatchEvent(new m.MediaStreamTrackEvent("removetrack",{track:n}))}function y(n,p,w,z){var C=new Event("track");C.track=p;C.receiver=
w;C.transceiver={receiver:w};C.streams=z;m.setTimeout(function(){n._dispatchEvent("track",C)})}var v=function(n){var p=this,w=document.createDocumentFragment();["addEventListener","removeEventListener","dispatchEvent"].forEach(function(C){p[C]=w[C].bind(w)});this.canTrickleIceCandidates=null;this.needNegotiation=!1;this.localStreams=[];this.remoteStreams=[];this._remoteDescription=this._localDescription=null;this.signalingState="stable";this.iceGatheringState=this.connectionState=this.iceConnectionState=
"new";n=JSON.parse(JSON.stringify(n||{}));this.usingBundle="max-bundle"===n.bundlePolicy;if("negotiate"===n.rtcpMuxPolicy)throw l("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");n.rtcpMuxPolicy||(n.rtcpMuxPolicy="require");switch(n.iceTransportPolicy){case "all":case "relay":break;default:n.iceTransportPolicy="all"}switch(n.bundlePolicy){case "balanced":case "max-compat":case "max-bundle":break;default:n.bundlePolicy="balanced"}n.iceServers=d(n.iceServers||[],t);this._iceGatherers=
[];if(n.iceCandidatePoolSize)for(var z=n.iceCandidatePoolSize;0<z;z--)this._iceGatherers.push(new m.RTCIceGatherer({iceServers:n.iceServers,gatherPolicy:n.iceTransportPolicy}));else n.iceCandidatePoolSize=0;this._config=n;this.transceivers=[];this._sdpSessionId=f.generateSessionId();this._sdpSessionVersion=0;this._dtlsRole=void 0;this._isClosed=!1};Object.defineProperty(v.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}});Object.defineProperty(v.prototype,
"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}});v.prototype.onicecandidate=null;v.prototype.onaddstream=null;v.prototype.ontrack=null;v.prototype.onremovestream=null;v.prototype.onsignalingstatechange=null;v.prototype.oniceconnectionstatechange=null;v.prototype.onconnectionstatechange=null;v.prototype.onicegatheringstatechange=null;v.prototype.onnegotiationneeded=null;v.prototype.ondatachannel=null;v.prototype._dispatchEvent=function(n,p){if(!this._isClosed&&
(this.dispatchEvent(p),"function"===typeof this["on"+n]))this["on"+n](p)};v.prototype._emitGatheringStateChange=function(){var n=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",n)};v.prototype.getConfiguration=function(){return this._config};v.prototype.getLocalStreams=function(){return this.localStreams};v.prototype.getRemoteStreams=function(){return this.remoteStreams};v.prototype._createTransceiver=function(n,p){var w=0<this.transceivers.length;n={track:null,
iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:n,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};this.usingBundle&&w?(n.iceTransport=this.transceivers[0].iceTransport,n.dtlsTransport=this.transceivers[0].dtlsTransport):(w=this._createIceAndDtlsTransports(),n.iceTransport=w.iceTransport,n.dtlsTransport=w.dtlsTransport);p||this.transceivers.push(n);
return n};v.prototype.addTrack=function(n,p){if(this._isClosed)throw l("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");if(this.transceivers.find(function(C){return C.track===n}))throw l("InvalidAccessError","Track already exists.");for(var w,z=0;z<this.transceivers.length;z++)this.transceivers[z].track||this.transceivers[z].kind!==n.kind||(w=this.transceivers[z]);w||(w=this._createTransceiver(n.kind));this._maybeFireNegotiationNeeded();-1===this.localStreams.indexOf(p)&&
this.localStreams.push(p);w.track=n;w.stream=p;w.rtpSender=new m.RTCRtpSender(n,w.dtlsTransport);return w.rtpSender};v.prototype.addStream=function(n){var p=this;if(15025<=t)n.getTracks().forEach(function(z){p.addTrack(z,n)});else{var w=n.clone();n.getTracks().forEach(function(z,C){var E=w.getTracks()[C];z.addEventListener("enabled",function(G){E.enabled=G.enabled})});w.getTracks().forEach(function(z){p.addTrack(z,w)})}};v.prototype.removeTrack=function(n){if(this._isClosed)throw l("InvalidStateError",
"Attempted to call removeTrack on a closed peerconnection.");if(!(n instanceof m.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var p=this.transceivers.find(function(z){return z.rtpSender===n});if(!p)throw l("InvalidAccessError","Sender was not created by this connection.");var w=p.stream;p.rtpSender.stop();p.rtpSender=null;p.track=null;p.stream=null;-1===this.transceivers.map(function(z){return z.stream}).indexOf(w)&&-1<
this.localStreams.indexOf(w)&&this.localStreams.splice(this.localStreams.indexOf(w),1);this._maybeFireNegotiationNeeded()};v.prototype.removeStream=function(n){var p=this;n.getTracks().forEach(function(w){var z=p.getSenders().find(function(C){return C.track===w});z&&p.removeTrack(z)})};v.prototype.getSenders=function(){return this.transceivers.filter(function(n){return!!n.rtpSender}).map(function(n){return n.rtpSender})};v.prototype.getReceivers=function(){return this.transceivers.filter(function(n){return!!n.rtpReceiver}).map(function(n){return n.rtpReceiver})};
v.prototype._createIceGatherer=function(n,p){var w=this;if(p&&0<n)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var z=new m.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});Object.defineProperty(z,"state",{value:"new",writable:!0});this.transceivers[n].bufferedCandidateEvents=[];this.transceivers[n].bufferCandidates=function(C){var E=!C.candidate||0===Object.keys(C.candidate).length;z.state=E?
"completed":"gathering";null!==w.transceivers[n].bufferedCandidateEvents&&w.transceivers[n].bufferedCandidateEvents.push(C)};z.addEventListener("localcandidate",this.transceivers[n].bufferCandidates);return z};v.prototype._gather=function(n,p){var w=this,z=this.transceivers[p].iceGatherer;if(!z.onlocalcandidate){var C=this.transceivers[p].bufferedCandidateEvents;this.transceivers[p].bufferedCandidateEvents=null;z.removeEventListener("localcandidate",this.transceivers[p].bufferCandidates);z.onlocalcandidate=
function(E){if(!(w.usingBundle&&0<p)){var G=new Event("icecandidate");G.candidate={sdpMid:n,sdpMLineIndex:p};var N=E.candidate;if(E=!N||0===Object.keys(N).length){if("new"===z.state||"gathering"===z.state)z.state="completed"}else"new"===z.state&&(z.state="gathering"),N.component=1,N.ufrag=z.getLocalParameters().usernameFragment,N=f.writeCandidate(N),G.candidate=Object.assign(G.candidate,f.parseCandidate(N)),G.candidate.candidate=N,G.candidate.toJSON=function(){return{candidate:G.candidate.candidate,
sdpMid:G.candidate.sdpMid,sdpMLineIndex:G.candidate.sdpMLineIndex,usernameFragment:G.candidate.usernameFragment}};N=f.getMediaSections(w._localDescription.sdp);N[G.candidate.sdpMLineIndex]=E?N[G.candidate.sdpMLineIndex]+"a=end-of-candidates\r\n":N[G.candidate.sdpMLineIndex]+("a="+G.candidate.candidate+"\r\n");w._localDescription.sdp=f.getDescription(w._localDescription.sdp)+N.join("");N=w.transceivers.every(function(Z){return Z.iceGatherer&&"completed"===Z.iceGatherer.state});"gathering"!==w.iceGatheringState&&
(w.iceGatheringState="gathering",w._emitGatheringStateChange());E||w._dispatchEvent("icecandidate",G);N&&(w._dispatchEvent("icecandidate",new Event("icecandidate")),w.iceGatheringState="complete",w._emitGatheringStateChange())}};m.setTimeout(function(){C.forEach(function(E){z.onlocalcandidate(E)})},0)}};v.prototype._createIceAndDtlsTransports=function(){var n=this,p=new m.RTCIceTransport(null);p.onicestatechange=function(){n._updateIceConnectionState();n._updateConnectionState()};var w=new m.RTCDtlsTransport(p);
w.ondtlsstatechange=function(){n._updateConnectionState()};w.onerror=function(){Object.defineProperty(w,"state",{value:"failed",writable:!0});n._updateConnectionState()};return{iceTransport:p,dtlsTransport:w}};v.prototype._disposeIceAndDtlsTransports=function(n){var p=this.transceivers[n].iceGatherer;p&&(delete p.onlocalcandidate,delete this.transceivers[n].iceGatherer);if(p=this.transceivers[n].iceTransport)delete p.onicestatechange,delete this.transceivers[n].iceTransport;if(p=this.transceivers[n].dtlsTransport)delete p.ondtlsstatechange,
delete p.onerror,delete this.transceivers[n].dtlsTransport};v.prototype._transceive=function(n,p,w){var z=g(n.localCapabilities,n.remoteCapabilities);p&&n.rtpSender&&(z.encodings=n.sendEncodingParameters,z.rtcp={cname:f.localCName,compound:n.rtcpParameters.compound},n.recvEncodingParameters.length&&(z.rtcp.ssrc=n.recvEncodingParameters[0].ssrc),n.rtpSender.send(z));w&&n.rtpReceiver&&0<z.codecs.length&&("video"===n.kind&&n.recvEncodingParameters&&15019>t&&n.recvEncodingParameters.forEach(function(C){delete C.rtx}),
z.encodings=n.recvEncodingParameters.length?n.recvEncodingParameters:[{}],z.rtcp={compound:n.rtcpParameters.compound},n.rtcpParameters.cname&&(z.rtcp.cname=n.rtcpParameters.cname),n.sendEncodingParameters.length&&(z.rtcp.ssrc=n.sendEncodingParameters[0].ssrc),n.rtpReceiver.receive(z))};v.prototype.setLocalDescription=function(n){var p=this;if(-1===["offer","answer"].indexOf(n.type))return Promise.reject(l("TypeError",'Unsupported type "'+n.type+'"'));if(!h("setLocalDescription",n.type,p.signalingState)||
p._isClosed)return Promise.reject(l("InvalidStateError","Can not set local "+n.type+" in state "+p.signalingState));if("offer"===n.type){var w=f.splitSections(n.sdp);var z=w.shift();w.forEach(function(E,G){E=f.parseRtpParameters(E);p.transceivers[G].localCapabilities=E});p.transceivers.forEach(function(E,G){p._gather(E.mid,G)})}else if("answer"===n.type){w=f.splitSections(p._remoteDescription.sdp);z=w.shift();var C=0<f.matchPrefix(z,"a=ice-lite").length;w.forEach(function(E,G){var N=p.transceivers[G],
Z=N.iceGatherer,L=N.iceTransport,J=N.dtlsTransport,Q=N.localCapabilities,T=N.remoteCapabilities;if(!(f.isRejected(E)&&0===f.matchPrefix(E,"a=bundle-only").length||N.rejected)){var aa=f.getIceParameters(E,z);E=f.getDtlsParameters(E,z);C&&(E.role="server");p.usingBundle&&0!==G||(p._gather(N.mid,G),"new"===L.state&&L.start(Z,aa,C?"controlling":"controlled"),"new"===J.state&&J.start(E));G=g(Q,T);p._transceive(N,0<G.codecs.length,!1)}})}p._localDescription={type:n.type,sdp:n.sdp};"offer"===n.type?p._updateSignalingState("have-local-offer"):
p._updateSignalingState("stable");return Promise.resolve()};v.prototype.setRemoteDescription=function(n){var p=this;if(-1===["offer","answer"].indexOf(n.type))return Promise.reject(l("TypeError",'Unsupported type "'+n.type+'"'));if(!h("setRemoteDescription",n.type,p.signalingState)||p._isClosed)return Promise.reject(l("InvalidStateError","Can not set remote "+n.type+" in state "+p.signalingState));var w={};p.remoteStreams.forEach(function(L){w[L.id]=L});var z=[],C=f.splitSections(n.sdp),E=C.shift(),
G=0<f.matchPrefix(E,"a=ice-lite").length,N=0<f.matchPrefix(E,"a=group:BUNDLE ").length;p.usingBundle=N;var Z=f.matchPrefix(E,"a=ice-options:")[0];p.canTrickleIceCandidates=Z?0<=Z.substr(14).split(" ").indexOf("trickle"):!1;C.forEach(function(L,J){var Q=f.splitLines(L),T=f.getKind(L),aa=f.isRejected(L)&&0===f.matchPrefix(L,"a=bundle-only").length,ma=Q[0].substr(2).split(" ")[2];Q=f.getDirection(L,E);var X=f.parseMsid(L),oa=f.getMid(L)||f.generateIdentifier();if(aa||"application"===T&&("DTLS/SCTP"===
ma||"UDP/DTLS/SCTP"===ma))p.transceivers[J]={mid:oa,kind:T,protocol:ma,rejected:!0};else{!aa&&p.transceivers[J]&&p.transceivers[J].rejected&&(p.transceivers[J]=p._createTransceiver(T,!0));var Ga=f.parseRtpParameters(L);if(!aa){var pa=f.getIceParameters(L,E);var Ha=f.getDtlsParameters(L,E);Ha.role="client"}ma=f.parseRtpEncodingParameters(L);var Ia=f.parseRtcpParameters(L),Ja=0<f.matchPrefix(L,"a=end-of-candidates",E).length,na=f.matchPrefix(L,"a=candidate:").map(function(Y){return f.parseCandidate(Y)}).filter(function(Y){return 1===
Y.component});("offer"===n.type||"answer"===n.type)&&!aa&&N&&0<J&&p.transceivers[J]&&(p._disposeIceAndDtlsTransports(J),p.transceivers[J].iceGatherer=p.transceivers[0].iceGatherer,p.transceivers[J].iceTransport=p.transceivers[0].iceTransport,p.transceivers[J].dtlsTransport=p.transceivers[0].dtlsTransport,p.transceivers[J].rtpSender&&p.transceivers[J].rtpSender.setTransport(p.transceivers[0].dtlsTransport),p.transceivers[J].rtpReceiver&&p.transceivers[J].rtpReceiver.setTransport(p.transceivers[0].dtlsTransport));
if("offer"!==n.type||aa)"answer"!==n.type||aa||(O=p.transceivers[J],ka=O.iceGatherer,T=O.iceTransport,oa=O.dtlsTransport,ha=O.rtpReceiver,aa=O.sendEncodingParameters,L=O.localCapabilities,p.transceivers[J].recvEncodingParameters=ma,p.transceivers[J].remoteCapabilities=Ga,p.transceivers[J].rtcpParameters=Ia,na.length&&"new"===T.state&&(!G&&!Ja||N&&0!==J?na.forEach(function(Y){q(O.iceTransport,Y)}):T.setRemoteCandidates(na)),N&&0!==J||("new"===T.state&&T.start(ka,pa,"controlling"),"new"===oa.state&&
oa.start(Ha)),!g(O.localCapabilities,O.remoteCapabilities).codecs.filter(function(Y){return"rtx"===Y.name.toLowerCase()}).length&&O.sendEncodingParameters[0].rtx&&delete O.sendEncodingParameters[0].rtx,p._transceive(O,"sendrecv"===Q||"recvonly"===Q,"sendrecv"===Q||"sendonly"===Q),!ha||"sendrecv"!==Q&&"sendonly"!==Q?delete O.rtpReceiver:(Q=ha.track,X?(w[X.stream]||(w[X.stream]=new m.MediaStream),x(Q,w[X.stream]),z.push([Q,ha,w[X.stream]])):(w.default||(w.default=new m.MediaStream),x(Q,w.default),z.push([Q,
ha,w.default]))));else{var O=p.transceivers[J]||p._createTransceiver(T);O.mid=oa;O.iceGatherer||(O.iceGatherer=p._createIceGatherer(J,N));na.length&&"new"===O.iceTransport.state&&(!Ja||N&&0!==J?na.forEach(function(Y){q(O.iceTransport,Y)}):O.iceTransport.setRemoteCandidates(na));L=m.RTCRtpReceiver.getCapabilities(T);15019>t&&(L.codecs=L.codecs.filter(function(Y){return"rtx"!==Y.name}));aa=O.sendEncodingParameters||[{ssrc:1001*(2*J+2)}];pa=!1;if("sendrecv"===Q||"sendonly"===Q){pa=!O.rtpReceiver;var ha=
O.rtpReceiver||new m.RTCRtpReceiver(O.dtlsTransport,T);if(pa){Q=ha.track;if(!X||"-"!==X.stream)if(X){w[X.stream]||(w[X.stream]=new m.MediaStream,Object.defineProperty(w[X.stream],"id",{get:function(){return X.stream}}));Object.defineProperty(Q,"id",{get:function(){return X.track}});var ka=w[X.stream]}else w.default||(w.default=new m.MediaStream),ka=w.default;ka&&(x(Q,ka),O.associatedRemoteMediaStreams.push(ka));z.push([Q,ha,ka])}}else O.rtpReceiver&&O.rtpReceiver.track&&(O.associatedRemoteMediaStreams.forEach(function(Y){var Ka=
Y.getTracks().find(function(rc){return rc.id===O.rtpReceiver.track.id});Ka&&r(Ka,Y)}),O.associatedRemoteMediaStreams=[]);O.localCapabilities=L;O.remoteCapabilities=Ga;O.rtpReceiver=ha;O.rtcpParameters=Ia;O.sendEncodingParameters=aa;O.recvEncodingParameters=ma;p._transceive(p.transceivers[J],!1,pa)}}});void 0===p._dtlsRole&&(p._dtlsRole="offer"===n.type?"active":"passive");p._remoteDescription={type:n.type,sdp:n.sdp};"offer"===n.type?p._updateSignalingState("have-remote-offer"):p._updateSignalingState("stable");
Object.keys(w).forEach(function(L){var J=w[L];if(J.getTracks().length){if(-1===p.remoteStreams.indexOf(J)){p.remoteStreams.push(J);var Q=new Event("addstream");Q.stream=J;m.setTimeout(function(){p._dispatchEvent("addstream",Q)})}z.forEach(function(T){J.id===T[2].id&&y(p,T[0],T[1],[J])})}});z.forEach(function(L){L[2]||y(p,L[0],L[1],[])});m.setTimeout(function(){p&&p.transceivers&&p.transceivers.forEach(function(L){L.iceTransport&&"new"===L.iceTransport.state&&0<L.iceTransport.getRemoteCandidates().length&&
(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),L.iceTransport.addRemoteCandidate({}))})},4E3);return Promise.resolve()};v.prototype.close=function(){this.transceivers.forEach(function(n){n.iceTransport&&n.iceTransport.stop();n.dtlsTransport&&n.dtlsTransport.stop();n.rtpSender&&n.rtpSender.stop();n.rtpReceiver&&n.rtpReceiver.stop()});this._isClosed=!0;this._updateSignalingState("closed")};v.prototype._updateSignalingState=function(n){this.signalingState=
n;n=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",n)};v.prototype._maybeFireNegotiationNeeded=function(){var n=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,m.setTimeout(function(){if(n.needNegotiation){n.needNegotiation=!1;var p=new Event("negotiationneeded");n._dispatchEvent("negotiationneeded",p)}},0))};v.prototype._updateIceConnectionState=function(){var n={"new":0,closed:0,checking:0,connected:0,completed:0,disconnected:0,
failed:0};this.transceivers.forEach(function(w){w.iceTransport&&!w.rejected&&n[w.iceTransport.state]++});var p="new";0<n.failed?p="failed":0<n.checking?p="checking":0<n.disconnected?p="disconnected":0<n.new?p="new":0<n.connected?p="connected":0<n.completed&&(p="completed");p!==this.iceConnectionState&&(this.iceConnectionState=p,p=new Event("iceconnectionstatechange"),this._dispatchEvent("iceconnectionstatechange",p))};v.prototype._updateConnectionState=function(){var n={"new":0,closed:0,connecting:0,
connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(w){w.iceTransport&&w.dtlsTransport&&!w.rejected&&(n[w.iceTransport.state]++,n[w.dtlsTransport.state]++)});n.connected+=n.completed;var p="new";0<n.failed?p="failed":0<n.connecting?p="connecting":0<n.disconnected?p="disconnected":0<n.new?p="new":0<n.connected&&(p="connected");p!==this.connectionState&&(this.connectionState=p,p=new Event("connectionstatechange"),this._dispatchEvent("connectionstatechange",p))};v.prototype.createOffer=
function(n){var p=this;if(p._isClosed)return Promise.reject(l("InvalidStateError","Can not call createOffer after close"));var w=p.transceivers.filter(function(E){return"audio"===E.kind}).length,z=p.transceivers.filter(function(E){return"video"===E.kind}).length;if(n){if(n.mandatory||n.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==n.offerToReceiveAudio&&(w=!0===n.offerToReceiveAudio?1:!1===n.offerToReceiveAudio?0:n.offerToReceiveAudio);void 0!==n.offerToReceiveVideo&&
(z=!0===n.offerToReceiveVideo?1:!1===n.offerToReceiveVideo?0:n.offerToReceiveVideo)}for(p.transceivers.forEach(function(E){"audio"===E.kind?(w--,0>w&&(E.wantReceive=!1)):"video"===E.kind&&(z--,0>z&&(E.wantReceive=!1))});0<w||0<z;)0<w&&(p._createTransceiver("audio"),w--),0<z&&(p._createTransceiver("video"),z--);var C=f.writeSessionBoilerplate(p._sdpSessionId,p._sdpSessionVersion++);p.transceivers.forEach(function(E,G){var N=E.track,Z=E.kind,L=E.mid||f.generateIdentifier();E.mid=L;E.iceGatherer||(E.iceGatherer=
p._createIceGatherer(G,p.usingBundle));L=m.RTCRtpSender.getCapabilities(Z);15019>t&&(L.codecs=L.codecs.filter(function(J){return"rtx"!==J.name}));L.codecs.forEach(function(J){"H264"===J.name&&void 0===J.parameters["level-asymmetry-allowed"]&&(J.parameters["level-asymmetry-allowed"]="1");E.remoteCapabilities&&E.remoteCapabilities.codecs&&E.remoteCapabilities.codecs.forEach(function(Q){J.name.toLowerCase()===Q.name.toLowerCase()&&J.clockRate===Q.clockRate&&(J.preferredPayloadType=Q.payloadType)})});
L.headerExtensions.forEach(function(J){(E.remoteCapabilities&&E.remoteCapabilities.headerExtensions||[]).forEach(function(Q){J.uri===Q.uri&&(J.id=Q.id)})});G=E.sendEncodingParameters||[{ssrc:1001*(2*G+1)}];N&&15019<=t&&"video"===Z&&!G[0].rtx&&(G[0].rtx={ssrc:G[0].ssrc+1});E.wantReceive&&(E.rtpReceiver=new m.RTCRtpReceiver(E.dtlsTransport,Z));E.localCapabilities=L;E.sendEncodingParameters=G});"max-compat"!==p._config.bundlePolicy&&(C+="a=group:BUNDLE "+p.transceivers.map(function(E){return E.mid}).join(" ")+
"\r\n");C+="a=ice-options:trickle\r\n";p.transceivers.forEach(function(E,G){C+=e(E,E.localCapabilities,"offer",E.stream,p._dtlsRole);C+="a=rtcp-rsize\r\n";!E.iceGatherer||"new"===p.iceGatheringState||0!==G&&p.usingBundle||(E.iceGatherer.getLocalCandidates().forEach(function(N){N.component=1;C+="a="+f.writeCandidate(N)+"\r\n"}),"completed"===E.iceGatherer.state&&(C+="a=end-of-candidates\r\n"))});n=new m.RTCSessionDescription({type:"offer",sdp:C});return Promise.resolve(n)};v.prototype.createAnswer=
function(){var n=this;if(n._isClosed)return Promise.reject(l("InvalidStateError","Can not call createAnswer after close"));if("have-remote-offer"!==n.signalingState&&"have-local-pranswer"!==n.signalingState)return Promise.reject(l("InvalidStateError","Can not call createAnswer in signalingState "+n.signalingState));var p=f.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++);n.usingBundle&&(p+="a=group:BUNDLE "+n.transceivers.map(function(C){return C.mid}).join(" ")+"\r\n");p+="a=ice-options:trickle\r\n";
var w=f.getMediaSections(n._remoteDescription.sdp).length;n.transceivers.forEach(function(C,E){if(!(E+1>w))if(C.rejected)"application"===C.kind?p="DTLS/SCTP"===C.protocol?p+"m=application 0 DTLS/SCTP 5000\r\n":p+("m=application 0 "+C.protocol+" webrtc-datachannel\r\n"):"audio"===C.kind?p+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===C.kind&&(p+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),p+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+C.mid+"\r\n";else{if(C.stream){var G;
"audio"===C.kind?G=C.stream.getAudioTracks()[0]:"video"===C.kind&&(G=C.stream.getVideoTracks()[0]);G&&15019<=t&&"video"===C.kind&&!C.sendEncodingParameters[0].rtx&&(C.sendEncodingParameters[0].rtx={ssrc:C.sendEncodingParameters[0].ssrc+1})}E=g(C.localCapabilities,C.remoteCapabilities);!E.codecs.filter(function(N){return"rtx"===N.name.toLowerCase()}).length&&C.sendEncodingParameters[0].rtx&&delete C.sendEncodingParameters[0].rtx;p+=e(C,E,"answer",C.stream,n._dtlsRole);C.rtcpParameters&&C.rtcpParameters.reducedSize&&
(p+="a=rtcp-rsize\r\n")}});var z=new m.RTCSessionDescription({type:"answer",sdp:p});return Promise.resolve(z)};v.prototype.addIceCandidate=function(n){var p=this,w;return n&&void 0===n.sdpMLineIndex&&!n.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(z,C){if(p._remoteDescription)if(n&&""!==n.candidate){var E=n.sdpMLineIndex;if(n.sdpMid)for(var G=0;G<p.transceivers.length;G++)if(p.transceivers[G].mid===n.sdpMid){E=G;break}var N=p.transceivers[E];if(N){if(N.rejected)return z();
G=0<Object.keys(n.candidate).length?f.parseCandidate(n.candidate):{};if("tcp"===G.protocol&&(0===G.port||9===G.port)||G.component&&1!==G.component)return z();if((0===E||0<E&&N.iceTransport!==p.transceivers[0].iceTransport)&&!q(N.iceTransport,G))return C(l("OperationError","Can not add ICE candidate"));C=n.candidate.trim();0===C.indexOf("a=")&&(C=C.substr(2));w=f.getMediaSections(p._remoteDescription.sdp);w[E]+="a="+(G.type?C:"end-of-candidates")+"\r\n";p._remoteDescription.sdp=f.getDescription(p._remoteDescription.sdp)+
w.join("")}else return C(l("OperationError","Can not add ICE candidate"))}else for(E=0;E<p.transceivers.length&&(p.transceivers[E].rejected||(p.transceivers[E].iceTransport.addRemoteCandidate({}),w=f.getMediaSections(p._remoteDescription.sdp),w[E]+="a=end-of-candidates\r\n",p._remoteDescription.sdp=f.getDescription(p._remoteDescription.sdp)+w.join(""),!p.usingBundle));E++);else return C(l("InvalidStateError","Can not add ICE candidate without a remote description"));z()})};v.prototype.getStats=function(n){if(n&&
n instanceof m.MediaStreamTrack){var p=null;this.transceivers.forEach(function(z){z.rtpSender&&z.rtpSender.track===n?p=z.rtpSender:z.rtpReceiver&&z.rtpReceiver.track===n&&(p=z.rtpReceiver)});if(!p)throw l("InvalidAccessError","Invalid selector.");return p.getStats()}var w=[];this.transceivers.forEach(function(z){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(C){z[C]&&w.push(z[C].getStats())})});return Promise.all(w).then(function(z){var C=new Map;z.forEach(function(E){E.forEach(function(G){C.set(G.id,
G)})});return C})};["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach(function(n){if((n=m[n])&&n.prototype&&n.prototype.getStats){var p=n.prototype.getStats;n.prototype.getStats=function(){return p.apply(this).then(function(w){var z=new Map;Object.keys(w).forEach(function(C){var E=w[C];w[C].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[E.type]||
E.type;z.set(C,w[C])});return z})}}});var A=["createOffer","createAnswer"];A.forEach(function(n){var p=v.prototype[n];v.prototype[n]=function(){var w=arguments;return"function"===typeof w[0]||"function"===typeof w[1]?p.apply(this,[arguments[2]]).then(function(z){"function"===typeof w[0]&&w[0].apply(null,[z])},function(z){"function"===typeof w[1]&&w[1].apply(null,[z])}):p.apply(this,arguments)}});A=["setLocalDescription","setRemoteDescription","addIceCandidate"];A.forEach(function(n){var p=v.prototype[n];
v.prototype[n]=function(){var w=arguments;return"function"===typeof w[1]||"function"===typeof w[2]?p.apply(this,arguments).then(function(){"function"===typeof w[1]&&w[1].apply(null)},function(z){"function"===typeof w[2]&&w[2].apply(null,[z])}):p.apply(this,arguments)}});["getStats"].forEach(function(n){var p=v.prototype[n];v.prototype[n]=function(){var w=arguments;return"function"===typeof w[1]?p.apply(this,arguments).then(function(){"function"===typeof w[1]&&w[1].apply(null)}):p.apply(this,arguments)}});
return v}},{sdp:17}],17:[function(a,b,c){var e={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};e.localCName=e.generateIdentifier();e.splitLines=function(d){return d.trim().split("\n").map(function(g){return g.trim()})};e.splitSections=function(d){return d.split("\nm=").map(function(g,h){return(0<h?"m="+g:g).trim()+"\r\n"})};e.getDescription=function(d){return(d=e.splitSections(d))&&d[0]};e.getMediaSections=function(d){d=e.splitSections(d);d.shift();return d};e.matchPrefix=
function(d,g){return e.splitLines(d).filter(function(h){return 0===h.indexOf(g)})};e.parseCandidate=function(d){d=0===d.indexOf("a=candidate:")?d.substring(12).split(" "):d.substring(10).split(" ");for(var g={foundation:d[0],component:parseInt(d[1],10),protocol:d[2].toLowerCase(),priority:parseInt(d[3],10),ip:d[4],address:d[4],port:parseInt(d[5],10),type:d[7]},h=8;h<d.length;h+=2)switch(d[h]){case "raddr":g.relatedAddress=d[h+1];break;case "rport":g.relatedPort=parseInt(d[h+1],10);break;case "tcptype":g.tcpType=
d[h+1];break;case "ufrag":g.ufrag=d[h+1];g.usernameFragment=d[h+1];break;default:g[d[h]]=d[h+1]}return g};e.writeCandidate=function(d){var g=[];g.push(d.foundation);g.push(d.component);g.push(d.protocol.toUpperCase());g.push(d.priority);g.push(d.address||d.ip);g.push(d.port);var h=d.type;g.push("typ");g.push(h);"host"!==h&&d.relatedAddress&&d.relatedPort&&(g.push("raddr"),g.push(d.relatedAddress),g.push("rport"),g.push(d.relatedPort));d.tcpType&&"tcp"===d.protocol.toLowerCase()&&(g.push("tcptype"),
g.push(d.tcpType));if(d.usernameFragment||d.ufrag)g.push("ufrag"),g.push(d.usernameFragment||d.ufrag);return"candidate:"+g.join(" ")};e.parseIceOptions=function(d){return d.substr(14).split(" ")};e.parseRtpMap=function(d){d=d.substr(9).split(" ");var g={payloadType:parseInt(d.shift(),10)};d=d[0].split("/");g.name=d[0];g.clockRate=parseInt(d[1],10);g.channels=3===d.length?parseInt(d[2],10):1;g.numChannels=g.channels;return g};e.writeRtpMap=function(d){var g=d.payloadType;void 0!==d.preferredPayloadType&&
(g=d.preferredPayloadType);var h=d.channels||d.numChannels||1;return"a=rtpmap:"+g+" "+d.name+"/"+d.clockRate+(1!==h?"/"+h:"")+"\r\n"};e.parseExtmap=function(d){d=d.substr(9).split(" ");return{id:parseInt(d[0],10),direction:0<d[0].indexOf("/")?d[0].split("/")[1]:"sendrecv",uri:d[1]}};e.writeExtmap=function(d){return"a=extmap:"+(d.id||d.preferredId)+(d.direction&&"sendrecv"!==d.direction?"/"+d.direction:"")+" "+d.uri+"\r\n"};e.parseFmtp=function(d){for(var g={},h=d.substr(d.indexOf(" ")+1).split(";"),
q=0;q<h.length;q++)d=h[q].trim().split("="),g[d[0].trim()]=d[1];return g};e.writeFmtp=function(d){var g="",h=d.payloadType;void 0!==d.preferredPayloadType&&(h=d.preferredPayloadType);if(d.parameters&&Object.keys(d.parameters).length){var q=[];Object.keys(d.parameters).forEach(function(l){d.parameters[l]?q.push(l+"="+d.parameters[l]):q.push(l)});g+="a=fmtp:"+h+" "+q.join(";")+"\r\n"}return g};e.parseRtcpFb=function(d){d=d.substr(d.indexOf(" ")+1).split(" ");return{type:d.shift(),parameter:d.join(" ")}};
e.writeRtcpFb=function(d){var g="",h=d.payloadType;void 0!==d.preferredPayloadType&&(h=d.preferredPayloadType);d.rtcpFeedback&&d.rtcpFeedback.length&&d.rtcpFeedback.forEach(function(q){g+="a=rtcp-fb:"+h+" "+q.type+(q.parameter&&q.parameter.length?" "+q.parameter:"")+"\r\n"});return g};e.parseSsrcMedia=function(d){var g=d.indexOf(" "),h={ssrc:parseInt(d.substr(7,g-7),10)},q=d.indexOf(":",g);-1<q?(h.attribute=d.substr(g+1,q-g-1),h.value=d.substr(q+1)):h.attribute=d.substr(g+1);return h};e.parseSsrcGroup=
function(d){d=d.substr(13).split(" ");return{semantics:d.shift(),ssrcs:d.map(function(g){return parseInt(g,10)})}};e.getMid=function(d){if(d=e.matchPrefix(d,"a=mid:")[0])return d.substr(6)};e.parseFingerprint=function(d){d=d.substr(14).split(" ");return{algorithm:d[0].toLowerCase(),value:d[1]}};e.getDtlsParameters=function(d,g){return{role:"auto",fingerprints:e.matchPrefix(d+g,"a=fingerprint:").map(e.parseFingerprint)}};e.writeDtlsParameters=function(d,g){var h="a=setup:"+g+"\r\n";d.fingerprints.forEach(function(q){h+=
"a=fingerprint:"+q.algorithm+" "+q.value+"\r\n"});return h};e.parseCryptoLine=function(d){d=d.substr(9).split(" ");return{tag:parseInt(d[0],10),cryptoSuite:d[1],keyParams:d[2],sessionParams:d.slice(3)}};e.writeCryptoLine=function(d){return"a=crypto:"+d.tag+" "+d.cryptoSuite+" "+("object"===typeof d.keyParams?e.writeCryptoKeyParams(d.keyParams):d.keyParams)+(d.sessionParams?" "+d.sessionParams.join(" "):"")+"\r\n"};e.parseCryptoKeyParams=function(d){if(0!==d.indexOf("inline:"))return null;d=d.substr(7).split("|");
return{keyMethod:"inline",keySalt:d[0],lifeTime:d[1],mkiValue:d[2]?d[2].split(":")[0]:void 0,mkiLength:d[2]?d[2].split(":")[1]:void 0}};e.writeCryptoKeyParams=function(d){return d.keyMethod+":"+d.keySalt+(d.lifeTime?"|"+d.lifeTime:"")+(d.mkiValue&&d.mkiLength?"|"+d.mkiValue+":"+d.mkiLength:"")};e.getCryptoParameters=function(d,g){return e.matchPrefix(d+g,"a=crypto:").map(e.parseCryptoLine)};e.getIceParameters=function(d,g){var h=e.matchPrefix(d+g,"a=ice-ufrag:")[0];d=e.matchPrefix(d+g,"a=ice-pwd:")[0];
return h&&d?{usernameFragment:h.substr(12),password:d.substr(10)}:null};e.writeIceParameters=function(d){return"a=ice-ufrag:"+d.usernameFragment+"\r\na=ice-pwd:"+d.password+"\r\n"};e.parseRtpParameters=function(d){for(var g={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},h=e.splitLines(d)[0].split(" "),q=3;q<h.length;q++){var l=h[q],f=e.matchPrefix(d,"a=rtpmap:"+l+" ")[0];if(f){f=e.parseRtpMap(f);var m=e.matchPrefix(d,"a=fmtp:"+l+" ");f.parameters=m.length?e.parseFmtp(m[0]):{};f.rtcpFeedback=
e.matchPrefix(d,"a=rtcp-fb:"+l+" ").map(e.parseRtcpFb);g.codecs.push(f);switch(f.name.toUpperCase()){case "RED":case "ULPFEC":g.fecMechanisms.push(f.name.toUpperCase())}}}e.matchPrefix(d,"a=extmap:").forEach(function(t){g.headerExtensions.push(e.parseExtmap(t))});return g};e.writeRtpDescription=function(d,g){var h="";h+="m="+d+" ";h+=0<g.codecs.length?"9":"0";h+=" UDP/TLS/RTP/SAVPF ";h+=g.codecs.map(function(l){return void 0!==l.preferredPayloadType?l.preferredPayloadType:l.payloadType}).join(" ")+
"\r\n";h+="c=IN IP4 0.0.0.0\r\n";h+="a=rtcp:9 IN IP4 0.0.0.0\r\n";g.codecs.forEach(function(l){h+=e.writeRtpMap(l);h+=e.writeFmtp(l);h+=e.writeRtcpFb(l)});var q=0;g.codecs.forEach(function(l){l.maxptime>q&&(q=l.maxptime)});0<q&&(h+="a=maxptime:"+q+"\r\n");h+="a=rtcp-mux\r\n";g.headerExtensions&&g.headerExtensions.forEach(function(l){h+=e.writeExtmap(l)});return h};e.parseRtpEncodingParameters=function(d){var g=[],h=e.parseRtpParameters(d),q=-1!==h.fecMechanisms.indexOf("RED"),l=-1!==h.fecMechanisms.indexOf("ULPFEC"),
f=e.matchPrefix(d,"a=ssrc:").map(function(r){return e.parseSsrcMedia(r)}).filter(function(r){return"cname"===r.attribute}),m=0<f.length&&f[0].ssrc,t;f=e.matchPrefix(d,"a=ssrc-group:FID").map(function(r){return r.substr(17).split(" ").map(function(y){return parseInt(y,10)})});0<f.length&&1<f[0].length&&f[0][0]===m&&(t=f[0][1]);h.codecs.forEach(function(r){"RTX"===r.name.toUpperCase()&&r.parameters.apt&&(r={ssrc:m,codecPayloadType:parseInt(r.parameters.apt,10)},m&&t&&(r.rtx={ssrc:t}),g.push(r),q&&(r=
JSON.parse(JSON.stringify(r)),r.fec={ssrc:m,mechanism:l?"red+ulpfec":"red"},g.push(r)))});0===g.length&&m&&g.push({ssrc:m});var x=e.matchPrefix(d,"b=");x.length&&(x=0===x[0].indexOf("b=TIAS:")?parseInt(x[0].substr(7),10):0===x[0].indexOf("b=AS:")?950*parseInt(x[0].substr(5),10)-16E3:void 0,g.forEach(function(r){r.maxBitrate=x}));return g};e.parseRtcpParameters=function(d){var g={},h=e.matchPrefix(d,"a=ssrc:").map(function(q){return e.parseSsrcMedia(q)}).filter(function(q){return"cname"===q.attribute})[0];
h&&(g.cname=h.value,g.ssrc=h.ssrc);h=e.matchPrefix(d,"a=rtcp-rsize");g.reducedSize=0<h.length;g.compound=0===h.length;d=e.matchPrefix(d,"a=rtcp-mux");g.mux=0<d.length;return g};e.parseMsid=function(d){var g=e.matchPrefix(d,"a=msid:");if(1===g.length)return d=g[0].substr(7).split(" "),{stream:d[0],track:d[1]};d=e.matchPrefix(d,"a=ssrc:").map(function(h){return e.parseSsrcMedia(h)}).filter(function(h){return"msid"===h.attribute});if(0<d.length)return d=d[0].value.split(" "),{stream:d[0],track:d[1]}};
e.parseSctpDescription=function(d){var g=e.parseMLine(d),h=e.matchPrefix(d,"a=max-message-size:"),q;0<h.length&&(q=parseInt(h[0].substr(19),10));isNaN(q)&&(q=65536);h=e.matchPrefix(d,"a=sctp-port:");if(0<h.length)return{port:parseInt(h[0].substr(12),10),protocol:g.fmt,maxMessageSize:q};if(0<e.matchPrefix(d,"a=sctpmap:").length)return d=e.matchPrefix(d,"a=sctpmap:")[0].substr(10).split(" "),{port:parseInt(d[0],10),protocol:d[1],maxMessageSize:q}};e.writeSctpDescription=function(d,g){d="DTLS/SCTP"!==
d.protocol?["m="+d.kind+" 9 "+d.protocol+" "+g.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+g.port+"\r\n"]:["m="+d.kind+" 9 "+d.protocol+" "+g.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+g.port+" "+g.protocol+" 65535\r\n"];void 0!==g.maxMessageSize&&d.push("a=max-message-size:"+g.maxMessageSize+"\r\n");return d.join("")};e.generateSessionId=function(){return Math.random().toString().substr(2,21)};e.writeSessionBoilerplate=function(d,g,h){g=void 0!==g?g:2;d=d?d:e.generateSessionId();return"v=0\r\no="+
(h||"thisisadapterortc")+" "+d+" "+g+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"};e.writeMediaSection=function(d,g,h,q){g=e.writeRtpDescription(d.kind,g);g+=e.writeIceParameters(d.iceGatherer.getLocalParameters());g+=e.writeDtlsParameters(d.dtlsTransport.getLocalParameters(),"offer"===h?"actpass":"active");g+="a=mid:"+d.mid+"\r\n";g=d.direction?g+("a="+d.direction+"\r\n"):d.rtpSender&&d.rtpReceiver?g+"a=sendrecv\r\n":d.rtpSender?g+"a=sendonly\r\n":d.rtpReceiver?g+"a=recvonly\r\n":g+"a=inactive\r\n";d.rtpSender&&
(h="msid:"+q.id+" "+d.rtpSender.track.id+"\r\n",g=g+("a="+h)+("a=ssrc:"+d.sendEncodingParameters[0].ssrc+" "+h),d.sendEncodingParameters[0].rtx&&(g+="a=ssrc:"+d.sendEncodingParameters[0].rtx.ssrc+" "+h,g+="a=ssrc-group:FID "+d.sendEncodingParameters[0].ssrc+" "+d.sendEncodingParameters[0].rtx.ssrc+"\r\n"));g+="a=ssrc:"+d.sendEncodingParameters[0].ssrc+" cname:"+e.localCName+"\r\n";d.rtpSender&&d.sendEncodingParameters[0].rtx&&(g+="a=ssrc:"+d.sendEncodingParameters[0].rtx.ssrc+" cname:"+e.localCName+
"\r\n");return g};e.getDirection=function(d,g){d=e.splitLines(d);for(var h=0;h<d.length;h++)switch(d[h]){case "a=sendrecv":case "a=sendonly":case "a=recvonly":case "a=inactive":return d[h].substr(2)}return g?e.getDirection(g):"sendrecv"};e.getKind=function(d){return e.splitLines(d)[0].split(" ")[0].substr(2)};e.isRejected=function(d){return"0"===d.split(" ",2)[1]};e.parseMLine=function(d){d=e.splitLines(d)[0].substr(2).split(" ");return{kind:d[0],port:parseInt(d[1],10),protocol:d[2],fmt:d.slice(3).join(" ")}};
e.parseOLine=function(d){d=e.matchPrefix(d,"o=")[0].substr(2).split(" ");return{username:d[0],sessionId:d[1],sessionVersion:parseInt(d[2],10),netType:d[3],addressType:d[4],address:d[5]}};e.isValidSDP=function(d){if("string"!==typeof d||0===d.length)return!1;d=e.splitLines(d);for(var g=0;g<d.length;g++)if(2>d[g].length||"="!==d[g].charAt(1))return!1;return!0};"object"===typeof b&&(b.exports=e)},{}]},{},[1])(1)});class sc{constructor(a){this.FrameDisplayDeltaTimeMs=this.BrowserReceiptTimeMs=this.UETransmissionTimeMs=
this.UECaptureToSendMs=this.UEEncodeMs=this.UEReceiptTimeMs=this.TestStartTimeMs=null;this.onReady=a}reset(){this.FrameDisplayDeltaTimeMs=this.BrowserReceiptTimeMs=this.UETransmissionTimeMs=this.UEEncodeMs=this.UECaptureToSendMs=this.UEReceiptTimeMs=this.TestStartTimeMs=null}start(){this.reset();this.TestStartTimeMs=Date.now();if(this.onReady)this.onReady()}setUETimings(a){this.UEReceiptTimeMs=a.ReceiptTimeMs;this.UEEncodeMs=a.EncodeMs;this.UECaptureToSendMs=a.CaptureToSendMs;this.UETransmissionTimeMs=
a.TransmissionTimeMs;this.BrowserReceiptTimeMs=Date.now();this.latencyTimingsReady()}setFrameDisplayDeltaTime(a){null==this.FrameDisplayDeltaTimeMs&&(this.FrameDisplayDeltaTimeMs=Math.round(a),this.latencyTimingsReady())}latencyTimingsReady(){if(this.BrowserReceiptTimeMs){var a=this.BrowserReceiptTimeMs-this.TestStartTimeMs,b=this.UECaptureToSendMs,c=this.UETransmissionTimeMs-this.UEReceiptTimeMs,e=a-c,d={latencyExcludingDecode:a,encodeLatency:this.UEEncodeMs.toFixed(1),uePixelStreamLatency:b.toFixed(1),
ueTestDuration:c.toFixed(1),networkLatency:e.toFixed(1)};this.FrameDisplayDeltaTimeMs&&this.BrowserReceiptTimeMs&&(d.endToEndLatency=this.FrameDisplayDeltaTimeMs+e+("string"===typeof b?0:b),d.browserSideLatency=this.FrameDisplayDeltaTimeMs+(a-e-c),d.endToEndLatency=d.endToEndLatency.toFixed(1),d.browserSideLatency=d.browserSideLatency.toFixed(1));if(this.onReady)this.onReady(d)}}}class tc{constructor(a,b){this.useStats=a.useStats;this.events=a.events;this.webRtcConfig=a.peerConnectionOptions||{};
this.webRtcConfig.sdpSemantics="unified-plan";this.tnClient=this.dcClient=this.pcClient=null;this.sdpConstraints={offerToReceiveAudio:0,offerToReceiveVideo:1,voiceActivityDetection:!1};this.dataChannelOptions={ordered:!0};this.lastStats={};this.playerId=0;this.latencyTestTimings=new sc(this.events.onLatencyTimingsReady);this.api_isSending=!1;this.api_queue=[];this.player=b;this.api_enableLog=a.enableApiCallLog;this.log=(c,e,d)=>this.player.getAPI().log(c,e,d)}setVideo(a){this.video=a}logStartupInfo(a){if(this.events.onlog)this.events.onlog(a)}logApiCall(a,
b,c){this.api_enableLog&&(a&&""!=a?(a=`[API] ${ua()} ${a}`,this.log(a,b,c)):this.log(""))}logPerfTest(a,b,c){a&&""!=a?this.log(`[PerfTest] ${ua()} ${a}`,b,c):this.log("")}setupPeerConnection(){this.pcClient||(this.pcClient=new RTCPeerConnection(this.webRtcConfig),this.pcClient.addTransceiver("video",{direction:"recvonly"}),this.pcClient.onsignalingstatechange=a=>this.onStateChanged(a),this.pcClient.oniceconnectionstatechange=a=>this.onStateChanged(a),this.pcClient.onicegatheringstatechange=a=>this.onStateChanged(a),
this.pcClient.ontrack=a=>this.events.ontrack(a),this.pcClient.onicecandidate=a=>this.events.onicecandidate(a),this.pcClient.ondatachannel=a=>{this.dcClient=a.channel;this.dcClient.bufferedAmountLowThreshold=this.lowWaterMark;this.setupDataChannelCallbacks()},this.lowWaterMark=this.chunkSize=this.pcClient.sctp?Math.min(this.pcClient.sctp.maxMessageSize,65536):65536,this.highWaterMark=Math.max(8*this.chunkSize,1048576),this.headerOffset=4,this.lenPerChunk=Math.floor((this.chunkSize-this.headerOffset)/
2),this.log("Chunk Size: "+this.chunkSize),this.log("Buffer Threshold Low: "+this.lowWaterMark),this.log("Buffer Threshold High: "+this.highWaterMark))}close(){this.dcClient&&"open"==this.dcClient.readyState&&(this.dcClient.close(),this.dcClient=null);this.pcClient&&(this.pcClient.close(),this.pcClient=null);this.clearStatsTimer()}setupDataChannelCallbacks(){this.dcClient.binaryType="arraybuffer";this.dcClient.onopen=a=>{this.isDatachannelOpened=!0;if(this.events&&this.events.ondatachannelopen)this.events.ondatachannelopen(a);
this.logStartupInfo("video: loading...");for(let b of[81,87,69,82,84,65,83,68,70])this.send((new Uint8Array([61,b])).buffer)};this.dcClient.onclose=a=>{this.isDatachannelOpened=!1;this.clearStatsTimer();if(this.events&&this.events.ondatachannelclose)this.events.ondatachannelclose(a)};this.dcClient.onerror=a=>{this.isDatachannelOpened=!1;this.logStartupInfo("channel error");if(this.events&&this.events.ondatachannelerror)this.events.ondatachannelerror(a)};this.dcClient.onmessage=a=>this.onDatachannelMessage(a)}createOffer(){this.close();
this.setupPeerConnection();try{this.dcClient=this.pcClient.createDataChannel("cirrus",this.dataChannelOptions),this.dcClient.bufferedAmountLowThreshold=this.lowWaterMark,this.setupDataChannelCallbacks(),this.logStartupInfo("channel created")}catch(a){this.logStartupInfo("no channel"),this.dcClient=null}this.pcClient.createOffer(this.sdpConstraints).then(a=>{a.sdp=a.sdp.replace("useinbandfec=1","useinbandfec=1;stereo=1;sprop-maxcapturerate=48000");this.pcClient.setLocalDescription(a);this.events.onOfferCreated(a)},
()=>{this.logStartupInfo("couldn't create offer")})}onReceiveOffer(a){a=new RTCSessionDescription(a);this.setupPeerConnection();this.pcClient.setRemoteDescription(a).then(()=>{this.pcClient.createAnswer().then(b=>this.pcClient.setLocalDescription(b)).then(()=>{if(this.events.onAnswerCreated)this.events.onAnswerCreated(this.pcClient.currentLocalDescription)}).then(()=>{let b=this.pcClient.getReceivers();for(let c of b)c.playoutDelayHint=0}).catch(b=>console.error("createAnswer() failed:",b))});this.setupLiveStats()}onIceCandidate(a){a=
new RTCIceCandidate(a);this.pcClient.addIceCandidate(a).then(b=>{this.logStartupInfo("candidate added")})}onReceiveAnswer(a){this.playerId=a.playerId;this.logStartupInfo(`received answer: ${this.playerId}`);a=new RTCSessionDescription(a);this.pcClient.setRemoteDescription(a);a=this.pcClient.getReceivers();for(let b of a)b.playoutDelayHint=0;this.setupLiveStats()}setupLiveStats(){this.clearStatsTimer();this.useStats&&!this.aggregateStatsIntervalId&&(this.aggregateStatsIntervalId=setInterval(()=>{this.pcClient&&
this.pcClient.getStats(null).then(a=>this.processLiveStats(a))},1E3))}onStateChanged(a){this.signalingState!=this.pcClient.signalingState&&(this.signalingState=this.pcClient.signalingState,this.logStartupInfo(`__signaling: ${this.signalingState}`));this.iceConnectionState!=this.pcClient.iceConnectionState&&(this.iceConnectionState=this.pcClient.iceConnectionState,this.logStartupInfo(`__ice_connection: ${this.iceConnectionState}`),"disconnected"!=this.iceConnectionState||this.isDatachannelOpened||
this.logStartupInfo("\u8fde\u63a5\u5efa\u7acb\u5931\u8d25\uff0c\u8bf7\u68c0\u67e5\u670d\u52a1\u5668\u7aef\u53e3\u8bbe\u7f6e"),"failed"!=this.iceConnectionState||this.isDatachannelOpened||this.logStartupInfo("\u8fde\u63a5\u5efa\u7acb\u5931\u8d25\uff0c\u65e0\u53ef\u7528\u7684\u5019\u9009\u8fde\u63a5"));this.iceGatheringState!=this.pcClient.iceGatheringState&&(this.iceGatheringState=this.pcClient.iceGatheringState,this.logStartupInfo(`__ice_gathering: ${this.iceGatheringState}`))}clearStatsTimer(){this.aggregateStatsIntervalId&&
(clearInterval(this.aggregateStatsIntervalId),this.aggregateStatsIntervalId=void 0)}send(a){return this.dcClient&&"open"==this.dcClient.readyState?(this.dcClient.send(a),!0):!1}requestInitialSettings(){this.send((new Uint8Array([7])).buffer)}requestQualityControl(){this.send((new Uint8Array([1])).buffer)}sendEnableIntervalSendQP(a){this.send((new Uint8Array([11,a?1:0])).buffer)}sendEnableReceiveEvents(a){this.send((new Uint8Array([12,a?1:0])).buffer)}_createApiBinBuffer(a,b,c){let e=new DataView(new ArrayBuffer(this.headerOffset+
2*b)),d=0;e.setUint8(d,52);d++;e.setUint8(d,a);d++;e.setUint16(d,b,!0);d+=2;for(a=0;a<c.length;a++)e.setUint16(d,c.charCodeAt(a),!0),d+=2;return e.buffer}_pushApiToSendingQueue(a){if("object"==typeof a){a.__playerId=this.playerId;var b=JSON.stringify(a);a=b.length;if(a>this.lenPerChunk){let e=Math.ceil(a/this.lenPerChunk);for(let d=0;d<e;d++){var c=0==d?1:d==e-1?3:2;let g=d==e-1?a-d*this.lenPerChunk:this.lenPerChunk,h=b.substr(d*this.lenPerChunk,g);c=this._createApiBinBuffer(c,g,h);this.api_queue.push(c)}this.logApiCall(`Push ${e} chunks, length: ${a}`)}else b=
this._createApiBinBuffer(0,a,b),this.api_queue.push(b),this.logApiCall(`Push 1 chunk, length: ${a}`)}}_sendApiFromQueue(){this.api_isSending=!0;this.api_timeoutHandle&&(clearTimeout(this.api_timeoutHandle),this.api_timeoutHandle=null);let a=this.dcClient.bufferedAmount;for(;0<this.api_queue.length;){let b=this.api_queue.shift();this.logApiCall(`Sending data begin, size: ${b.byteLength}, queue: ${this.api_queue.length}`,!1,"gray");this.dcClient.send(b);a+=this.chunkSize;if(a>=this.highWaterMark){this.dcClient.bufferedAmount<
this.lowWaterMark&&(this.api_timeoutHandle=setTimeout(()=>{0==this.api_queue.length?(this.api_isSending=!1,this.logApiCall("Finished!")):(this.logApiCall("continue send after timeout."),this._sendApiFromQueue())},0));this.logApiCall(`-------paused------- queue: ${this.api_queue.length}, bufferedAmount: ${a}, highWaterMark: ${this.highWaterMark}`);return}}this.api_isSending=!1;this.logApiCall("Finished!")}sendApi(a){this.logApiCall();this.logApiCall(`START: ${a?a.command:"Unkown"}`);this.dcClient&&
"open"==this.dcClient.readyState?(this._pushApiToSendingQueue(a),this.dcClient.onbufferedamountlow=b=>{this.logApiCall(`-------bufferedamountlow------- queue: ${this.api_queue.length}`);0==this.api_queue.length?(this.api_isSending=!1,this.logApiCall("Finished!")):(this.logApiCall("continue send after bufferedamountlow."),this._sendApiFromQueue())},this.api_isSending?this.logApiCall("Waiting..."):this._sendApiFromQueue()):this.logApiCall("Data channel is invalid!")}perfTest(a){this.api_isSending?this.logPerfTest("Please wait until the API call is completed before testing!",
!1,"red"):(this.pt_finished=!1,this.pt_bytesToSend=1048576*a,this.pt_timeoutHandle=null,this.pt_sended=this.pt_sendStartTime=this.pt_maxTimeUsedInSend=this.pt_numberOfSendCalls=this.pt_totalTimeUsedInSend=0,this.logPerfTest(),this.logPerfTest(`Performence Testing Starting: ${a}MB (${1048576*a})`,!1,"blue"),this.logPerfTest("Chunk Size: "+this.chunkSize),this.logPerfTest("Buffer Threshold Low: "+this.lowWaterMark),this.logPerfTest("Buffer Threshold High: "+this.highWaterMark),this.dcClient.onbufferedamountlow=
b=>{this.logPerfTest(`[bufferedamountlow] sendCalls: ${this.pt_numberOfSendCalls}, sended: ${this.pt_sended}, bufferedAmount: ${this.dcClient.bufferedAmount}`,!1,"gray");this.pt_finished||this.pt_send()},this.pt_create_data=()=>{var b=new Date;b=b.toLocaleString()+"."+b.getMilliseconds();let c=new DataView(new ArrayBuffer(this.chunkSize)),e=0;c.setUint8(e,53);e++;c.setUint16(e,b.length,!0);e+=2;for(let d=0;d<b.length;d++)c.setUint16(e,b.charCodeAt(d),!0),e+=2;return c.buffer},this.pt_send=()=>{null!==
this.pt_timeoutHandle&&(clearTimeout(this.pt_timeoutHandle),this.pt_timeoutHandle=null);for(var b=this.dcClient.bufferedAmount;this.pt_sended<this.pt_bytesToSend;){var c=performance.now();this.dcClient.send(this.pt_create_data());c=performance.now()-c;this.pt_totalTimeUsedInSend+=c;c>this.pt_maxTimeUsedInSend&&(this.pt_maxTimeUsedInSend=c);this.pt_numberOfSendCalls+=1;b+=this.chunkSize;this.pt_sended+=this.chunkSize;if(b>=this.highWaterMark){this.dcClient.bufferedAmount<this.lowWaterMark&&(this.pt_timeoutHandle=
setTimeout(()=>sendData(),0));this.logPerfTest(`[-------paused-------] sendCalls: ${this.pt_numberOfSendCalls}, sended: ${this.pt_sended}, ${b} / ${this.dcClient.bufferedAmount}`,!1,"darkgray");break}}this.pt_sended===this.pt_bytesToSend&&(this.pt_finished=!0,this.logPerfTest("Data transfer completed successfully!",!1,"blue"),this.logPerfTest("      Total sended: "+this.pt_sended),this.logPerfTest("      Average time spent in send() (ms): "+this.pt_totalTimeUsedInSend/this.pt_numberOfSendCalls),this.logPerfTest("      Max time spent in send() (ms): "+
this.pt_maxTimeUsedInSend),b=performance.now()-this.pt_sendStartTime,this.logPerfTest("      Total time spent: "+b),this.logPerfTest("      MBytes/Sec: "+this.pt_bytesToSend/1E3/b))},this.logPerfTest("Start sending data..."),this.pt_sendStartTime=performance.now(),this.pt_sended=this.pt_numberOfSendCalls=this.pt_totalTimeUsedInSend=this.pt_maxTimeUsedInSend=0,this.pt_send())}sendDescriptor(a,b){b=JSON.stringify(b);let c=new DataView(new ArrayBuffer(3+2*b.length)),e=0;c.setUint8(e,a);e++;c.setUint16(e,
b.length,!0);e+=2;for(a=0;a<b.length;a++)c.setUint16(e,b.charCodeAt(a),!0),e+=2;this.send(c.buffer)}updateRenderResolution(a,b){a={Console:"setres "+a+"x"+b+"w"};b=JSON.stringify(a);console.log(b);let c,e;null==(c=this.player)||null==(e=c.eventsPanel)||e.appendText(b);this.sendDescriptor(50,a)}updateParams(a){a.encodeMaxQP&&this.sendDescriptor(50,{Console:"PixelStreaming.Encoder.MaxQP "+a.encodeMaxQP});a.keyframeInterval&&this.sendDescriptor(50,{Console:"PixelStreaming.Encoder.KeyframeInterval "+
a.keyframeInterval});a.maxBitrate&&this.sendDescriptor(50,{Console:"PixelStreaming.WebRTC.MaxBitrate "+a.maxBitrate})}processLiveStats(a){let b={};a.forEach(c=>{"inbound-rtp"!=c.type||c.isRemote||"video"!=c.mediaType&&"video"!=c.kind&&!c.id.toLowerCase().includes("video")||(b.timestamp=c.timestamp,b.timestampStart=this.lastStats&&this.lastStats.timestampStart?this.lastStats.timestampStart:c.timestamp,b.bytesReceived=c.bytesReceived,b.packetsLost=c.packetsLost,b.framesDropped=c.framesDropped,b.framesPerSecond=
c.framesPerSecond,b.framesReceived=c.framesReceived,b.framesDecoded=c.framesDecoded,b.keyFramesDecoded=c.keyFramesDecoded,b.frameWidth=c.frameWidth,b.frameHeight=c.frameHeight,this.lastStats&&(b.bitrate=8*(b.bytesReceived-this.lastStats.bytesReceived)/(b.timestamp-this.lastStats.timestamp)));"track"==c.type&&c.frameWidth&&(c.framesDropped&&(b.framesDropped=c.framesDropped),c.framesReceived&&(b.framesReceived=c.framesReceived),b.frameWidth=c.frameWidth,b.frameHeight=c.frameHeight);"candidate-pair"==
c.type&&c.hasOwnProperty("currentRoundTripTime")&&0!=c.currentRoundTripTime&&(b.currentRoundTripTime=c.currentRoundTripTime)});this.lastStats.receiveToCompositeMs&&(b.receiveToCompositeMs=this.lastStats.receiveToCompositeMs,this.latencyTestTimings.setFrameDisplayDeltaTime(this.lastStats.receiveToCompositeMs));this.lastStats=b;this.events.onstats(this.lastStats)}onDatachannelMessage(a){var b=new Uint8Array(a.data);switch(b[0]){case 0:this.events.ondatachannelmessage("QualityControlOwnership",1==b[1]);
break;case 1:if((a=(new TextDecoder("utf-16")).decode(a.data.slice(1)))&&""!=a)this.events.ondatachannelmessage("ResponseAPI",a);break;case 101:b=b[1];a=(new TextDecoder("utf-8")).decode(a.data.slice(2));switch(b){case 0:this.events.ondatachannelmessage("ResponseAPI",a);break;case 1:this.reponse_api_json_string=a;break;case 2:this.reponse_api_json_string+=a;break;case 3:this.reponse_api_json_string+=a,this.events.ondatachannelmessage("ResponseAPI",this.reponse_api_json_string)}break;case 6:a=(new TextDecoder("utf-16")).decode(a.data.slice(1));
a=JSON.parse(a);this.latencyTestTimings.setUETimings(a);break;case 7:a=(new TextDecoder("utf-16")).decode(a.data.slice(1));a=JSON.parse(a);if(a.Encoder)this.events.ondatachannelmessage("InitialSettings",a);break;case 5:this.events.ondatachannelmessage("VideoEncoderAvgQP",a)}}startLatencyTest(){this.video&&(this.latencyTestTimings.start(),this.sendDescriptor(6,{StartTime:this.latencyTestTimings.TestStartTimeMs}))}}class ia{constructor(a){this.player=a;this.uniqueId=a.uniqueId;this.container=a.container}appendElement(a){this.container.appendChild(a);
this.setNeedReposUI()}removeElement(a){this.container.removeChild(a)}get(a){return Aa(a+"_"+this.uniqueId)}getValue(a){let b;return null==(b=this.get(a))?void 0:b.value}setValue(a,b){(a=this.get(a))&&(a.value=b)}setText(a,b){(a=this.get(a))&&(a.innerText=b)}setHtml(a,b){(a=this.get(a))&&(a.innerHTML=b)}resize(){this.player.resize()}setNeedReposUI(){this.player.bNeedReposUI=!0}}class uc extends ia{constructor(a){super(a);this.el=document.createElement("pre");this.el.id="eventsPanel_"+this.uniqueId;
this.el.className="__eventsPanel";this.el.oncontextmenu=new Function("return false");this.el.onselectstart=new Function("return false");this.appendElement(this.el);this.btnClear=document.createElement("a");this.btnClear.id="eventsPanelClear_"+this.uniqueId;this.btnClear.innerText="Clear";this.btnClear.className="__eventsPanelClear";this.btnClear.style.position="absolute";this.btnClear.setAttribute("href","javascript:");this.btnClear.addEventListener("click",b=>this.el.innerText="",!1);this.appendElement(this.btnClear)}destroy(){this.el&&
(this.removeElement(this.el),this.el=null)}appendText(a){if(this.el){let b=this.el.innerText;b+=a+"\n";this.el.innerText=b;this.el.scrollTop=this.el.scrollHeight+100}}setPosition(a){a||(a=this.container.getBoundingClientRect());this.el&&(this.el.style.width=a.width+"px",this.el.style.height=a.height/3+"px");this.btnClear&&(this.btnClear.style.left=a.width-50+"px",this.btnClear.style.top="20px")}}class da{}da.Information='<svg width="22" height="22" viewBox="0 0 32 32"><g transform="translate(1878 2755)"><rect width="32" height="32" transform="translate(-1878 -2755)" fill="none"/><path d="M11,22A11,11,0,0,1,3.222,3.222,11,11,0,1,1,18.779,18.778,10.929,10.929,0,0,1,11,22ZM8.5,10a.5.5,0,0,0-.5.5v2a.5.5,0,0,0,.5.5H10v5.5a.5.5,0,0,0,.5.5h2a.5.5,0,0,0,.5-.5v-8a.5.5,0,0,0-.5-.5ZM11,4a2,2,0,1,0,2,2A2,2,0,0,0,11,4Z" transform="translate(-1873 -2750)" fill="#3c3b3c"/></g></svg>';
da.FullScreenEnter='<svg width="22" height="22" viewBox="0 0 32 32"><g id="cloud_icon_fullscreen_def" transform="translate(1787 2755)"><rect width="32" height="32" transform="translate(-1787 -2755)" fill="none"/><g transform="translate(0 -0.5)"><g transform="translate(-1780 -2747.5)"><rect width="10" height="10" transform="translate(-2 -2)" fill="none"/><path d="M12,0H7A1,1,0,0,0,6,1V6" transform="translate(-6)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,6.5,0,1" transform="translate(0.5 -0.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1782 -2737.5)"><rect width="10" height="10" fill="none"/><path d="M12,6H7A1,1,0,0,1,6,5V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,1,0,6.5" transform="translate(2.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1770 -2749.5)"><rect width="10" height="10" fill="none"/><path d="M6,0h5a1,1,0,0,1,1,1V6" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,6.5,6,1" transform="translate(1.5 1.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1770 -2737.5)"><rect width="10" height="10" fill="none"/><path d="M6,6h5a1,1,0,0,0,1-1V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,1,6,6.5" transform="translate(1.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g></g></g></svg>';
da.FullScreenExit='<svg width="22" height="22" viewBox="0 0 32 32"><g id="cloud_icon_exotfullsc_def" transform="translate(1744 2755)"><rect width="32" height="32" transform="translate(-1744 -2755)" fill="none"/><g transform="translate(0 -1)"><g transform="translate(-1739 -2749)"><rect width="10" height="10" fill="none"/><path d="M6,6h5a1,1,0,0,0,1-1V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,1,6,6.5" transform="translate(1.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1739 -2737)"><rect width="10" height="10" fill="none"/><path d="M6,0h5a1,1,0,0,1,1,1V6" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,6.5,6,1" transform="translate(1.5 1.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1727 -2749)"><rect width="10" height="10" fill="none"/><path d="M12,6H7A1,1,0,0,1,6,5V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,1,0,6.5" transform="translate(2.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1727 -2737)"><rect width="10" height="10" fill="none"/><path d="M12,0H7A1,1,0,0,0,6,1V6" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,6.5,0,1" transform="translate(2.5 1.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g></g></g></svg>';
da.InitialCamera='<svg width="22" height="22" viewBox="0 0 32 32"><g id="cloud_icon_initialpos_def" transform="translate(1831 2755)"><rect width="32" height="32" transform="translate(-1831 -2755)" fill="none"/><path d="M18.667,22.222H1.333A1.333,1.333,0,0,1,0,20.893V8.24A1.319,1.319,0,0,1,.5,7.2L9.167.292a1.335,1.335,0,0,1,1.666,0L19.5,7.2A1.319,1.319,0,0,1,20,8.24V20.893A1.333,1.333,0,0,1,18.667,22.222ZM9,12.11a1,1,0,0,0-1,1v6a1,1,0,0,0,1,1h2a1,1,0,0,0,1-1v-6a1,1,0,0,0-1-1Z" transform="translate(-1825 -2751.11)" fill="#3c3b3c" stroke="rgba(0,0,0,0)" stroke-width="1"/></g></svg>';
class vc extends ia{constructor(a){super(a);this.create()}create(){if(!this.el){this.el=document.createElement("button");this.el.id=`fullscreeButton_${this.uniqueId}`;this.el.className="__fullscreeButton";if(S.isIOSDevice||S.isSafari)this.el.style.backgroundSize="cover";this.el.innerHTML=da.FullScreenEnter;this.el.title=H.getString("EnterFullscreen");this.el.oncontextmenu=new Function("return false");this.el.onselectstart=new Function("return false");this.el.onclick=()=>this.player.fullscreen=!this.player.fullscreen;
this.el.onmousedown=a=>{1==a.button&&a.preventDefault()};this.appendElement(this.el);document.addEventListener("fullscreenchange",()=>{this.el.title=H.getString("ExitFullscreen");this.el.innerHTML=S.isFullscreen()?da.FullScreenExit:da.FullScreenEnter;this.player.resize()})}}destroy(){this.el&&(this.removeElement(this.el),this.el=null)}setPosition(a,b){a||(a=this.container.getBoundingClientRect());this.el&&(this.el.style.left=`${b?38:5}px`,this.el.style.top=`${a.height-32}px`)}}const ea={BackSpace:8,
Shift:16,Control:17,Alt:18,RightShift:253,RightControl:254,RightAlt:255};class wc extends ia{constructor(a,b){super(a);this.video=null;this.useBuiltinCursors=b.useBuiltinCursors;this.keyEventTargetStr=b.keyEventTarget;this.keyEventTarget="document"==b.keyEventTarget?document:"none"==b.keyEventTarget?null:"video";this.callOnUserInteraction=()=>this.player.resetInteractTimestamp();this.fakeMouseWithTouches=!1;this.onMButtonDoubleClick=b.onMButtonDoubleClick;this.mouseKeyListener=b.mouseKeyListener;
this.touchListener=b.touchListener;this.onActionEnable=!0;this.onEvents=b.onEvents;this.onVideoLoaded=b.onVideoLoaded;this.orientationChangeTimeout=void 0;document.addEventListener("WeixinJSBridgeReady",()=>{S.isInWeixinBrowser=!0;this.video.play()});this.create()}registerEvents(){window.addEventListener("orientationchange",()=>{clearTimeout(this.orientationChangeTimeout);this.orientationChangeTimeout=setTimeout(()=>this.player.resize(),500)});this.registerKeyboardEvents();this.registerMouseEvents();
this.registerTouchEvents()}isVideoLoaded(){return!!this._isVideoLoaded}create(){this.video&&this.destroy();this.video||(this.video=document.createElement("video"),this.video.id=`streamingVideo_${this.uniqueId}`,this.video.style="position:absolute;",this.video.playsInline=!0,this.video.muted=!0,this.video.autoplay=!0,this.video.disablepictureinpicture=!0,"video"===this.keyEventTarget&&(this.video.tabIndex=1,this.keyEventTarget=this.video),this.appendElement(this.video),this.player.logStartupInfo("video element created"),
this._isVideoLoaded=!1,this.video.addEventListener("loadedmetadata",()=>{this._isVideoLoaded=!0;this.video.onmouseenter({button:0,x:0,y:0});this.onVideoLoaded()},!0),this.setCursor());if("requestVideoFrameCallback"in HTMLVideoElement.prototype){const a=(b,c)=>{c.receiveTime&&c.expectedDisplayTime&&(this.rtc.lastStats.receiveToCompositeMs=c.presentationTime-c.receiveTime);this.video&&this.video.requestVideoFrameCallback(a)};this.video.requestVideoFrameCallback(a)}}setWebRtcPlayer(a){(!this.video||
S.isAndroidDevice||S.isIOSDevice&&!S.isInWeixinBrowser)&&this.create();this.rtc=a;this.rtc.setVideo(this.video)}setActionEventEnabled(a){this.onActionEnable=a}setEnableInteract(a){this.disableInteract=!a}destroy(){this.video&&(this.removeElement(this.video),this.video=null)}valid(){return!!this.video}videoWidth(){let a;return null==(a=this.video)?void 0:a.videoWidth}videoHeight(){let a;return null==(a=this.video)?void 0:a.videoHeight}clientWidth(){let a;return null==(a=this.video)?void 0:a.clientWidth}clientHeight(){let a;
return null==(a=this.video)?void 0:a.clientHeight}resize(a){this.playerAspectRatio=this.container.clientHeight/this.container.clientWidth;this.videoAspectRatio=a?this.playerAspectRatio:this.videoHeight()/this.videoWidth()}setTrack(a){let b=a.streams[0];this.player.logStartupInfo("track: "+a.track.kind);"audio"==a.track.kind?(a=document.createElement("Audio"),a.autoplay=!0,a.srcObject=b,a.play(),this.player.logStartupInfo("audio element created")):"video"==a.track.kind&&this.video.srcObject!==b&&(this.video.srcObject=
b)}setKeyEventTarget(a){this.keyEventTargetStr!=a&&(this.clearKeyboardEvents(),"video"==a?(this.video&&(this.video.tabIndex=1),this.keyEventTarget=this.video,this.video.focus()):"document"==a?(this.keyEventTarget=document,this.video.focus()):this.keyEventTarget&&(this.keyEventTarget=null),this.keyEventTargetStr=a,this.registerKeyboardEvents())}clearKeyboardEvents(){this.keyEventTarget&&(this.keyEventTarget.onkeydown=null,this.keyEventTarget.onkeyup=null,this.keyEventTarget.onkeypress=null)}registerKeyboardEvents(){this.keyEventTarget&&
(this.keyEventTarget.onkeydown=a=>{if(this.disableInteract)a.preventDefault();else{112<=a.keyCode&&123>=a.keyCode||a.ctrlKey||this.sendKeydown(a);if(a.keyCode===ea.BackSpace)this.keyEventTarget.onkeypress({charCode:ea.BackSpace});this.callActionEventHander(5,a)}},this.keyEventTarget.onkeyup=a=>{this.disableInteract?a.preventDefault():(112<=a.keyCode&&123>=a.keyCode||a.ctrlKey||this.sendKeyup(a),this.callActionEventHander(6,a))},this.keyEventTarget.onkeypress=a=>{this.disableInteract?a.preventDefault():
(112<=a.keyCode&&123>=a.keyCode||this.sendKeypress(a),this.callActionEventHander(7,a))})}setCursor(){if(this.video&&!this.disableInteract)if(this.useBuiltinCursors)switch(this.mouseDownButton){case 0:this.video.setAttribute("class","streamingVideoCursorPan");break;case 1:this.video.setAttribute("class","streamingVideoCursorMove");break;case 2:this.video.setAttribute("class","streamingVideoCursorRotate");break;default:this.video.setAttribute("class","streamingVideoCursorPointer")}else this.video.setAttribute("class",
"streamingVideoNoCursor")}callActionEventHander(a,b){if(this.mouseKeyListener&&this.onActionEnable)switch(a){case 0:if(S.isFunction(this.mouseKeyListener.onMouseEnter))this.mouseKeyListener.onMouseEnter(b);break;case 1:if(S.isFunction(this.mouseKeyListener.onMouseLeave))this.mouseKeyListener.onMouseLeave(b);break;case 2:if(S.isFunction(this.mouseKeyListener.onMouseMove))this.mouseKeyListener.onMouseMove(b);break;case 3:if(S.isFunction(this.mouseKeyListener.onMouseDown))this.mouseKeyListener.onMouseDown(b);
break;case 4:if(S.isFunction(this.mouseKeyListener.onMouseUp))this.mouseKeyListener.onMouseUp(b);break;case 5:if(S.isFunction(this.mouseKeyListener.onKeyDown))this.mouseKeyListener.onKeyDown(b);break;case 6:if(S.isFunction(this.mouseKeyListener.onKeyUp))this.mouseKeyListener.onKeyUp(b);break;case 7:if(S.isFunction(this.mouseKeyListener.onKeyPress))this.mouseKeyListener.onKeyPress(b)}}isMiddleDoubleClick(a){if(1==a.button){if(this.lastMClickTimestamp&&200>a.timeStamp-this.lastMClickTimestamp)return!0;
this.lastMClickTimestamp=a.timeStamp}else this.lastMClickTimestamp=void 0;return!1}normalizeAndQuantizeUnsigned(a,b){if(this.playerAspectRatio>this.videoAspectRatio){let c=this.playerAspectRatio/this.videoAspectRatio;a/=this.container.clientWidth;b=c*(b/this.container.clientHeight-.5)+.5}else a=this.videoAspectRatio/this.playerAspectRatio*(a/this.container.clientWidth-.5)+.5,b/=this.container.clientHeight;return 0>a||1<a||0>b||1<b?{inRange:!1,x:65535,y:65535}:{inRange:!0,x:65536*a,y:65536*b}}normalizeAndQuantizeSigned(a,
b){if(this.playerAspectRatio>this.videoAspectRatio){let c=this.playerAspectRatio/this.videoAspectRatio;a/=.5*this.container.clientWidth;b=c*b/(.5*this.container.clientHeight);return{x:32767*a,y:32767*b}}return{x:this.videoAspectRatio/this.playerAspectRatio*a/(.5*this.container.clientWidth)*32767,y:b/(.5*this.container.clientHeight)*32767}}registerMouseEvents(){this.video.onmouseenter=a=>{this.disableInteract?a.preventDefault():(this.sendMouseEnter(!0),a.buttons&1&&this.sendMouseDown(0,a.offsetX,a.offsetY),
a.buttons&2&&this.sendMouseDown(2,a.offsetX,a.offsetY),a.buttons&4&&this.sendMouseDown(1,a.offsetX,a.offsetY),a.buttons&8&&this.sendMouseDown(3,a.offsetX,a.offsetY),a.buttons&16&&this.sendMouseDown(4,a.offsetX,a.offsetY),this.callActionEventHander(0,a))};this.video.onmouseleave=a=>{this.disableInteract?a.preventDefault():(this.sendMouseLeave(!0),a.buttons&1&&this.sendMouseUp(0,a.offsetX,a.offsetY),a.buttons&2&&this.sendMouseUp(2,a.offsetX,a.offsetY),a.buttons&4&&this.sendMouseUp(1,a.offsetX,a.offsetY),
a.buttons&8&&this.sendMouseUp(3,a.offsetX,a.offsetYy),a.buttons&16&&this.sendMouseUp(4,a.offsetX,a.offsetY),this.callActionEventHander(1,a))};this.video.onmousemove=a=>{if(this.disableInteract)a.preventDefault();else{this.sendMouseMove(a.offsetX,a.offsetY,a.movementX,a.movementY);a.preventDefault();if(0==this.mouseDownButton||2==this.mouseDownButton)this.setCursor(),this.mouseDownButton=void 0;this.callActionEventHander(2,a)}};this.video.onmousedown=a=>{this.disableInteract?a.preventDefault():(this.keyEventTarget==
this.video&&this.video.focus(),this.sendMouseEnter(!0),this.isMiddleDoubleClick(a)&&this.onMButtonDoubleClick?(this.onMButtonDoubleClick(a.offsetX,a.offsetY),a.preventDefault()):(this.mouseDownButton=a.button,this.sendMouseDown(a.button,a.offsetX,a.offsetY),a.preventDefault(),0!=a.button&&2!=a.button&&this.setCursor(),this.callActionEventHander(3,a)))};this.video.onmouseup=a=>{this.disableInteract?a.preventDefault():(this.mouseDownButton=void 0,this.sendMouseUp(a.button,a.offsetX,a.offsetY),a.preventDefault(),
this.setCursor(),this.callActionEventHander(4,a))};this.video.oncontextmenu=a=>{a.preventDefault()};"onmousewheel"in this.video?this.video.addEventListener("mousewheel",a=>{this.disableInteract||this.sendMouseWheel(a.wheelDelta,a.offsetX,a.offsetY);a.preventDefault()},{passive:!1}):this.video.addEventListener("DOMMouseScroll",a=>{this.disableInteract||this.sendMouseWheel(-120*a.detail,a.offsetX,a.offsetY);a.preventDefault()},!1)}registerTouchEvents(){this.initFingers=()=>{this.fingers=[9,8,7,6,5,
4,3,2,1,0];this.fingerIds={};this.bWaitFor0Fingers=!1};this.rememberTouch=a=>{let b=this.fingers.pop();if(void 0===b){let c;null==(c=this.onEvents)||c.call(this,"\tError! Exhausted touch indentifiers!")}else this.fingerIds[a.identifier]=b};this.forgetTouch=a=>{this.fingers.push(this.fingerIds[a.identifier]);delete this.fingerIds[a.identifier]};this.triggerTouchEvent=(a,b)=>{"function"===typeof this.touchListener&&this.onActionEnable&&this.touchListener(a,b)};this.initFingers();this.handleTouchStart=
a=>{a.preventDefault();let b;null==(b=this.onEvents)||b.call(this,`[S] ${a.touches.length} / ${a.changedTouches.length}`);if(this.bWaitFor0Fingers){let e;null==(e=this.onEvents)||e.call(this,"\tWaiting...")}else if(2<a.touches.length){this.bWaitFor0Fingers=!0;let e;null==(e=this.onEvents)||e.call(this,"\tWAITING START!");this.sendTouch(81,a.touches)}else{for(let e of a.changedTouches)this.rememberTouch(e);var c;null==(c=this.onEvents)||c.call(this,`\t${JSON.stringify(this.fingerIds)} ${JSON.stringify(this.fingers)}`);
this.sendTouch(80,a.changedTouches);this.triggerTouchEvent("touchstart",a)}};this.handleTouchMove=a=>{if(this.disableInteract)a.preventDefault();else{a.preventDefault();var b;null==(b=this.onEvents)||b.call(this,`[M] ${a.touches.length} / ${a.changedTouches.length}`);if(this.bWaitFor0Fingers){let c;null==(c=this.onEvents)||c.call(this,"\tWaiting...")}else this.sendTouch(82,a.touches),this.triggerTouchEvent("touchmove",a)}};this.handleTouchEnd=a=>{if(this.disableInteract)a.preventDefault();else{a.preventDefault();
var b;null==(b=this.onEvents)||b.call(this,`[E] ${a.touches.length} / ${a.changedTouches.length}`);if(this.bWaitFor0Fingers){if(0==a.touches.length){this.bWaitFor0Fingers=!1;this.initFingers();let e;null==(e=this.onEvents)||e.call(this,"\tWAITING END!")}}else{this.sendTouch(81,a.changedTouches);for(let e of a.changedTouches)this.forgetTouch(e);var c;null==(c=this.onEvents)||c.call(this,`\t${JSON.stringify(this.fingerIds)} ${JSON.stringify(this.fingers)}`);this.triggerTouchEvent("touchend",a)}}};this.handleTouchCancel=
a=>{if(this.disableInteract)a.preventDefault();else{a.preventDefault();var b;null==(b=this.onEvents)||b.call(this,`[C] ${a.touches.length} / ${a.changedTouches.length}`);this.sendTouch(81,a.changedTouches);this.initFingers();this.triggerTouchEvent("touchcancel",a)}};this.video.addEventListener("touchstart",a=>this.handleTouchStart(a),!1);this.video.addEventListener("touchmove",a=>this.handleTouchMove(a),!1);this.video.addEventListener("touchend",a=>this.handleTouchEnd(a),!1);this.video.addEventListener("touchcancel",
a=>this.handleTouchCancel(a),!1)}sendTouch(a,b){this.callOnUserInteraction();let c=new DataView(new ArrayBuffer(2+10*b.length));c.setUint8(0,a);c.setUint8(1,b.length);a=2;let e=[],d=this.video.getBoundingClientRect();for(let g of b){let h=g.clientX-d.left,q=g.clientY-d.top,l=this.normalizeAndQuantizeUnsigned(h,q),f=this.fingerIds[g.identifier];if(void 0==f){let m;null==(m=this.onEvents)||m.call(this,`\tError! Invalid: ${g.identifier}, ${JSON.stringify(this.fingerIds)} ${JSON.stringify(this.fingers)}, count:${b.length}`)}else c.setUint16(a,
l.x,!0),a+=2,c.setUint16(a,l.y,!0),a+=2,c.setInt32(a,f,!0),a+=4,c.setUint8(a,255*g.force,!0),a+=1,c.setUint8(a,l.inRange?1:0,!0),a+=1,e.push(`{${f} (${parseInt(h)},${parseInt(q)})->(${parseInt(l.x)},${parseInt(l.y)})}`)}this.player.doEventSync(c.buffer);this.rtc.send(c.buffer)}sendKeydown(a){this.callOnUserInteraction();let b=Ba(a),c;null==(c=this.onEvents)||c.call(this,`Send KeyDown, Code:${b}, Repeat:${a.repeat}`);a=(new Uint8Array([60,b,a.repeat])).buffer;this.player.doEventSync(a);this.rtc.send(a)}sendKeyup(a){this.callOnUserInteraction();
a=Ba(a);var b;null==(b=this.onEvents)||b.call(this,`Send KeyUp:${a}`);b=(new Uint8Array([61,a])).buffer;this.player.doEventSync(b);this.rtc.send(b)}sendKeypress(a){this.callOnUserInteraction();var b;null==(b=this.onEvents)||b.call(this,`Send KeyPress:${a.charCode}`);b=new DataView(new ArrayBuffer(3));b.setUint8(0,62);b.setUint16(1,a.charCode,!0);this.player.doEventSync(b.buffer);this.rtc.send(b.buffer)}sendMouseEnter(){this.callOnUserInteraction();var a=new DataView(new ArrayBuffer(1));a.setUint8(0,
70);this.player.doEventSync(a.buffer);this.rtc.send(a.buffer);let b;null==(b=this.onEvents)||b.call(this,"Send MouseEnter")}sendMouseLeave(){this.callOnUserInteraction();var a=new DataView(new ArrayBuffer(1));a.setUint8(0,71);this.player.doEventSync(a.buffer);this.rtc.send(a.buffer);let b;null==(b=this.onEvents)||b.call(this,"Send MouseLeave")}sendMouseMove(a,b,c,e){this.callOnUserInteraction();if(this.normalizeAndQuantizeUnsigned){var d=this.normalizeAndQuantizeUnsigned(a,b),g=this.normalizeAndQuantizeSigned(c,
e),h=new DataView(new ArrayBuffer(9));h.setUint8(0,74);h.setUint16(1,d.x,!0);h.setUint16(3,d.y,!0);h.setInt16(5,g.x,!0);h.setInt16(7,g.y,!0);this.player.doEventSync(h.buffer);this.rtc.send(h.buffer);var q;null==(q=this.onEvents)||q.call(this,`Send MouseMove, X:${a}, Y:${b}, DeltaX:${c}, DeltaY:${e}`)}}sendMouseDown(a,b,c){this.callOnUserInteraction();if(this.normalizeAndQuantizeUnsigned){var e=this.normalizeAndQuantizeUnsigned(b,c),d=new DataView(new ArrayBuffer(6));d.setUint8(0,72);d.setUint8(1,
a);d.setUint16(2,e.x,!0);d.setUint16(4,e.y,!0);this.player.doEventSync(d.buffer);this.rtc.send(d.buffer);var g;null==(g=this.onEvents)||g.call(this,`Send MouseDown, Button:${a}, X:${b}, Y:${c}`)}}sendMouseUp(a,b,c){if(this.normalizeAndQuantizeUnsigned){var e=this.normalizeAndQuantizeUnsigned(b,c),d=new DataView(new ArrayBuffer(6));d.setUint8(0,73);d.setUint8(1,a);d.setUint16(2,e.x,!0);d.setUint16(4,e.y,!0);this.player.doEventSync(d.buffer);this.rtc.send(d.buffer);var g;null==(g=this.onEvents)||g.call(this,
`Send MouseUp, Button:${a}, X:${b}, Y:${c}`)}}sendMouseWheel(a,b,c){this.callOnUserInteraction();if(this.normalizeAndQuantizeUnsigned){var e=this.normalizeAndQuantizeUnsigned(b,c),d=new DataView(new ArrayBuffer(7));d.setUint8(0,75);d.setInt16(1,a,!0);d.setUint16(3,e.x,!0);d.setUint16(5,e.y,!0);this.player.doEventSync(d.buffer);this.rtc.send(d.buffer);var g;null==(g=this.onEvents)||g.call(this,`Send MouseWheel, Delta:${a}, X:${b}, Y:${c}`)}}}class xc extends ia{constructor(a,b,c){super(a);this.updateConnectionCount(b);
this.createButton(c)}createButton(a){if(!this.elButton){this.elButton=document.createElement("button");this.elButton.id=`liveStatusSwitchButton_${this.uniqueId}`;this.elButton.innerHTML=da.Information;this.elButton.title=a;this.elButton.className="__liveStatusSwitchButton";if(S.isIOSDevice||S.isSafari)this.elButton.style.backgroundSize="cover";this.elButton.oncontextmenu=new Function("return false");this.elButton.onselectstart=new Function("return false");this.elButton.onclick=()=>this.setPanelVisible();
this.elButton.onmousedown=b=>{1==b.button&&b.preventDefault()};this.appendElement(this.elButton)}}createPanel(){if(!this.elPanel){var a='<table border="0" cellspacing="0" cellpadding="0" style="position:absolute !important;padding:4px;left:0px;top:0px;width:100%;height:100%;">'+`<tr><td height="22" align="right" width="66" title="${H.getString("Connections")}-PlayerId-QualityControl">${H.getString("ConnInfo")}</td><td><span id="i_connections_${this.uniqueId}">1</span>-<span id="i_playerId_${this.uniqueId}">1</span></td><td align="right"><a id="i_hide_panel_${this.uniqueId}" href="javascript:">${H.getString("Close")}</a></td></tr>`+
`<tr><td height="22" align="right">${H.getString("Duration")}</td><td colspan="2" id="i_duration_${this.uniqueId}">00:00:00</td></tr>`+`<tr><td height="22" align="right">${H.getString("Resolution")}</td><td colspan="2" id="i_resolution_${this.uniqueId}">1920 x 1080</td></tr>`+`<tr><td height="22" align="right">${H.getString("Received")}</td><td colspan="2" id="i_bytesReceived_${this.uniqueId}">0 kB</td></tr>`+`<tr><td height="22" align="right">${H.getString("ReceivedFrames")}</td><td colspan="2" id="i_framesReceived_${this.uniqueId}">0</td></tr>`+
`<tr><td height="22" align="right" title="${H.getString("DroppedTip")}">${H.getString("Dropped")}</td><td colspan="2"><span id="i_packetsLost_${this.uniqueId}">0</span> / <span id="i_frameDropped_${this.uniqueId}">0</span></td></tr>`+`<tr><td height="22" align="right" title="${H.getString("DecodeTimeTip")}">${H.getString("DecodingTime")}</td><td colspan="2"><span id="i_compositeTime_${this.uniqueId}"></span></td></tr>`+`<tr><td height="22" align="right" title="${H.getString("DecodeFramesTip")}">${H.getString("DecodeFrames")}</td><td colspan="2"><span id="i_framesDecoded_${this.uniqueId}" title="number of frames decoded">0</span> / <span id="i_keyframesDecoded_${this.uniqueId}" title="Number of keyframes decoded">0</span></td></tr>`+
`<tr><td height="22" align="right">${H.getString("Bitrate")}</td><td colspan="2" id="i_bitrate_${this.uniqueId}">-</td></tr>`+`<tr><td height="22" align="right" title="${H.getString("FPSTip")}">${H.getString("FPS")}</td><td colspan="2" title="${H.getString("FPSTip")}"><span id="i_framerateRender_${this.uniqueId}">-</span> / <span id="i_framerateVideo_${this.uniqueId}">-</span></td></tr>`+`<tr><td height="22" align="right">${H.getString("QP")}</td><td colspan="2" id="i_qp_${this.uniqueId}">0</td></tr>`+
`<tr><td height="22" align="right" title="${H.getString("MaxQPTip")}">${H.getString("MaxQP")}</td><td colspan="2"><select id="i_settings_maxQP_${this.uniqueId}" style="width:84px;"></select></td></tr>`+`<tr><td height="22" align="right" title="Mbps">${H.getString("MaxBitrate")}</td><td colspan="2"><select id="i_settings_maxBitrate_${this.uniqueId}" style="width:84px;"></select></td></tr>`+"</table>";this.elPanel=document.createElement("div");this.elPanel.innerHTML=a;this.elPanel.id=`liveStatusPanel_${this.uniqueId}`;
this.elPanel.className="__liveStatusPanel __fadeinComp";this.elPanel.oncontextmenu=new Function("return false");this.elPanel.onselectstart=new Function("return false");this.appendElement(this.elPanel);this.get("i_hide_panel").onclick=()=>this.setPanelVisible();for(a=29;40>=a;a++)this.get("i_settings_maxQP").add(new Option(a,a));this.get("i_settings_maxQP").onchange=()=>{let b={encodeMaxQP:this.getValue("i_settings_maxQP")};this.player.rtc.updateParams(b);this.player.signallingConnection.updateParams(b);
this.get("i_settings_maxQP").blur()};for(a=10;100>=a;a+=5)this.get("i_settings_maxBitrate").add(new Option(a,a));this.get("i_settings_maxBitrate").onchange=()=>{let b={maxBitrate:this.getValue("i_settings_maxBitrate")};this.player.rtc.updateParams(b);this.player.signallingConnection.updateParams(b);this.get("i_settings_maxBitrate").blur()}}}setPosition(a){a||(a=this.container.getBoundingClientRect());this.elButton&&(this.elButton.style.top=`${a.height-32}px`);if(this.elPanel)if(this.customPosX&&this.customPosY)this.elPanel.style.left=
this.customPosX+"px",this.elPanel.style.top=this.customPosY+"px";else{let b=this.elPanel.getBoundingClientRect();this.elPanel.style.left="5px";this.elPanel.style.top=`${a.height-b.height-42}px`}}destroy(){this.updateLatencyTestUI=!1;this.visible&&this.setPanelVisible();this.elPanel&&(this.removeElement(this.elPanel),this.elPanel=null);this.elButton&&(this.removeElement(this.elButton),this.elButton=null)}setPanelVisible(a,b){this.customPosX=a;this.customPosY=b;if(this.elPanel)if("none"==this.elPanel.style.display)this.updateConnectionCount(),
this.elPanel.style.display="block",this.elPanel.className="__liveStatusPanel __fadeinComp",this.visible=!0,this.player.rtc.sendEnableIntervalSendQP(!0);else{this.elPanel.className="__liveStatusPanel __fadeoutComp";setTimeout(()=>{this.visible=!1;this.elPanel&&(this.elPanel.style.display="none");this.player.rtc.sendEnableIntervalSendQP(!1);this.setNeedReposUI();this.resize()},500);return}else this.createPanel(),this.visible=!0,this.resize(),this.updateConnectionCount(),this.player.rtc.requestInitialSettings(),
this.player.rtc.sendEnableIntervalSendQP(!0);this.setNeedReposUI();this.resize()}adjustMaxQP(a){return 18==a?(this.get("i_settings_maxQP")&&this.get("i_settings_maxQP").add(new Option(18,18)),18):0>a?40:0<=a&&29>a?29:40<a?40:a}adjustMaxBitrate(a){return 300==a?(this.get("i_settings_maxBitrate")&&this.get("i_settings_maxBitrate").add(new Option(300,300)),300):10>a?10:100<a?100:a}formatNumber(a,b){return"undefined"===typeof a?"-":(new Intl.NumberFormat(window.navigator.language,{maximumFractionDigits:b||
0})).format(a)}processValue(a){return"undefined"===typeof a?"-":a}updateButtonTip(a){this.elButton&&(this.elButton.title=a)}updateConnectionCount(a){"undefined"!==typeof a&&(this.playersCount=a);this.setText("i_connections",this.playersCount);this.updatePlayerId()}updatePlayerId(){this.setText("i_playerId",this.player.playerId+(this.player.isQualityController()?"-QC":""))}updateWebRtcStats(a){if(this.visible){var b=(a.timestamp-a.timestampStart)/1E3,c=new Intl.NumberFormat(window.navigator.language,
{maximumFractionDigits:0,minimumIntegerDigits:2}),e=[],d=[60,60];for(let g=0;g<d.length;g++)e.push(b%d[g]),b/=d[g];e.push(b);d=e[0];b=e[1];e=e[2];b=60==b?0:b;d=c.format(60==d?0:d);b=c.format(Math.floor(b));c=c.format(Math.floor(e));this.setText("i_duration",c+":"+b+":"+d);this.setText("i_resolution",a.frameWidth&&a.frameHeight?a.frameWidth+" x "+a.frameHeight:"-");c="B";e=a.bytesReceived||0;b=["kB","MB","GB"];for(d=0;d<b.length&&!(1E5>e);d++)e/=1E3,c=b[d];this.setText("i_bytesReceived",this.formatNumber(e)+
" "+c);this.setText("i_framesReceived",this.processValue(a.framesReceived));this.setText("i_framesDecoded",a.framesDecoded);this.setText("i_keyframesDecoded",this.processValue(a.keyFramesDecoded));this.updateVideoFramerate(this.formatNumber(a.framesPerSecond));this.updateBitrate(a.bitrate);this.setText("i_packetsLost",this.processValue(a.packetsLost));this.setText("i_frameDropped",this.processValue(a.framesDropped));this.setText("i_compositeTime",this.formatNumber(a.receiveToCompositeMs,1))}}updateVideoFramerate(a){let b=
this.get("i_framerateVideo");b.style.color=10>a?"red":20>a?"orange":"rgb(128,255,0)";b.innerText=a}updateBitrate(a){let b=this.get("i_bitrate");a?1E3<a?(b.style.color="rgb(128,255,0)",b.innerText=this.formatNumber(a/1E3,1)+" Mbps"):(b.style.color=100>a?"red":1E3>a?"orange":"rgb(128,255,0)",b.innerText=this.formatNumber(a)+" kbps"):b.innerText="-"}updateInitialSettings(a){let b=this.adjustMaxQP(a.Encoder.MaxQP);this.setValue("i_settings_maxQP",b);a=this.adjustMaxBitrate(a.WebRTC.MaxBitrate/1E3/1E3);
this.setValue("i_settings_maxBitrate",a)}updateQPAndRenderFPS(a,b){let c=this.get("i_qp");c.style.color=35<a?"red":26<a?"orange":"rgb(128,255,0)";c.innerText=a;this.get("i_framerateRender").innerText=this.processValue(b)}}class yc extends ia{constructor(a){super(a);this.bDelayClear=!1;this.create()}create(){this.el=document.createElement("pre");this.el.id=`startupInfoPanel_${this.uniqueId}`;this.el.className="__startupInfoPanel";this.el.oncontextmenu=new Function("return false");this.el.onselectstart=
new Function("return false");this.appendElement(this.el);this._visible=!0}destroy(){this.el&&(this.removeElement(this.el),this.el=null)}setVisible(a){!this._visible&&a&&this.el&&(this.el.innerText="");this._visible!=a&&this.el&&((this._visible=a)?(this.el.className="__startupInfoPanel",this.el.style.display="block"):this.el.style.display="none",this.setNeedReposUI())}hide(){this.el.className="__startupInfoPanel __fadeoutComp";setTimeout(()=>this.setVisible(!1),500)}setDelayClear(a){this.bDelayClear=
a}log(a){if(this.el){if(this.bDelayClear)this.bDelayClear=!1,this.el.innerText=a+"\n";else{let b=this.el.innerText;b+=a+"\n";this.el.innerText=b}this.el.scrollTop=this.el.scrollHeight+100}}setPosition(a){a||(a=this.container.getBoundingClientRect());this.el&&(this.el.style.height=`${a.height-20}px`)}}class zc extends ia{constructor(a){super(a);this.create()}create(){this.el||(this.el=document.createElement("i"),this.el.id=`statusIndicator_${this.uniqueId}`,this.el.className="__statusIndicator",this.appendElement(this.el))}destroy(){this.el&&
(this.removeElement(this.el),this.el=null)}setStatus(a){let b="Red",c=this.get("statusIndicator");switch(a){case U.WS_Disconnected:b="Red";c&&(c.style.display="inline-block");break;case U.WS_Connecting:b="RGB(200,200,200)";break;case U.WS_Connected:b="Yellow";break;case U.RTC_Opened:b="RGB(0,128,255)";break;case U.OnReady:b="RGB(0,255,0)",this.hide()}this.lastStatus=a;c&&(c.style.backgroundColor=b)}hide(){let a=this.get("statusIndicator");a&&(a.style.display="none")}}class Ac{constructor(){this.css("@keyframes __fadeoutframes {0%{opacity:1;} 100%{opacity:0;}}.__fadeoutComp {animation-duration:600ms; animation-name:__fadeoutframes;}@keyframes __fadeinframes {0%{opacity:0;} 100%{opacity:0.65;}}.__fadeinComp {animation-duration:100ms; animation-name:__fadeinframes;}@keyframes twinkling{0%{opacity:0;}100%{opacity:1;}}.__statusIndicator {position:absolute;display:block;left:5px;top:5px;width:6px;height:6px;border-radius:50%;background-color:red;animation:twinkling 0.25s infinite ease-in-out;}.__startupInfoPanel { position:absolute;left:3px;top:0px;width:400px;background-color:transparent !important;text-shadow:black 0.1em 0.1em 0.2em;color:white;font-family:Verdana;font-size:12px !important;overflow:auto;}.__liveStatusPanel {position:absolute;left:5px;top:10px;width:164px;height:300px;background-color:rgba(50,50,50,0.8);color:#00FF00;font-family:Verdana;font-size:10px;box-shadow:0px 0px 5px 5px rgba(200,200,200,0.5);-moz-border-radius:8px;-webkit-border-radius:8px;}.__liveStatusPanel a:link { color: rgb(0,255,128); text-decoration: none; }.__liveStatusPanel a:hover { color: yellow; background:transparent; }.__liveStatusPanel input {background:transparent; border:1px solid gray; color: #00FF00;}.__liveStatusPanel select {background:transparent; border:1px solid gray; color: #00FF00;}.__liveStatusPanel option {background:#333; color: #00FF00;}.__liveStatusSwitchButton{position:absolute;padding:0;left:6px;width:26px;height:26px;opacity:0.65;cursor:pointer;background-size:conver;}.__fullscreeButton {position:absolute;padding:0;left:38px;width:26px;height:26px;opacity:0.65;cursor:pointer;background-size:conver;}.__viewHomeButton {position:absolute;padding:0;left:70px;width:26px;height:26px;opacity:0.65;cursor:pointer;background-size:conver;}.__taskWaiting {position:absolute;z-index:30;text-align:center;padding-left:5px;text-shadow:black 0.1em 0.1em 0.2em;color:lime;font-family:Verdana;font-size:12px !important;overflow:hidden;display:none;}.__eventsPanel {position:absolute;left:3px;top:0px;width:600px;height:200px;background-color:rgba(64,64,64,0.5) !important;text-shadow:black 0.1em 0.1em 0.2em;color:white;font-family:Verdana;font-size:12px !important;overflow:auto;}.__eventsPanelClear {position:absolute;left:3px;top:20px;text-shadow:black 0.1em 0.1em 0.2em;color:yellow;font-family:Verdana;font-size:12px !important;}.streamingVideoNoCursor {width:100%;height:100%;}.streamingVideoCursorMove {width:100%;height:100%;cursor:move;}.streamingVideoCursorPan {width:100%;height:100%;cursor:url(data:application/octet-stream;base64,AAACAAEAICAAAA8ADwCoDAAAFgAAACgAAAAgAAAAQAAAAAEAGAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////8AAAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD////////////////////////////////////////////4B///+Af///AH///gA///wAP//8AB///gAf//8AH//+AB///gA///8k///////////////////////////////////////////////////////////////////////w==),auto;}.streamingVideoCursorPointer {width:100%;height:100%;cursor:url(data:application/octet-stream;base64,AAACAAEAICAAAA0ACACoDAAAFgAAACgAAAAgAAAAQAAAAAEAGAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD////////////////////////////////////////////4B///+Af///AH///gA///wAP//8AB//+AAf//AAD//wAA//+AAP//4AD//+AF///gT///4f///+H////h////8////////////////////////////////////////w==),auto;}.streamingVideoCursorRotate {width:100%;height:100%;cursor:url(data:application/octet-stream;base64,AAACAAEAICAAAA8ADwCoDAAAFgAAACgAAAAgAAAAQAAAAAEAGAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAD///////////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////8AAAD///8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD////////////////////////////////////////////4B///+Af///AH//hgAh/wwAMP4cABh+HgAYfh8AGH4eABh+HgA4eAck4BwP//A+H//4fz///P/////////////////////////////////////////////////////w==),auto;}")}css(a){let b=
document.createElement("style");try{b.appendChild(document.createTextNode(a))}catch(c){b.styleSheet.cssText=a}(a=document.getElementsByTagName("head")[0])?a.appendChild(b):console.error("[Cloud] No head element found, cannot create css.")}}class Bc extends ia{constructor(a){super(a);this.elContainer=document.createElement("div");this.elContainer.id=`taskWaiting_${this.uniqueId}`;this.elContainer.className="__taskWaiting";this.elContainer.oncontextmenu=new Function("return false");this.elContainer.onselectstart=
new Function("return false");this.appendElement(this.elContainer);this.elText=document.createElement("div");this.elText.style.marginBottom="15px";this.elText.innerText="dddd";this.elContainer.appendChild(this.elText);this.elButton=document.createElement("input");this.elButton.type="button";this.elButton.value=H.getString("RestartInstNow");this.elButton.onclick=()=>{this.player.setInstanceOptions({iid:this.player.options.iid,reset:!0})};this.elContainer.appendChild(this.elButton);H.onLanguageChangedCallbacks.push(()=>
{this.elButton.value=H.getString("RestartInstNow")});this.setMode(0)}setMode(a){this._mode=a;0===a?this.elContainer.style.background="transparent":(this.elContainer.style.background="rgba(64,64,64,0.5)",this.elContainer.style.height="26px",this.elContainer.style.lineHeight="26px")}destroy(){this.elContainer&&(this.removeElement(this.elContainer),this.elContainer=null)}updateText(a){this.elText&&(this.elText.innerText=a,this.show())}show(){this.elContainer.className="__taskWaiting";this.elContainer.style.display=
"block"}hide(){this.elContainer.className="__taskWaiting __fadeoutComp";setTimeout(()=>this.elContainer.style.display="none",500)}setPosition(a,b,c,e){if(this.elContainer)if(a||(a=this.container.getBoundingClientRect()),0==this._mode){var d=a.width/2-150;0>d&&(d=10);this.elContainer.style.left=d+"px";this.elContainer.style.top=a.height/2+"px"}else d=0,b&&d++,c&&d++,e&&d++,a=a.height-32,this.elContainer.style.left=(0==d?5:1==d?38:2==d?70:102)+"px",this.elContainer.style.top=a+"px"}}class Cc extends ia{constructor(a){super(a);
this.create()}create(){if(!this.el){var a=[];a.push(H.getString("LeftClickTip"));a.push(H.getString("RightClickTip"));a.push(H.getString("MiddleClickTip"));a=a.join("\n");this.el=document.createElement("button");this.el.id=`viewHomeButton_${this.uniqueId}`;this.el.className="__viewHomeButton";if(S.isIOSDevice||S.isSafari)this.el.style.backgroundSize="cover";this.el.innerHTML=da.InitialCamera;this.el.title=a;this.el.oncontextmenu=new Function("return false");this.el.onselectstart=new Function("return false");
this.el.onclick=b=>this.player.viewHome(0);this.el.onmousedown=b=>{0!=b.button&&this.player.viewHome(b.button);b.preventDefault()};this.appendElement(this.el)}}destroy(){this.el&&(this.removeElement(this.el),this.el=null)}setPosition(a,b,c){a||(a=this.container.getBoundingClientRect());if(this.el){let e=5;if(b&&c)e=70;else if(b||c)e=38;this.el.style.left=e+"px";this.el.style.top=`${a.height-32}px`}}}var ya=0,La=[];class Dc{constructor(a,b){if(a){this.host=a;this.options=b;la.processParams(this.options);
this.receiveRenderEvents=this.options.receiveRenderEvents;H.setLanguage();this.uniqueId=++ya;La.push(this);this.options.apiOptions.player=this;this.api=new ta(null,this.options.apiOptions);if(this.options.domId)if(a=Aa(this.options.domId))this.container=document.createElement("div"),this.container.style="position:relative;width:100%;height:100%;",a.innerHTML="",a.appendChild(this.container);else if("loading"==document.readyState){console.error(H.getString("DomLoading"));return}if(this.hasVideo=!!this.container)this.styleManager=
new Ac,this.createWidgetsInConstructor(),this.canAdaptiveResolution=!1,this.lastResizeTime=Date.now(),this.orientationChangeTimeout=void 0;this.constructUrlAndConnect();this.resetInteractTimestamp()}else console.error("[DigitalTwinPlayer] The parameter `host` cannot be empty!")}constructUrlAndConnect(){this.logStartupInfo(`host: ${this.host}`);this.useHttps="https:"==location.protocol||this.options.useHttps;this.url=`${this.useHttps?"wss":"ws"}://${this.host}/player?hasVideo=${this.hasVideo}`;this.options.offer&&
(this.url+="&offer=1");this.options.iid&&(this.url+="&iid="+this.options.iid);this.options.pid&&(this.url+="&pid="+this.options.pid);this.options.password&&(this.url+="&protected=1");if("object"==typeof this.options.urlExtraInfo)for(let a in this.options.urlExtraInfo){let b=this.options.urlExtraInfo[a];null!=b&&void 0!=b&&(this.url+="&"+a+"="+b)}this.options.reset&&(this.url+="&reset=1",this.options.reset=!1);setTimeout(()=>this.connectSignallingServer(),200)}callEvent(a,b,c){a=this.options.events[a];
"function"==typeof a&&a(b,c)}doEventSync(a){if(this.options.enableEventSync)for(let b of La)b.options.enableEventSync&&b.uniqueId!=this.uniqueId&&b.rtc.send(a)}setEnableEventSync(a){this.options.enableEventSync=a}getHost(){return this.host}getAPI(){return this.api}getInstanceInfo(){return this.paramInfo}getVideoElement(){let a;return null==(a=this.htmlVideo)?void 0:a.video}getVideoElementSize(){let a=this.getVideoElement();return{width:null==a?void 0:a.clientWidth,height:null==a?void 0:a.clientHeight}}getVideoStreamSize(){return{width:this.htmlVideo.videoWidth(),
height:this.htmlVideo.videoHeight()}}getVideoSize(){return this.getVideoStreamSize()}screen2World(a,b,c){let e=this.getVideoElementSize(),d=this.getVideoStreamSize(),g=0,h=0,q=d.height/d.width;e.height/e.width>q?h=-(e.width*q-e.height)/2:g=-(e.height/q-e.width)/2;a=d.width/(e.width-2*g)*(a-g);b=d.height/(e.height-2*h)*(b-h);return this.getAPI().coord.screen2World(a,b,c)}world2Screen(a,b,c,e){const d=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){let g=yield d.getAPI().coord.world2Screen(a,
b,c,e),h=d.getVideoElementSize(),q=d.getVideoStreamSize(),l=0,f=0,m=q.height/q.width;h.height/h.width>m?f=-(h.width*m-h.height)/2:l=-(h.height/m-h.width)/2;g.screenPosition=[g.screenPosition[0]/(q.width/(h.width-2*l))+l,g.screenPosition[1]/(q.height/(h.height-2*f))+f];return g})}setResolution(a,b){if(this.streamerAdaptive&&(0!=a||0!=b)){window.devicePixelRatio&&(a*=window.devicePixelRatio,b*=window.devicePixelRatio);if(4096<a||4096<b)b=a/b,1<=b?(a=4096,b=4096/b):(a=4096*b,b=4096);this.streamerAdaptive&&
this.streamerLimitMaxResolution&&(a>this.streamerResX||b>this.streamerResY)&&(b=a/b,b>=this.streamerResX/this.streamerResY?(a=this.streamerResX,b=this.streamerResX/b):(a=this.streamerResY*b,b=this.streamerResY));a=Math.floor(a);b=Math.floor(b);256>a&&(console.warn("ResX:"+a),a=256);256>b&&(console.warn("ResY:"+b),b=256);var c=this.htmlVideo.videoWidth(),e=this.htmlVideo.videoHeight();if(a!=c&&a+1!=c&&a-1!=c||b!=e&&b+1!=e&&b-1!=e){if(this.lastWidth&&this.lastHeight&&(c=Math.abs(this.lastWidth-a),e=
Math.abs(this.lastHeight-b),(0!=c||0!=e)&&4>c&&4>e))return;this.lastWidth=a;this.lastHeight=b;this.rtc&&this.rtc.updateRenderResolution(a,b);return[a,b]}}}destroy(a,b){console.log(`call DigitalTwinPlayer.destroy: ${a||" "}`);if(!this.isDestroyed){this.logStartupInfo(a||" ");ya--;this.isDestroyed=!0;this.destroyReason=a;var c;null==(c=this.rtc)||c.close();var e;null==(e=this.signallingConnection)||e.close(b)}}setKeyEventTarget(a){let b;null==(b=this.htmlVideo)||b.setKeyEventTarget(a)}setKeyEventReceiver(a){this.setKeyEventTarget(a)}resize(){if(this.container){this.rect=
this.container.getBoundingClientRect();if(this.bNeedReposUI||!this.oldRect||this.oldRect.width!=this.rect.width||this.oldRect.height!=this.rect.height){this.bNeedReposUI=!1;this.oldRect=this.rect;let b;null==(b=this.eventsPanel)||b.setPosition(this.rect);let c;null==(c=this.startupInfo)||c.setPosition(this.rect);let e;null==(e=this.liveStatus)||e.setPosition(this.rect);let d;null==(d=this.fullscreenButton)||d.setPosition(this.rect,this.options.ui.statusButton);let g;null==(g=this.homeButton)||g.setPosition(this.rect,
this.options.ui.statusButton,this.options.ui.fullscreenButton);let h;null==(h=this.taskListBar)||h.setPosition(this.rect,this.options.ui.statusButton,this.options.ui.fullscreenButton,this.options.ui.homeButton)}if(this.htmlVideo.valid()){this.canAdaptiveResolution&&this.setResolution(this.htmlVideo.clientWidth(),this.htmlVideo.clientHeight());var a;null==(a=this.htmlVideo)||a.resize(this.streamerAdaptive&&this.canAdaptiveResolution)}}}setInstanceOptions(a){const b=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){var c=
a.pid==b.options.pid&&a.iid==b.options.iid||!a.pid&&a.iid==b.options.iid||!a.iid&&a.pid==b.options.pid;if(c&&!a.reset)b.api.log("setInstanceOptions: no change.");else{if(!c){c=(!a.iid||a.iid==b.options.iid)&&a.pid!=b.options.pid;if(b.streamerLocked&&c){b.api.log(H.getString("CannotChangeProject"));return}c=yield b.signallingConnection.checkParams(a);if(null!==c){if(!1===c.iid){b.api.log(`iid(${a.iid})${H.getString("InstanceNotExist")}`);return}if(!1===c.pid){b.api.log(`pid(${a.pid})${H.getString("ProjectNotExist")}`);
return}}}console.warn("call setInstanceOptions!");b.destroy();b.options.iid=a.iid;b.options.pid=a.pid;b.options.reset=a.reset;b.constructUrlAndConnect()}})}set fullscreen(a){a?S.fullscreen(this.container):S.exitFullscreen()}get fullscreen(){return!!document.fullscreenElement}setActionEventEnabled(a){let b;null==(b=this.htmlVideo)||b.setActionEventEnabled(a)}setEnableInteract(a){let b;null==(b=this.htmlVideo)||b.setEnableInteract(a)}perfTest(a){let b;null==(b=this.rtc)||b.perfTest(a)}onApiReady(){this.setStatus(U.OnReady);
let a;null==(a=this.signallingConnection)||a.sendReady();1==this.options.ui.mainUI?this.api.settings.setMainUIVisibility(!0):0==this.options.ui.mainUI&&this.api.settings.setMainUIVisibility(!1);1==this.options.ui.campass?this.api.settings.setCampassVisible(!0):0==this.options.ui.campass&&this.api.settings.setCampassVisible(!1)}sendApi(a,b){if(a)if(b){let c;null==(c=this.signallingConnection)||c.sendApi(a)}else{let c;null==(c=this.rtc)||c.sendApi(a)}}onMainThreadBusy(a){if(1===a.busy){let b="BUSY! ",
c=this.api.apiQueue.queueSize(),e=this.api.apiQueue.dataSize();0<c&&(b+=`${H.getString("JSQueue")}${c}/${e}\uff0c`);1<a.taskCount&&(b+=`${H.getString("BackQueue")}${a.taskCount}\uff0c`);b+=`${H.getString("Execting")}${k[a.currentTask]}${H.getString("PleaseWait")}`;this.hasVideo&&(this.htmlVideo.isVideoLoaded()&&this.options.ui.taskListBar||!this.htmlVideo.isVideoLoaded())&&this.taskListBar.updateText(b)}else this.taskListBar.hide()}viewHome(a){this.api.viewHome(a)}resetInteractTimestamp(){let a=Date.now();
this.timeOfLastInteraction=a;let b;null==(b=this.signallingConnection)||b.resetInteractTimestamp(a)}receiveParamInfo(a){this.paramInfo=a;let b=a.project,c=a.nodeIP,e=a.hostName;this.options.iid=a.iid;this.options.pid=a.pid;this.streamerAdaptive=a.adjustResolution;this.streamerLimitMaxResolution=a.limitMaxResolution;this.streamerResX=a.resX;this.streamerResY=a.resY;this.streamerLocked=a.locked;if(this.kickOutInMinutes=a.kickOutInMinutes){let g=setInterval(()=>{if(Date.now()-this.timeOfLastInteraction>
6E4*this.kickOutInMinutes){let h=H.getString("DisconnectForIdle"),q;null==(q=this.startupInfo)||q.setVisible(!0);this.destroy(h,ja.kicked);console.warn(h);clearInterval(g)}},2E4)}this.nodeInfoArray=[`${H.getString("NodeInfo")}`];e&&this.nodeInfoArray.push(`\t${H.getString("Host")}`+e);c&&this.nodeInfoArray.push(`\t${H.getString("HostAddress")}`+c);b&&this.nodeInfoArray.push(`\t\u3000${H.getString("Project")}`+b);this.options.pid&&this.nodeInfoArray.push(`\t${H.getString("ProjectId")}`+this.options.pid);
this.nodeInfoArray.push(`\t${H.getString("IID")}`+this.options.iid);this.nodeInfoArray.push(`\t${H.getString("Adaptive")}`+this.streamerAdaptive);this.nodeInfoArray.push(`\t${H.getString("LimitMaxRes")}`+this.streamerLimitMaxResolution);this.nodeInfoArray.push(`\t${H.getString("Resolution")}${this.streamerResX}x${this.streamerResY}`);let d;null==(d=this.liveStatus)||d.updateButtonTip(this.nodeInfoArray.join("\n"));this.api.log(this.nodeInfoArray.join("\n\t"))}logStartupInfo(a){if(a){a=`[${ua()}] ${a}`;
var b;null==(b=this.startupInfo)||b.log(a);1<ya&&(a=`[${this.uniqueId}] `+a);console.log("[DEBUG]"+a);this.api.log(a)}}setStatus(a,b){this.cloudStatus=a;let c;null==(c=this.statusIndicator)||c.setStatus(a);switch(a){case U.WS_Disconnected:if(this.isDestroyed){console.log("destroyed.");break}b&&(this.logStartupInfo(b),console.warn(b));break;case U.WS_Connecting:this.logStartupInfo(`connecting with ${this.useHttps?"wss":"ws"}...`);break;case U.WS_Connected:this.logStartupInfo("connected");break;case U.OnReady:let e;
null==(e=this.startupInfo)||e.hide();this.options.ui.debugEventsPanel&&!this.eventsPanel&&(this.eventsPanel=new uc(this),this.eventsPanel.appendText("Start recording events here:"))}}createWidgetsInConstructor(){this.taskListBar=new Bc(this);this.htmlVideo=new wc(this,{keyEventTarget:this.options.keyEventTarget,useBuiltinCursors:this.options.useBuiltinCursors,mouseKeyListener:this.options.events.mouseKeyListener,touchListener:this.options.events.touchListener,onEvents:a=>{let b;return null==(b=this.eventsPanel)?
void 0:b.appendText(a)},onVideoLoaded:()=>{this.logStartupInfo("video: loadedmetadata");this.createWidgets();this.callEvent("onVideoLoaded");this.resize()},onMButtonDoubleClick:(a,b)=>{let c;null==(c=this.liveStatus)||c.setPanelVisible(a,b)}});this.options.ui.statusIndicator&&(this.statusIndicator=new zc(this));this.options.ui.startupInfo&&(this.startupInfo=new yc(this),this.logStartupInfo("sdk version: "+this.api.getVersion()),this.logStartupInfo("uniqueId: "+this.uniqueId));this.options.disableResizeObserver||
S.isMobileDevice||(this.resizeObserver=new ResizeObserver(Ma(()=>this.resize(),1E3)),this.resizeObserver.observe(this.container))}createWidgets(){this.options.ui.statusButton&&(this.liveStatus=new xc(this,this.playerCount,this.nodeInfoArray.join("\n")));this.options.ui.fullscreenButton&&(this.fullscreenButton=new vc(this));this.options.ui.homeButton&&(this.homeButton=new Cc(this));this.taskListBar&&this.taskListBar.setMode(1)}destroyWidgets(){this.liveStatus&&(this.liveStatus.destroy(),this.liveStatus=
null);this.fullscreenButton&&(this.fullscreenButton.destroy(),this.fullscreenButton=null);this.homeButton&&(this.homeButton.destroy(),this.homeButton=null);this.eventsPanel&&(this.eventsPanel.destroy(),this.eventsPanel=null);this.taskListBar&&(this.taskListBar.destroy(),this.taskListBar=null)}isQualityController(){return this.bQualityController}initWebRtcPlayer(a){H.setLanguage(a.language);this.rtc=new tc({useStats:this.hasVideo&&(this.options.ui.statusButton||this.options.events.onRtcStatsReport),
peerConnectionOptions:a.peerConnectionOptions,events:{onlog:b=>this.logStartupInfo(b),ontrack:b=>{let c;null==(c=this.htmlVideo)||c.setTrack(b)},onicecandidate:b=>{let c;return null==(c=this.signallingConnection)?void 0:c.sendCandidate(b)},onOfferCreated:b=>{let c;return null==(c=this.signallingConnection)?void 0:c.sendOffer(b.sdp,this.hasVideo)},onAnswerCreated:b=>{let c;return null==(c=this.signallingConnection)?void 0:c.send(b)},onstats:b=>{let c;null==(c=this.liveStatus)||c.updateWebRtcStats(b);
this.callEvent("onRtcStatsReport",b)},ondatachannelopen:()=>{this.setStatus(U.RTC_Opened);this.signallingConnection.sendCustomString(this.options.customString);this.hasVideo&&(this.rtc.requestQualityControl(),this.liveStatus&&this.rtc.requestInitialSettings());this.receiveRenderEvents||this.rtc.sendEnableReceiveEvents(!1);this.api.onConnectionOpen()},ondatachannelclose:b=>this.api.onConnectionClose(b),ondatachannelerror:b=>this.api.onConnectionClose(b.error),ondatachannelmessage:(b,c)=>{switch(b){case "ResponseAPI":this.api.onConnectionMessage(c);
break;case "InitialSettings":let e;null==(e=this.liveStatus)||e.updateInitialSettings(c);break;case "VideoEncoderAvgQP":let d;if(null==(d=this.liveStatus)?0:d.visible)b=(new TextDecoder("utf-16")).decode(c.data.slice(1)),(b=JSON.parse(b))&&this.liveStatus.updateQPAndRenderFPS(b.qp,b.renderFPS);break;case "QualityControlOwnership":this.bQualityController=c;let g;null==(g=this.liveStatus)||g.updatePlayerId()}}},enableApiCallLog:this.options.enableApiCallLog},this);this.hasVideo&&this.htmlVideo.setWebRtcPlayer(this.rtc);
this.logStartupInfo("setting up...");this.options.offer&&this.rtc.createOffer();this.hasVideo&&this.options.registerEvents&&this.htmlVideo.registerEvents()}connectSignallingServer(){this.setStatus(U.WS_Connecting);this.signallingConnection=new qc({onopen:()=>{this.isDestroyed=!1;this.setStatus(U.WS_Connected);this.options.password&&this.signallingConnection.sendInstancePassword(this.options.password)},onclose:a=>this.handleSignallingClose(a),onerror:()=>{this.setStatus(U.WS_Disconnected)},onmessage:a=>
{switch(a.type){case "config":setTimeout(()=>this.initWebRtcPlayer(a),200);break;case "answer":this.playerId=a.playerId;this.rtc&&this.rtc.onReceiveAnswer(a);break;case "offer":this.playerId=a.playerId;this.rtc&&this.rtc.onReceiveOffer(a);break;case "iceCandidate":this.rtc&&this.rtc.onIceCandidate(a.candidate);break;case "paramInfo":this.receiveParamInfo(a);break;case "status":this.handleSignallingMessage_Status(a);break;case "playerCount":this.handleSignallingMessage_PlayerCount(a);break;case "api":this.api.onConnectionMessage(JSON.stringify(a));
break;case "userConfig":this.captchaRequired=a.captchaRequired}},customString:this.options.customString});this.signallingConnection.connect(this.url)}handleSignallingClose(a){this.destroyWidgets();this.callEvent("onConnClose",a);var b;null==(b=this.startupInfo)||b.setVisible(!0);this.api.onConnectionClose(a);this.rtc&&this.rtc.close();b="";this.isDestroyed?(b=H.getString("Destroyed"),b=this.destroyReason?b+(": "+this.destroyReason):b+"."):(b="closed: ",a.code&&(b+=a.code),a.reason?b+=" "+a.reason:
1006==a.code&&(b+=" "+H.getString("Disconnect")));this.setStatus(U.WS_Disconnected,b);Oa(a.code)||this.destroy();this.isDestroyed||(this.logStartupInfo(H.getString("Reconnect5s")),this.startupInfo.setDelayClear(!0),setTimeout(()=>this.connectSignallingServer(),5E3));a.code==ja.instance_is_busy?confirm(H.getString("RestartAndRetry"))&&this.setInstanceOptions({iid:this.busyIId,reset:!0}):a.code==ja.no_username_provided?this.callEvent("onLoginRequired",this.captchaRequired):a.code==ja.user_does_not_exist?
this.callEvent("onLoginRequired",this.captchaRequired):a.code==ja.not_logged_in&&this.callEvent("onLoginRequired",this.captchaRequired)}handleSignallingMessage_Status(a){let b=Na(a.state);(b=a.info||b)&&this.logStartupInfo(b);a.state===ba.StartFailed?(console.error(a.info),this.options.ui.startupInfo&&setTimeout(()=>alert(a.info),500)):a.state==ba.ConfirmBusy&&(this.busyIId=a.info)}handleSignallingMessage_PlayerCount(a){this.playerCount=a.count;let b;null==(b=this.liveStatus)||b.updateConnectionCount(this.playerCount);
this.canAdaptiveResolution=1===a.count}}u.APIErrorCode=va;u.ActionMode={None:0,Follow:1,FollowBehindAndAbove:2,FollowBehind:3,FollowAbove:4,FollowBellow:5,FollowLeft:6,FollowRight:7};u.AnimatedImageButtonData=Xa;u.AssetType={EPT_Scene:1,EPT_ModelActor:2,EPT_Measurement:3,EPT_Cut:4,EPT_DynamicWater:5,EPT_Vehicle:6,EPT_Tag:7,EPT_Light:8,EPT_Decal:9,EPT_LightBeam:10,EPT_RadiationPoint:11,EPT_Surface:12,EPT_ShpPoint:13,EPT_ShpPolyline:14,EPT_ShpPolygon:15,EPT_Polyline:16,EPT_VideoProjector:17,EPT_Panoramic:18,
EPT_FlattenModifier:19,EPT_Cesium:20,EPT_CutPolygonModifier:21,EPT_EffectPoint:22,EPT_S3MLayer:23,EPT_ParticleActor:24,EPT_RoleActor:25,EPT_SoundActor:26,EPT_CustomActor:27,EPT_CameraTour:28,EPT_Animation:29};u.BPFuncParamType={Bool:0,UInt8:1,Int32:2,Float:3,Double:4,String:5,Color:6,Vector2D:7,Vector:8,Rotator:9,IntArray:10,StringArray:11,VectorArray:12,Coordinate:13,CoordinateArray:14,FloatArray:15,DisplayStyle:16,CustomIconPath:17};u.BPFunctionData=kb;u.BeamData=Ya;u.CameraTourData=mb;u.CameraTourKeyFrame=
lb;u.CloudStatus=U;u.Color={LightPink:"RGB(255,182,193)",Pink:"RGB(255,192,203)",Crimson:"RGB(220,20,60)",LavenderBlush:"RGB(255,240,245)",PaleVioletRed:"RGB(219,112,147)",HotPink:"RGB(255,105,180)",DeepPink:"RGB(255,20,147)",MediumVioletRed:"RGB(199,21,133)",Orchid:"RGB(218,112,214)",Thistle:"RGB(216,191,216)",plum:"RGB(221,160,221)",Violet:"RGB(238,130,238)",Magenta:"RGB(255,0,255)",Fuchsia:"RGB(255,0,255)",DarkMagenta:"RGB(139,0,139)",Purple:"RGB(128,0,128)",MediumOrchid:"RGB(186,85,211)",DarkVoilet:"RGB(148,0,211)",
DarkOrchid:"RGB(153,50,204)",Indigo:"RGB(75,0,130)",BlueViolet:"RGB(138,43,226)",MediumPurple:"RGB(147,112,219)",MediumSlateBlue:"RGB(123,104,238)",SlateBlue:"RGB(106,90,205)",DarkSlateBlue:"RGB(72,61,139)",Lavender:"RGB(230,230,250)",GhostWhite:"RGB(248,248,255)",Blue:"RGB(0,0,255)",MediumBlue:"RGB(0,0,205)",MidnightBlue:"RGB(25,25,112)",DarkBlue:"RGB(0,0,139)",Navy:"RGB(0,0,128)",RoyalBlue:"RGB(65,105,225)",CornflowerBlue:"RGB(100,149,237)",LightSteelBlue:"RGB(176,196,222)",LightSlateGray:"RGB(119,136,153)",
SlateGray:"RGB(112,128,144)",DoderBlue:"RGB(30,144,255)",AliceBlue:"RGB(240,248,255)",SteelBlue:"RGB(70,130,180)",LightSkyBlue:"RGB(135,206,250)",SkyBlue:"RGB(135,206,235)",DeepSkyBlue:"RGB(0,191,255)",LightBLue:"RGB(173,216,230)",PowDerBlue:"RGB(176,224,230)",CadetBlue:"RGB(95,158,160)",Azure:"RGB(240,255,255)",LightCyan:"RGB(225,255,255)",PaleTurquoise:"RGB(175,238,238)",Cyan:"RGB(0,255,255)",Aqua:"RGB(212,242,231)",DarkTurquoise:"RGB(0,206,209)",DarkSlateGray:"RGB(47,79,79)",DarkCyan:"RGB(0,139,139)",
Teal:"RGB(0,128,128)",MediumTurquoise:"RGB(72,209,204)",LightSeaGreen:"RGB(32,178,170)",Turquoise:"RGB(64,224,208)",Auqamarin:"RGB(127,255,170)",MediumAquamarine:"RGB(0,250,154)",MediumSpringGreen:"RGB(0,255,127)",MintCream:"RGB(245,255,250)",SpringGreen:"RGB(60,179,113)",SeaGreen:"RGB(46,139,87)",Honeydew:"RGB(240,255,240)",LightGreen:"RGB(144,238,144)",PaleGreen:"RGB(152,251,152)",DarkSeaGreen:"RGB(143,188,143)",LimeGreen:"RGB(50,205,50)",Lime:"RGB(0,255,0)",ForestGreen:"RGB(34,139,34)",Green:"RGB(0,128,0)",
DarkGreen:"RGB(0,100,0)",Chartreuse:"RGB(127,255,0)",LawnGreen:"RGB(124,252,0)",GreenYellow:"RGB(173,255,47)",OliveDrab:"RGB(85,107,47)",Beige:"RGB(245,245,220)",LightGoldenrodYellow:"RGB(250,250,210)",Ivory:"RGB(255,255,240)",LightYellow:"RGB(255,255,224)",Yellow:"RGB(255,255,0)",Olive:"RGB(128,128,0)",DarkKhaki:"RGB(189,183,107)",LemonChiffon:"RGB(255,250,205)",PaleGodenrod:"RGB(238,232,170)",Khaki:"RGB(240,230,140)",Gold:"RGB(255,215,0)",Cornislk:"RGB(255,248,220)",GoldEnrod:"RGB(218,165,32)",
FloralWhite:"RGB(255,250,240)",OldLace:"RGB(253,245,230)",Wheat:"RGB(245,222,179)",Moccasin:"RGB(255,228,181)",Orange:"RGB(255,165,0)",PapayaWhip:"RGB(255,239,213)",BlanchedAlmond:"RGB(255,235,205)",NavajoWhite:"RGB(255,222,173)",AntiqueWhite:"RGB(250,235,215)",Tan:"RGB(210,180,140)",BrulyWood:"RGB(222,184,135)",Bisque:"RGB(255,228,196)",DarkOrange:"RGB(255,140,0)",Linen:"RGB(250,240,230)",Peru:"RGB(205,133,63)",PeachPuff:"RGB(255,218,185)",SandyBrown:"RGB(244,164,96)",Chocolate:"RGB(210,105,30)",
SaddleBrown:"RGB(139,69,19)",SeaShell:"RGB(255,245,238)",Sienna:"RGB(160,82,45)",LightSalmon:"RGB(255,160,122)",Coral:"RGB(255,127,80)",OrangeRed:"RGB(255,69,0)",DarkSalmon:"RGB(233,150,122)",Tomato:"RGB(255,99,71)",MistyRose:"RGB(255,228,225)",Salmon:"RGB(250,128,114)",Snow:"RGB(255,250,250)",LightCoral:"RGB(240,128,128)",RosyBrown:"RGB(188,143,143)",IndianRed:"RGB(205,92,92)",Red:"RGB(255,0,0)",Brown:"RGB(165,42,42)",FireBrick:"RGB(178,34,34)",DarkRed:"RGB(139,0,0)",Maroon:"RGB(128,0,0)",White:"RGB(255,255,255)",
WhiteSmoke:"RGB(245,245,245)",Gainsboro:"RGB(220,220,220)",LightGrey:"RGB(211,211,211)",Silver:"RGB(192,192,192)",DarkGray:"RGB(169,169,169)",Gray:"RGB(128,128,128)",DimGray:"RGB(105,105,105)",Black:"RGB(0,0,0)"};u.CommandType=k;u.CustomObjectData=gb;u.CustomObjectData2=hb;u.CustomTagData=Ua;u.DecalData=eb;u.DigitalTwinAPI=ta;u.DigitalTwinPlayer=Dc;u.DynamicWaterData=jb;u.FieldType={String:0,Number:1};u.HeatMapPointData=Ra;u.HeatMapStyle={Normal:-1,CustomColor:0,CustomWave:1};u.HighlightAreaData=
Va;u.HydroDynamic2DStyle={TrueWater:0,HeatMap:1};u.ImageButtonData=Wa;u.LayerVisibleData=Qa;u.MapMode={Campass:0,SmallMap:1,BigMap:2};u.MeasurementMode={Coordinate:1,Linear:2,Horizontal:3,Vertical:4,MultiPoint:5,TerrainArea:6};u.MousePickMask=ra;u.ODLineData=bb;u.PanelType={ViewshedAnalysis:0,SkylineAnalysis:1,ViewDomeAnalysis:2,VisiblityAnalysis:3,FloodFillAnalysis:4,SolarAnalysis:5,CutFillAnalysis:6,TerrainSlopeAnalysi:7,ContourLineAnalysis:8};u.PanoramaData=db;u.Polygon3DData=$a;u.Polygon3DStyle=
{Wave:0,LoopLine:1,Gradual:2,DynamicGradual:3,WaveTransparent:4,WideWave:5,RotateArrow:6,RotateLine:7,RotateGradual:8,SingleColor:9,SingleColorWithLight:10,OriginColor:11,OceanWater:12,LakeWater:13,Opacity:14,Mask:15,Transparent:16,Volumn:17};u.PolygonData=cb;u.PolygonStyle={SingleColor:0,CirclePoint:1,Volumn:2,Gradual:3,DynamicGradual:4,WaveTransparent:5,WideWave:6,RotateArrow:7,RotateLine:8,RotateGradual:9,OnTerrain:10,OriginColor:11};u.PolylineData=ab;u.PolylineStyle={Arrow:0,Arrow1:1,Flow:2,Beam:3,
Normal:4,OnTerrain:5,DottedNormal:6,DottedCircle:7,OriginColor:8};u.REVISION=19146;u.RadiationPointData=Za;u.RendererType={SimpleRenderer:0,UniqueValueRenderer:1,ClassBreaksRenderer:2,VisibleRenderer:3};u.ResetType=Da;u.SplineMeshStyle={Fence_1:0,Fence_2:1,Wall_1:2,Wall_2:3,Road_0:4,Road_1:5,Road_2:6,Road_3:7,Road_4:8,Road_5:9,Road_6:10,Road_7:11,Road_8:12,Road_9:13,Fence_Tree:14,Pipe:15};u.TagData=Sa;u.TileLayerActorData=fa;u.TileLayerData=fb;u.UIPanelType={MainUI:0,NewACP:1,OpenACP:2,MergeACP:3,
SaveACP:4,Load3DT:5,Load3DTService:6,LoadSHP:7,Load3DTiles:8,Exit:9,InfoTreeGet:10,ResourceGet:11,PreferenceSettings:12,Around:13,CenterAround:14,Robomb:15,Role:16,Map:17,Vehicle:18,Edit:19,CameraTour:20,Report:21,Weather:22,PostProcess:23,Camera:24,ShowSettings:25,Measure:26,Flatten:27,Hole:28,Clip:29,Select:30,Screenshot:31,Material:32,Vegetation:33,Vehicle:34,DynamicWater:35,VideoProjection:36,Panorama:37,Light:38,Marker:39,Marker3D:40,Decal:41,Beam:42,RadiationPoint:43,Polyline:44,Polygon:45,
Settings:46,Help:47,ViewshedAnalysis:48,SkylineAnalysis:49,ViewDomeAnalysis:50,VisiblityAnalysis:51,FloodFillAnalysis:52,SolarAnalysis:53,CutFillAnalysis:54,TerrainSlopeAnalysi:55,ContourLineAnalysis:56};u.VERSION="6.1";u.VectorFieldStyle={Typhoon:"DTS_TPHOON",Waves:"DTS_WAVE",Ocean:"DTS_OCEAN",Fluid:"DTS_STYLE_FLUID",Fluid_UV:"DTS_FLUID_COUNT_XYZ_UV",River:"DTS_RIVER",Wave:"DTS_WATER_WAVE"};u.VehicleViewMode={FirstPerson:0,ThirdPerson:1};u.VideoProjectionData=ib;u.Viewport=ca;u.VisualType={Color:0,
Size:1,Height:1,Opacity:1};u.WaterFlowFieldStyle={HeatMap:0,Water:1,Particle:2};u.WaterMode={Animation:0,Simulation:1,Arrow:2};u.WebUIData=Ta;u.__onCefResponse=function(a,b){if(xa)xa.onConnectionMessage(a,b)};Object.defineProperty(u,"__esModule",{value:!0});return u}({});
(()=>{if("undefined"!=typeof module&&module.exports)module.exports=acapi;else if("function"==typeof define&&define.amd)define(function(){return acapi});else{window.AcApiVersion=acapi.VERSION;acapi.AirCityAPI=acapi.DigitalTwinAPI;acapi.AirCityPlayer=acapi.DigitalTwinPlayer;for(let u in acapi)"VERSION"!=u&&(window[u]=acapi[u]);acapi.DigitalTwinAPI.__onApiConstructed=u=>{window.__g=u;window.fdapi=u}}})();
