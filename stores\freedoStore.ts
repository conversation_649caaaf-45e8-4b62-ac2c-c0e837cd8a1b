import { defineStore } from 'pinia';

// 添加轨迹相关的类型定义
interface TrackPoint {
  id?: string;
  position: number[];
  locationTime?: string;
  [key: string]: any;
}

interface TrackOptions {
  groupId?: string;
  color?: number[];
  thickness?: number;
  style?: number;
  flowRate?: number;
  intensity?: number;
  startMarkerPath?: string;
  endMarkerPath?: string;
  pointMarkerPath?: string;
  markerSize?: number[];
  textVisible?: boolean;
  showMarkers?: boolean; // 是否显示轨迹点标记
  // 新增选项
  shape?: number; // 折线类型：0直线，1曲线
  tiling?: number; // 材质贴图平铺比例
  material?: string; // 自定义材质路径
  scalarParameters?: Array<{name: string, value: number}>; // 材质数值类型参数
  vectorParameters?: Array<{name: string, value: number[]}>; // 材质数组类型参数
  autoFocus?: boolean; // 是否自动聚焦到轨迹线
  focusDistance?: number; // 聚焦距离
  focusDuration?: number; // 聚焦动画时间
  focusRotation?: number[]; // 聚焦时相机旋转角度
}

export const useFreedoStore = defineStore('freedo', {
  state: () => ({
    
    freedoReady: false,
    layerTree: [] as any,
    animationList: [] as any,
    freedoApi: null as any,
    freedoPlayer: null as any,
    freedoClickedObject: null as any,
    // 是否已经播放过导览动画
    hasPlayedIntro: false,
    // 摄像头位置相关状态
    cameraPositions: {
      initial: null, // 初始位置
      previous: null, // 上一次位置
      current: null, // 当前位置
      history: [] // 位置历史记录，可用于前进/后退功能
    },
    // 镜头自动移动
    isAutoMoving: false,
    autoCameraMovementTimer: null as any,
    lastModelInteractionTime: Date.now(), // 记录最后一次模型交互时间
    // 轨迹相关状态
    trackPolylineId: '',
    trackMarkerIds: [] as string[],
  }),
  
  getters: {
    isFreedoReady: (state) => state.freedoReady && !!state.freedoApi,
    
    getLayerById: (state) => (id: string) => {
      return findLayerById(state.layerTree, id);
    },
    
    getAnimationByName: (state) => (name: string) => {
      return state.animationList.find(item => item.name === name);
    }
  },
  
  actions: {
    setFreedoReadyState(flag: boolean) {
      this.freedoReady = flag;
      console.log('this.freedoReady===>', this.freedoReady)
    },
    
    setLayerTree(layerTree: any[]) {
      this.layerTree = layerTree;
      console.log('this.layerTree===>', this.layerTree)
    },
    
    setAnimationList(animationList: any[]) {
      this.animationList = animationList;
      console.log('this.animationList===>', this.animationList)
    },
    
    setFreedoApi(api: any) {
      this.freedoApi = api;
    },
    
     setFreedoPlayer(player: any | null) {
      this.freedoPlayer = player;
    },

    setFreedoClickedObject(obj: any) {
      this.freedoClickedObject = obj;
    },

    // 设置是否已播放登录导览动画
    setHasPlayedIntro(value: boolean) {
      this.hasPlayedIntro = value;
    },

    // 获取图层树数据
    async getFreedoLayerTree() {
      if (!this.freedoApi?.infoTree) return;
      
      const info = await this.freedoApi.infoTree.get();
      this.layerTree = info.infotree;
      
      return info.infotree;
    },
    
    // 获取导览数据
    async getFreedoAnimationList() {
      if (!this.freedoApi?.camera) return;
      
      const animationList = [];
      const res = await this.freedoApi.camera.getAnimationList();
      console.log('response====>', res)
      const data = res?.data ? res.data : [];
    
      for (const item of data) {
        const obj = {
          id: item.id,
          name: item.name,
          img: ''
        };
        
        const { image } = await this.freedoApi.camera.getAnimationImage(item.name);
        obj.img = 'data:image/png;base64,' + image;
        animationList.push(obj);
      }
      
      this.animationList = animationList;
      return animationList;
    },
    
    // 播放导览
    async playFreedoAnimation(nameOrId: string) {
      if (!this.freedoApi?.camera) return;
      
      const animation = this.animationList.find(
        item => item.name === nameOrId || item.id === nameOrId
      );
      
      if (animation) {
        return this.freedoApi.camera.playAnimation(animation.id);
      }
    },
    
    // 摄像头位置相关方法
    /**
     * 更新最后一次模型交互时间
     */
    updateLastModelInteraction() {
      this.lastModelInteractionTime = Date.now();
    },
    /**
     * 开始自动相机移动计时器 - 在指定的空闲时间后启动自动移动
     * @param idleTimeout 空闲超时时间（毫秒），默认30秒
     */
    startAutoCameraMovementTimer(idleTimeout = 30000) {
      // 清除现有计时器
      this.clearAutoCameraMovementTimer();
      
      // 设置新的计时器
      this.autoCameraMovementTimer = setTimeout(async () => {
        // 如果已经在自动移动，不需要再次启动
        if (this.isAutoMoving) return;
        
        // 开始自动移动
        await this.startAutoCameraMovement();
      }, idleTimeout);
      
      console.log('设置自动移动计时器，将在', idleTimeout / 1000, '秒后启动');
    },
    /**
     * 清除自动移动计时器
     */
    clearAutoCameraMovementTimer() {
      if (this.autoCameraMovementTimer) {
        clearTimeout(this.autoCameraMovementTimer);
        this.autoCameraMovementTimer = null;
      }
    },
    /**
     * 开始自动相机移动 - 使用turnRight方法
     * @param duration 移动持续时间（毫秒），默认为无限
     */
    async startAutoCameraMovement(duration?: number) {
      if (!this.isFreedoReady || this.isAutoMoving || !this.freedoApi?.camera) return;
      
      // 获取当前相机位置 - 使用已有的方法
      await this.getCameraPosition();
      
      // 此时 cameraPositions.current 已经被更新，可以作为初始位置
      
      this.isAutoMoving = true;
      console.log('开始自动相机移动，初始位置:', this.cameraPositions.current);
      
      // 使用turnRight方法进行旋转
      try {
        // 开始旋转
        await this.freedoApi.camera.turnRight();
        
        // 如果设置了持续时间，则在指定时间后停止
        if (duration && duration > 0) {
          this.autoCameraMovementTimer = setTimeout(() => {
            this.stopAutoCameraMovement();
          }, duration);
        }
      } catch (error) {
        console.error('启动相机旋转失败:', error);
        this.isAutoMoving = false;
      }
    },
    
    /**
     * 停止自动相机移动并返回初始位置
     */
    async stopAutoCameraMovement() {
      if (!this.isAutoMoving || !this.freedoApi?.camera) return;
      
      // 清除定时器
      this.clearAutoCameraMovementTimer();
      
      try {
        // 停止相机移动
        await this.freedoApi.camera.stop();
        
        // 如果有初始位置，则返回到初始位置
        if (this.cameraPositions.current) {
          const { x, y, z, pitch, yaw } = this.cameraPositions.current;
          
          console.log('返回到初始位置:', this.cameraPositions.current);
          
          // 使用已有的setCameraPosition方法返回到初始位置
          await this.setCameraPosition({
            x, y, z, pitch, yaw,
            flyTime: 2 // 使用2秒的过渡时间
          });
        }
      } catch (error) {
        console.error('停止相机移动或返回初始位置失败:', error);
      } finally {
        // 无论成功失败，都重置状态
        this.isAutoMoving = false;
      }
    },
    /**
     * 重置自动移动 - 停止当前移动并设置新的计时器
     * @param idleTimeout 空闲超时时间（毫秒），默认30秒
     */
    async resetAutoCameraMovement(idleTimeout = 30000) {
      // 如果正在自动移动，先停止
      if (this.isAutoMoving) {
        await this.stopAutoCameraMovement();
      } else {
        // 如果没有在自动移动，只清除计时器
        this.clearAutoCameraMovementTimer();
      }
      
      // 更新最后交互时间
      this.updateLastModelInteraction();
      
      // 设置新的计时器
      this.startAutoCameraMovementTimer(idleTimeout);
    },

    // 获取相机位置
    async getCameraPosition() {
      if (!this.freedoApi?.camera) return null;
      
      try {
        const cameraInfo = await this.freedoApi.camera.get();
        // 更新当前位置
        this.cameraPositions.previous = this.cameraPositions.current;
        this.cameraPositions.current = cameraInfo;
        
        // 如果初始位置未设置，则设置初始位置
        if (!this.cameraPositions.initial) {
          this.cameraPositions.initial = cameraInfo;
        }
        
        return cameraInfo;
      } catch (error) {
        console.error('获取相机位置失败:', error);
        return null;
      }
    },
    
    async setCameraPosition(options: {
      x?: number,
      y?: number,
      z?: number,
      pitch?: number,
      yaw?: number,
      flyTime?: number
    }) {
      if (!this.freedoApi?.camera) return false;
      
      try {
        // 先获取当前位置
        const current = this.cameraPositions.current || await this.getCameraPosition();
        if (!current) return false;
        
        // 保存当前位置到历史记录
        this.cameraPositions.history.push({...current});
        
        // 限制历史记录长度，避免内存占用过多
        if (this.cameraPositions.history.length > 20) {
          this.cameraPositions.history.shift();
        }
        
        // 设置新位置
        const x = options.x ?? current.x;
        const y = options.y ?? current.y;
        const z = options.z ?? current.z;
        const pitch = options.pitch ?? current.pitch;
        const yaw = options.yaw ?? current.yaw;
        const flyTime = options.flyTime ?? 0;
        
        await this.freedoApi.camera.set(x, y, z, pitch, yaw, flyTime);
        
        // 更新当前位置
        await this.getCameraPosition();
        
        return true;
      } catch (error) {
        console.error('设置相机位置失败:', error);
        return false;
      }
    },
    
    // 返回初始位置
    async resetCameraToInitial(flyTime = 2) {
      if (!this.cameraPositions.initial) {
        await this.getCameraPosition(); // 如果没有初始位置，先获取当前位置作为初始位置
      }
      
      if (this.cameraPositions.initial) {
        const { x, y, z, pitch, yaw } = this.cameraPositions.initial;
        return this.setCameraPosition({ x, y, z, pitch, yaw, flyTime });
      }
      
      return false;
    },
    
    // 返回上一个位置
    async goToPreviousCameraPosition(flyTime = 2) {
      if (this.cameraPositions.previous) {
        const { x, y, z, pitch, yaw } = this.cameraPositions.previous;
        return this.setCameraPosition({ x, y, z, pitch, yaw, flyTime });
      }
      
      return false;
    },
    
    // 从历史记录返回到指定位置
    async goToCameraPositionHistory(index: number, flyTime = 2) {
      if (index >= 0 && index < this.cameraPositions.history.length) {
        const position = this.cameraPositions.history[index];
        const { x, y, z, pitch, yaw } = position;
        return this.setCameraPosition({ x, y, z, pitch, yaw, flyTime });
      }
      
      return false;
    },
    
    // 保存当前位置为书签
    saveCameraPositionBookmark(name: string) {
      if (!this.cameraPositions.current) return null;
      
      // 这里可以添加将书签保存到localStorage或其他存储的逻辑
      const bookmark = {
        name,
        position: {...this.cameraPositions.current},
        timestamp: new Date().toISOString()
      };
      
      // 示例：保存到localStorage
      try {
        const bookmarks = JSON.parse(localStorage.getItem('cameraBookmarks') || '[]');
        bookmarks.push(bookmark);
        localStorage.setItem('cameraBookmarks', JSON.stringify(bookmarks));
      } catch (e) {
        console.error('保存相机书签失败:', e);
      }
      
      return bookmark;
    },
    
    // 加载相机位置书签
    loadCameraPositionBookmarks() {
      try {
        return JSON.parse(localStorage.getItem('cameraBookmarks') || '[]');
      } catch (e) {
        console.error('加载相机书签失败:', e);
        return [];
      }
    },
    
    // 跳转到书签位置
    async goToBookmark(bookmarkNameOrIndex: string | number, flyTime = 1) {
      const bookmarks = this.loadCameraPositionBookmarks();
      if (!bookmarks.length) return false;
      
      let bookmark;
      if (typeof bookmarkNameOrIndex === 'string') {
        bookmark = bookmarks.find(b => b.name === bookmarkNameOrIndex);
      } else if (typeof bookmarkNameOrIndex === 'number') {
        bookmark = bookmarks[bookmarkNameOrIndex];
      }
      
      if (bookmark && bookmark.position) {
        const { x, y, z, pitch, yaw } = bookmark.position;
        return this.setCameraPosition({ x, y, z, pitch, yaw, flyTime });
      }
      
      return false;
    },


    /**
     * 绘制轨迹线
     * @param points 轨迹点数组
     * @param options 轨迹绘制选项
     * @returns 是否绘制成功
     */
    async drawTrack(points: TrackPoint[], options: TrackOptions = {}) {
      console.log('this.freedoApi', this.freedoApi)
      if (!this.freedoApi || !this.freedoApi.polyline || points.length < 2) {
        console.warn('无法绘制轨迹：API未就绪或轨迹点不足');
        return false;
      }
      
      // 清除之前的轨迹
      await this.clearTrack();
      
      // 生成唯一ID
      this.trackPolylineId = `track-polyline-${Date.now()}`;
      
      // 提取坐标点
      
      const coordinates = points
        .filter(item => item && item.length >= 2)
        .map(item => {
          if (item.length === 2) {
            return [...item, 5]; // 添加一个固定高度值，避免轨迹线贴地
          }
          return item;
        });

        console.log('coordinates===>', coordinates)
      
      if (coordinates.length < 2) {
        console.warn('有效坐标点不足，无法绘制轨迹线');
        return false;
      }
      
      // 创建轨迹线
      const polylineData = {
        id: this.trackPolylineId,
        groupId: options.groupId || 'personnelTrack',
        coordinates: coordinates,
        coordinateType: 0, // 使用投影坐标系
        color: options.color || [0, 0.7, 1, 1], // 默认蓝色
        thickness: options.thickness || 5, // 线宽
        style: options.style || 2, // 光流效果
        flowRate: options.flowRate || 0.5, // 流速
        intensity: options.intensity || 1000, // 亮度
        depthTest: true,
        range: [1, 100000], // 可视范围
        // 新增支持的选项
        shape: options.shape !== undefined ? options.shape : 0, // 折线类型：0直线，1曲线
        tiling: options.tiling || 0, // 材质贴图平铺比例
        material: options.material, // 自定义材质路径
        scalarParameters: options.scalarParameters, // 材质数值类型参数
        vectorParameters: options.vectorParameters, // 材质数组类型参数
      };
      
      try {
        // 添加轨迹线
        await this.freedoApi.polyline.add(polylineData);
        console.log('轨迹线绘制成功');
        
        // 只有当 showMarkers 选项为 true 时才添加轨迹点标记
        if (options.showMarkers) {
          await this.addTrackMarkers(points, options);
        }
        
        // 如果需要自动聚焦到轨迹线
        if (options.autoFocus) {
          await this.freedoApi.polyline.focus(
            this.trackPolylineId,
            options.focusDistance || 100,
            options.focusDuration || 1.0,
            options.focusRotation || [-30, 0, 0]
          );
        }
        
        return true;
      } catch (err) {
        console.error('轨迹线绘制失败:', err);
        return false;
      }
    },
    
    /**
     * 添加轨迹点标记
     * @param points 轨迹点数组
     * @param options 轨迹绘制选项
     */
    async addTrackMarkers(points: TrackPoint[], options: TrackOptions = {}) {
      if (!this.freedoApi || !this.freedoApi.marker) return false;
      
      // 清除之前的标记
      await this.clearTrackMarkers();
      
      // 创建新的标记
      const markers = points
        .filter(item => item.position && item.position.length >= 2)
        .map((item, index) => {
          const position = item.position.length === 2 ? [...item.position, 5] : item.position;
          const isStart = index === 0;
          const isEnd = index === points.length - 1;
          
          // 确定标记图标
          let imagePath = options.pointMarkerPath || '/assets/images/common/track-point.svg';
          if (isStart) {
            imagePath = options.startMarkerPath || '/assets/images/common/start-point.svg';
          } else if (isEnd) {
            imagePath = options.endMarkerPath || '/assets/images/common/end-point.svg';
          }
          
          const markerSize = options.markerSize || [24, 24];
          const halfWidth = markerSize[0] / 2;
          
          return {
            id: `track-marker-${index}-${Date.now()}`,
            groupId: options.groupId ? `${options.groupId}-markers` : 'personnelTrackMarkers',
            coordinate: position,
            coordinateType: 0,
            imagePath: imagePath,
            imageSize: markerSize,
            anchors: [-halfWidth, markerSize[1]],
            fixedSize: true,
            text: options.textVisible !== false ? `${item.locationTime || '未知时间'}` : '',
            fontSize: 12,
            fontColor: [1, 1, 1, 1],
            textBackgroundColor: [0, 0, 0, 0.5],
            showLine: false,
            range: [1, 10000],
          };
        });
      
      if (markers.length > 0) {
        try {
          await this.freedoApi.marker.add(markers);
          this.trackMarkerIds = markers.map(m => m.id);
          console.log('轨迹点标记添加成功');
          return true;
        } catch (err) {
          console.error('轨迹点标记添加失败:', err);
          return false;
        }
      }
      
      return false;
    },
    
    /**
     * 清除轨迹线
     */
    async clearTrackPolyline() {
      if (!this.freedoApi || !this.freedoApi.polyline || !this.trackPolylineId) return;
      
      try {
        await this.freedoApi.polyline.delete(this.trackPolylineId);
        this.trackPolylineId = '';
      } catch (err) {
        console.warn('清除轨迹线失败:', err);
      }
    },
    
    /**
     * 清除轨迹点标记
     */
    async clearTrackMarkers() {
      if (!this.freedoApi || !this.freedoApi.marker || this.trackMarkerIds.length === 0) return;
      
      try {
        await this.freedoApi.marker.remove(this.trackMarkerIds);
        this.trackMarkerIds = [];
      } catch (err) {
        console.warn('清除轨迹点标记失败:', err);
      }
    },
    
    /**
     * 清除所有轨迹相关元素
     */
    async clearTrack() {
      await this.clearTrackPolyline();
      await this.clearTrackMarkers();
    },
    
   /**
     * 飞行到轨迹点
     * @param point 轨迹点
     * @param options 飞行选项
     */
    async flyToTrackPoint(point: TrackPoint, options: {
      duration?: number;
      focusEntireTrack?: boolean;
      distance?: number;
      rotation?: number[];
    } = {}) {
      if (!this.freedoApi || !this.freedoApi.camera || !point.position || point.position.length < 2) return false;
      
      const duration = options.duration || 1.0;
      const distance = options.distance || 100;
      const rotation = options.rotation || [-30, 0, 0];
      
      try {
        // 如果需要聚焦到整条轨迹线
        if (options.focusEntireTrack && this.trackPolylineId && this.freedoApi.polyline && this.freedoApi.polyline.focus) {
          // 使用轨迹线的focus方法
          await this.freedoApi.polyline.focus(
            this.trackPolylineId, // 轨迹线ID
            distance,            // 观察距离
            duration,            // 飞行时间
            rotation             // 相机旋转角度 [Pitch, Yaw, Roll]
          );
          
          return true;
        } else {
          // 聚焦到特定点
          const position = point.position.length === 2 ? [...point.position, 5] : point.position;
          
          // 计算相机位置 - 在轨迹点上方一定高度
          const cameraHeight = distance; // 相机高度
          const x = position[0];
          const y = position[1];
          const z = position[2] + cameraHeight;
          
          // 设置相机角度 - 俯视
          const pitch = rotation[0]; // 俯视角度
          const yaw = rotation[1];   // 朝向
          
          // 直接使用相机的 set 方法
          await this.freedoApi.camera.set(x, y, z, pitch, yaw, duration);
          
          return true;
        }
      } catch (err) {
        console.error('飞行到轨迹点失败:', err);
        return false;
      }
    },

    /**
     * 批量添加多条轨迹线
     * @param tracksData 轨迹数据数组，每个元素包含 points 和 options
     * @returns 是否绘制成功
     */
    async drawMultipleTracks(tracksData: Array<{
      points: TrackPoint[];
      options: TrackOptions;
    }>) {
      if (!this.freedoApi || !this.freedoApi.polyline) {
        console.warn('无法绘制轨迹：API未就绪');
        return false;
      }
      
      // 清除之前的轨迹
      await this.clearTrack();
      
      // 准备所有轨迹线数据
      const polylines = [];
      const trackIds = [];
      
      for (const trackData of tracksData) {
        const { points, options } = trackData;
        
        if (points.length < 2) {
          console.warn('轨迹点不足，跳过此轨迹');
          continue;
        }
        
        // 生成唯一ID
        const trackId = `track-polyline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        trackIds.push(trackId);
        
        // 提取坐标点
        const coordinates = points
          .filter(item => item.position && item.position.length >= 2)
          .map(item => {
            // 如果position是二维数组，添加高度值
            if (item.position.length === 2) {
              return [...item.position, 5]; // 添加一个固定高度值，避免轨迹线贴地
            }
            return item.position;
          });
        
        if (coordinates.length < 2) {
          console.warn('有效坐标点不足，跳过此轨迹');
          continue;
        }
        
        // 创建轨迹线
        const polylineData = {
          id: trackId,
          groupId: options.groupId || 'personnelTrack',
          coordinates: coordinates,
          coordinateType: 0, // 使用投影坐标系
          color: options.color || [0, 0.7, 1, 1], // 默认蓝色
          thickness: options.thickness || 5, // 线宽
          style: options.style || 2, // 光流效果
          flowRate: options.flowRate || 0.5, // 流速
          intensity: options.intensity || 0.8, // 亮度
          depthTest: true,
          range: [1, 100000], // 可视范围
          shape: options.shape !== undefined ? options.shape : 0, // 折线类型：0直线，1曲线
          tiling: options.tiling || 0, // 材质贴图平铺比例
          material: options.material, // 自定义材质路径
          scalarParameters: options.scalarParameters, // 材质数值类型参数
          vectorParameters: options.vectorParameters, // 材质数组类型参数
        };
        
        polylines.push(polylineData);
      }
      
      if (polylines.length === 0) {
        console.warn('没有有效的轨迹数据');
        return false;
      }
      
      try {
        // 批量添加轨迹线
        await this.freedoApi.polyline.add(polylines);
        this.trackPolylineId = trackIds[0]; // 保存第一条轨迹线的ID
        console.log('批量轨迹线绘制成功');
        
        return true;
      } catch (err) {
        console.error('批量轨迹线绘制失败:', err);
        return false;
      }
    }
  }
});

// 辅助函数：根据ID查找图层
function findLayerById(layers: any[], id: string): any | null {
  if (!layers || !Array.isArray(layers)) return null;
  
  for (const layer of layers) {
    if (layer.id === id || layer.iD === id) return layer;
    if (layer.children) {
      const found = findLayerById(layer.children, id);
      if (found) return found;
    }
  }
  
  return null;
}