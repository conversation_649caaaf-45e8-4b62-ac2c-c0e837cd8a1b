import { defineStore } from 'pinia'
import deepmerge from 'deepmerge'
import storage from '~/utils/storage'

interface PortalState {
  [keu: string]: any
}

export const usePortalStore = defineStore('portal', {
  state: (): PortalState => {
    // const portalIdData = storage.get('portalId')
    const portalsData = storage.get('portals')
    const portalData = storage.get('portal')
    const microPortalData = storage.get('microPortal')
    const subProjectListData = storage.get('subProjectList')
    const subProjectNodeData = storage.get('subProjectNode')
    const subProjectsData = storage.get('subProjects')
    const siteListData = storage.get('siteList')
    return {
      portalId: '',
      portals: portalsData? JSON.parse(portalsData): [], // 姚家平+其他6个子工程
      portal: portalData ? JSON.parse(portalData) : {},// 保存用户信息
      microPortal: microPortalData ? JSON.parse(microPortalData) : {},// 保存portalId、siteId、subProjectId
      subProjectList: subProjectListData ? JSON.parse(subProjectListData) : [],// 
      subProjectNode: subProjectNodeData ? JSON.parse(subProjectNodeData) : {},
      subProjects: subProjectsData ? JSON.parse(subProjectsData) : [],
      siteList: siteListData ? JSON.parse(siteListData) : [],
    }
  },
  getters: {
  },
  actions: {
    // 更新当前
    updatePortalId(data: any) {
      this.portalId = data || ''
      // storage.set('portalId', JSON.stringify(data))
    },
    // 更新当前门户姚家平+另外6个子工程
    setPortals(data: any) {
      this.portals = deepmerge([], data)
      storage.set('portals', JSON.stringify(data))
    },
    // 更新用户信息
    updatePortal(data: any) {
      this.portal = deepmerge({}, data)
      storage.set('portal', JSON.stringify(data))
    },
    // 更新当前门户微场景id
    updateMicroPortal(data: any) {
      this.microPortal = deepmerge({}, data)
      storage.set('microPortal', JSON.stringify(data))
    },
    // 设置子工程列表
    setSubjectList(data: any) {
      this.subProjectList = deepmerge([], data)
      storage.set('subProjectList', JSON.stringify(data))
    },
    // 更新当前子工程
    updateSubProjectNode(data: any) {
      this.subProjectNode = deepmerge({}, data)
      storage.set('subProjectNode', JSON.stringify(data))
    },
    // 获取wbstree子工程
    updateWbsTree(data: any) {
      this.subProjects = deepmerge([], data)
      storage.set('subProjects', JSON.stringify(data))
    },
    // 获取项目部
    setSiteList(data: any) {
      this.siteList = deepmerge([], data)
      storage.set('siteList', JSON.stringify(data))
    },
  },
})
