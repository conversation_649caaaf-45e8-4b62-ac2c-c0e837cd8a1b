import { defineStore } from 'pinia';

interface User {
  id: number;
  name: string;
  email: string;
}

interface UserState {
  userInfo: User | null;
  isAuthenticated: boolean;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    isAuthenticated: false,
  }),
  getters: {
    userName: (state) => state.userInfo?.name || 'Guest',
  },
  actions: {
    login(user: User) {
      this.userInfo = user;
      this.isAuthenticated = true;
    },
    logout() {
      this.userInfo = null;
      this.isAuthenticated = false;
    },
  },
});
