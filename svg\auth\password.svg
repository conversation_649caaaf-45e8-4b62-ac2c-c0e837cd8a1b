<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Mask group">
<mask id="mask0_406_3945" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
<rect id="Rectangle 34625592" width="20" height="20" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_406_3945)">
<g id="Vector" filter="url(#filter0_d_406_3945)">
<path d="M15.9256 8.93721H6.14V6.07563C6.14 4.16322 7.8265 2.60876 9.8965 2.60876H10.0752C12.1474 2.60876 13.8317 4.16523 13.8317 6.07563C13.8317 6.52005 14.2217 6.88001 14.7033 6.88001C15.1848 6.88001 15.5748 6.52005 15.5748 6.07563C15.5748 4.72025 15.0018 3.44531 13.9646 2.48609C12.9252 1.52687 11.5438 1 10.0752 1H9.8965C8.42789 1 7.04644 1.52888 6.00708 2.48609C4.96772 3.44531 4.39684 4.72025 4.39684 6.07563V8.9352H4.07436C2.92823 8.9352 2 9.79187 2 10.8496V17.0856C2 18.1433 2.92823 19 4.07436 19H15.9256C17.0718 19 18 18.1433 18 17.0856V10.8516C18 9.79388 17.0718 8.93721 15.9256 8.93721ZM10.7615 14.1858V15.9574C10.7615 16.2852 10.4739 16.5507 10.1188 16.5507H9.87907C9.5239 16.5507 9.23628 16.2852 9.23628 15.9574V14.1657C8.87457 13.9364 8.63925 13.5523 8.63925 13.118C8.63925 12.4162 9.25589 11.8471 10.0163 11.8471C10.7768 11.8471 11.3934 12.4162 11.3934 13.118C11.3934 13.5664 11.1429 13.9606 10.7615 14.1858Z" fill="#0E9EC5"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_406_3945" x="-4" y="-3" width="28" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.182065 0 0 0 0 0.543162 0 0 0 0 0.767628 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_406_3945"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_406_3945" result="shape"/>
</filter>
</defs>
</svg>
