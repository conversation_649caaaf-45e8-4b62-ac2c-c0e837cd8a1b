<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Mask group">
<mask id="mask0_406_3937" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
<rect id="Rectangle 34625592" width="20" height="20" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_406_3937)">
<g id="Vector" filter="url(#filter0_d_406_3937)">
<path d="M9.80409 1C7.22016 1 5.12164 3.0142 5.12164 5.5C5.12164 7.9858 7.21925 10 9.80409 10C12.3899 10 14.4875 7.9858 14.4875 5.5C14.4875 3.0142 12.3917 1 9.80409 1ZM9.80409 1C7.22016 1 5.12164 3.0142 5.12164 5.5C5.12164 7.9858 7.21925 10 9.80409 10C12.3899 10 14.4875 7.9858 14.4875 5.5C14.4875 3.0142 12.3917 1 9.80409 1ZM8.04966 11.5003C4.70872 11.5003 2 14.1031 2 17.3125V17.686C2 18.9991 4.70872 19 8.04966 19H11.9522C15.2922 19 18 18.9514 18 17.686V17.3125C18 14.1031 15.2922 11.5003 11.9522 11.5003H8.04966Z" fill="#0E9EC5"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_406_3937" x="-4" y="-3" width="28" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.182065 0 0 0 0 0.543162 0 0 0 0 0.767628 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_406_3937"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_406_3937" result="shape"/>
</filter>
</defs>
</svg>
