{
  "compilerOptions": {
    "esModuleInterop": true,
    "target": "ES2021",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2021", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "resolveJsonModule": true,

    /* Bundler mode */
    "moduleResolution": "node",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    // Resolve
    "baseUrl": "./",
    "paths": {
      "~/*": ["./*"],
      "@assets/*": ["./assets/*"],
      "@components/*": ["./router/components/*"],
    },
  },
  // "include": [
  //   "router/**/*",
  //   "app.ts",
  //   "vite.config.ts",
  //   "vite-env.d.ts", "utils",
  // ],
  "include": ["router/**/*.ts", "router/**/*.d.ts", "router/**/*.tsx", "router/**/*.vue","app.ts",
    "vite.config.ts",
    "vite-env.d.ts", "utils",],
  "exclude": ["node_modules", "dist"]
}
