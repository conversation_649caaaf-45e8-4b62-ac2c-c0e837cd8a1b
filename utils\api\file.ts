/*
 * by: zhang_j47
 * time: 2024年11月24日
 * desc: 文件相关
 * */
import { AxiosResponse } from 'axios'
import request from '../../router/request'
// import requestWithoutTimeout from "@/utils/requestWithoutTimeout";

//上传文件 form data格式
export function uploadFile(data: any): Promise<any> {
  return request({
    url: '/sys-storage/upload',
    method: 'post',
    data,
    timeout: 0,
  })
}

// 根据文件token和grouptoken获取文件信息,data中传至少传g9s数组和f8s数组中的一个
export function getFile(data: any): Promise<any> {
  return request({
    url: '/sys-storage/file',
    method: 'post',
    data,
  })
}

// 根据文件token和grouptoken删除文件，token删除单文件，grouptoken删除文件组
export function deleteFile(data: any): Promise<any> {
  return request({
    url: '/sys-storage/file',
    method: 'delete',
    data,
  })
}

//根据fileToken下载文件
export function downloadFile(f8s: any): Promise<any> {
  //   return requestWithoutTimeout({
  return request({
    url: '/sys-storage/download',
    method: 'GET',
    params: {
      f8s,
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    timeout: 0,
    responseType: 'blob',
  })
}

// 根据文件token和grouptoken获取文件信息,data中传至少传g9s数组和f8s数组中的一个
export function getFiles(data: any): Promise<any> {
  return request({
    url: '/sys-storage/file',
    method: 'post',
    data,
  })
}

//根据fileToken下载图片
export function downloadImage(f8s: any): Promise<any> {
  return request({
    // return requestWithoutTimeout({
    method: 'get',
    url: '/sys-storage/download_image',
    responseType: 'blob',
    params: {
      f8s,
    },
  })
}

//根据groupToken下载zip
export function downloadZip(groupToken: any): Promise<any> {
  return request({
    url: '/sys-storage/zip',
    method: 'get',
    params: { groupToken },
    responseType: 'blob',
  })
}

//修改已上传文件信息
export function changeFileG9s(groupToken: any, data: any): Promise<any> {
  return request({
    url: `/sys-storage/file/token?groupToken=${groupToken}`,
    method: 'put',
    data: data,
  })
}

//上传图片识别文字
export function orc(data: any) {
  return request({
    url: '/sys-storage/ocr',
    method: 'post',
    data,
    timeout: 0,
  })
}

//校验是否下载完成
// export function canBeDownload(params) {
//   // 添加权限校验
//   if (window.bizId) {
//     params['bizId'] = window.bizId
//   }
//   return request({
//     url: '/cybereng-technology/fileDownloadApprove/apply/result',
//     method: 'get',
//     params: params,
//   })
// }

// 定义 params 类型，bizId 是可选的
interface DownloadParams {
  bizId?: string
  [key: string]: any // 允许其他任意字段
}

// 定义 canBeDownload 函数，返回类型是 Promise<AxiosResponse<any>>
export function canBeDownload(
  params: DownloadParams
): Promise<AxiosResponse<any>> {
  // 添加权限校验
  if ((window as any).bizId) {
    params['bizId'] = (window as any).bizId
  }

  // 发送请求并返回
  return request({
    url: '/cybereng-technology/fileDownloadApprove/apply/result',
    method: 'get',
    params: params,
  })
}
// 获取文件或者视频资源
export function getUrl(params:string){
  return `${import.meta.env.CVE_VUE_APP_BASE_API}/sys-storage/download?f8s=${params}`
}
