import request from '~/router/request'

/**
 * @description: 获取服务器时间戳
 * @param {*}
 * @return {*}
 */
export function getTs() {
  return request({
    method: 'GET',
    url: '/sys-gateway/sign/ts',
    // timeout: 1000,
    // cMsg: true,
  })
}

export interface Response {
  data: Record<string, any>
  [key: string]: any
}

/**
 * 获取管理人员字典
 * @param params parentId: string  langCode:string
 * @returns 
 */
export interface dictoryParams {
  parentId: string;
  langCode: string;
}
export interface RequiredContent {
  code: string;
  content: string;
}
export interface DictionaryResponse {
  content: RequiredContent[];
  [key:string]:any
}

export function getDictionary(params:dictoryParams): AxiosPromise<DictionaryResponse> {
  return request({
    method: 'GET',
    url: '/sys-system/dictionary/child/list',
    params
  })
}

// 按类型获取枚举
// data: {code: 'xxxxx'}
export function getDict(data:any) {
  return request({
    url: '/sys-system/dictionary/detail/list',
    method: 'get',
    params: data,
  });
}