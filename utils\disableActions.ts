/**
 * 禁止选中、复制、右键等操作
 * @param element - 目标 HTML 元素
 */
export function disableActions(element: HTMLElement | null): void {
    if (!element) return;
  
    // 禁止选中文本
    element.style.userSelect = 'none';
    // 使用断言添加非标准属性
  (element.style as any).webkitUserSelect = 'none'; // 针对 Webkit 浏览器
  (element.style as any).mozUserSelect = 'none';    // 针对 Firefox 浏览器
  (element.style as any).msUserSelect = 'none';     // 针对旧版 IE 浏览器
  
    // 禁止右键菜单
    const contextMenuHandler = (event: MouseEvent): void => event.preventDefault();
    element.addEventListener('contextmenu', contextMenuHandler);
  
    // 禁止复制
    const copyHandler = (event: ClipboardEvent): void => event.preventDefault();
    element.addEventListener('copy', copyHandler);
  
    // 禁止鼠标长按菜单
    (element.style as any).webkitTouchCallout = 'none'; // 针对 iOS Safari
  
    // 保存事件监听器（便于后续移除）
    element.dataset.disableActionsContextMenuHandler = contextMenuHandler.toString();
    element.dataset.disableActionsCopyHandler = copyHandler.toString();
}
  
  /**
   * 恢复选中、复制、右键等操作
   * @param element - 目标 HTML 元素
   */
export function enableActions(element: HTMLElement | null): void {
    if (!element) return;
  
    // 恢复默认选中行为
    element.style.userSelect = '';
    // 使用断言添加非标准属性
   (element.style as any).webkitUserSelect = ''; // 针对 Webkit 浏览器
   (element.style as any).mozUserSelect = '';    // 针对 Firefox 浏览器
   (element.style as any).msUserSelect = '';     // 针对旧版 IE 浏览器
  
    // 移除禁止右键菜单的监听器
    const contextMenuHandler = element.dataset.disableActionsContextMenuHandler;
    if (contextMenuHandler) {
      element.removeEventListener('contextmenu', eval(contextMenuHandler) as EventListener);
      delete element.dataset.disableActionsContextMenuHandler;
    }
  
    // 移除禁止复制的监听器
    const copyHandler = element.dataset.disableActionsCopyHandler;
    if (copyHandler) {
      element.removeEventListener('copy', eval(copyHandler) as EventListener);
      delete element.dataset.disableActionsCopyHandler;
    }
  
    // 恢复长按菜单
    (element.style as any).webkitTouchCallout = ''; // 针对 iOS Safari
}
  