import globalConfigs from '~/config/index'
import { SM4Encrypt } from './sm4'
import CryptoJS from 'crypto-js'

const { pwdEncrypType } = globalConfigs

/**
 * 密码加密函数
 * @param value 要加密的密码
 * @returns 加密后的密码
 */
export function pwdEncrypt(value: any) {
  if (!value) return '';
  
  switch (pwdEncrypType) {
    case 'MD5':
      return CryptoJS.MD5(value).toString();
    case 'SM4':
      return SM4Encrypt(value);
    default:
      return SM4Encrypt(value); // 默认使用SM4
  }
}