/*
 * @Description: SM4 加密/解密
 * @Reference:
 */

export let SM4_KEY = '43869399C1411A3BE71A99B35123AEFC'

//加密
function encrypt(plaintext: any, key: string) {
  const sm4 = new Sm4utils(key)

  if (plaintext instanceof Object) {
    plaintext = JSON.stringify(plaintext)
  }

  const encryptData = sm4.encryptData_ECB(plaintext)

  if (!encryptData) {
    return false
  } else {
    return encryptData
  }
}

//接口数据加密
export function SM4Encrypt(data: any) {
  const encryptData = encrypt(data, SM4_KEY)

  if (!encryptData) {
    console.error('数据加密失败')
    return
  }

  return encryptData
}