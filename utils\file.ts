/*
 * @Description: 文件类型对应图标
 */
import fileTypeIcon from '~/config/fileIcon'

//常用文本
export const download = (
  file: { fileName?: string; name?: string },
  blobs: BlobPart | BlobPart[] // 文件内容
): void => {
  let blob = new File(
    Array.isArray(blobs) ? blobs : [blobs], // 确保 blobs 是数组
    file.fileName || file.name || 'download', // 文件名
    { type: 'application/octet-stream' } // 文件类型
  )

  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a')
    let href = ''
    if (window.URL) {
      href = window.URL.createObjectURL(blob)
    } else {
      href = window.webkitURL.createObjectURL(blob)
    }

    downloadElement.href = href
    ;(downloadElement.download = file.fileName || file.name || 'download'),
      document.body.appendChild(downloadElement)
    downloadElement.click()
    if (window.URL) {
      window.URL.revokeObjectURL(href)
    } else {
      window.webkitURL.revokeObjectURL(href)
    }
    document.body.removeChild(downloadElement)
    URL.revokeObjectURL(href) //释放 Blob
  } else {
    if ('msSaveBlob' in navigator) {
      ;(navigator as any).msSaveBlob(
        blob,
        file.fileName || file.name || 'download'
      )
    } else {
      console.error('Your browser does not support msSaveBlob.')
    }

    // navigator.msSaveBlob(blob, file.fileName || file.name)
  }
  return
}

//excel
export function exportFile(response: any) {
  const blob = new Blob([response.data], {
    type: response.headers['content-type'],
  }) // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
  const desprsitions = (response.headers['content-disposition'] || '').split(
    ';'
  )
  let filename = ''
  for (let i = 0; i < desprsitions.length; i++) {
    if (desprsitions[i].indexOf('filename') > -1) {
      const filenames = desprsitions[i].split('=')
      if (filenames.length > 1) {
        filename = decodeURI(filenames[1].trim())
      }
    }
  }
  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a')
    let href = ''
    if (window.URL) {
      href = window.URL.createObjectURL(blob)
    } else {
      href = window.webkitURL.createObjectURL(blob)
    }
    downloadElement.href = href
    downloadElement.download = filename
    document.body.appendChild(downloadElement)
    downloadElement.click()
    if (window.URL) {
      window.URL.revokeObjectURL(href)
    } else {
      window.webkitURL.revokeObjectURL(href)
    }
    document.body.removeChild(downloadElement)
  } else {
    // navigator.msSaveBlob(blob, filename)

    if ('msSaveBlob' in navigator) {
        (navigator as any).msSaveBlob(blob, filename);
      } else {
        console.error('Your browser does not support msSaveBlob.');
      }
      
  }
}

export function getFileIconByExtName(extName:string) {
  if (fileTypeIcon[extName]) {
    return fileTypeIcon[extName]
  }

  return 'doc_blank'
}

export function isImage(filename: string | null | undefined): boolean{
  const IMAGE_LIST = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'svg']
  if (filename) {
    let ext = filename.substr(filename.lastIndexOf('.') + 1)
    return IMAGE_LIST.indexOf(ext.toLowerCase()) !== -1
  } else {
    return false
  }
}

export function isVideo(filename: string | null | undefined): boolean {
  const VODEO_LIST = ['mp4', '3gp', 'webm', 'mov']
  if (filename) {
    let ext = filename.slice(filename.lastIndexOf('.') + 1)
    return VODEO_LIST.indexOf(ext.toLowerCase()) >-1
  } else {
    return false
  }
}
