export const fileTypeIcon: { [key: string]: string } = {
    jpg: 'doc_img',
    jpeg: 'doc_img',
    png: 'doc_img',
    gif: 'doc_img',
    bmp: 'doc_img',
    txt: 'doc_txt',
    css: 'doc_code',
    html: 'doc_html',
    xlsx: 'doc_excel',
    xls: 'doc_excel',
    exe: 'doc_exe',
    iso: 'doc_iso',
    ppt: 'doc_ppt',
    pptx: 'doc_ppt',
    zip: 'doc_zip',
    rar: 'doc_zip',
    pdf: 'doc_pdf',
    doc: 'doc_word',
    docx: 'doc_word',
    dir: 'doc_folder',
    doc_blank: 'doc_blank', // 添加默认值，防止返回undefined
};

export function getFileIconByExtName(extName: string): string {
    return fileTypeIcon[extName] || fileTypeIcon['doc_blank'];
}



