/*
 * @Description: 可自动清除message
 */
import {
  ElMessage,
  MessageHandler,
  MessageOptions,
  ElMessageBox,
} from 'element-plus'

let messageInstance: MessageHandler | null = null

export const resetMessage = (options: MessageOptions | string): void => {
  if (messageInstance) {
    messageInstance.close() // 关闭之前的消息
  }
  messageInstance = ElMessage(options) // 创建新的消息实例
}
// 为 resetMessage 动态添加方法类型
['error', 'success', 'info', 'warning'].forEach((type) => {
  // 使用类型断言来扩展 resetMessage 类型
  ;(resetMessage as any)[type] = (options: MessageOptions | string): void => {
    if (typeof options === 'string') {
      options = { message: options } // 如果是字符串，包装成 MessageOptions 对象
    }
    // 强制类型转换，确保 type 被视为 "error" | "success" | "info" | "warning"
    options.type = type as 'error' | 'success' | 'info' | 'warning'
    return resetMessage(options) // 调用 resetMessage 发送消息
  }
})

// 声明 confirmMap，Map 的 key 为 string，value 为 boolean
const confirmMap = new Map<string, boolean>()

// 定义 confirmHandler 的类型
export const confirmHandler = (
  title: string, // 标题是字符串类型
  msg: string, // 信息是字符串类型
  cb: (...args: any[]) => void // 回调函数，接受任意参数并返回 void
): void => {
  console.log(title, msg, cb)

  // 如果 confirmMap 中已有该 title，返回 false
  if (confirmMap.has(title)) {
    return
  } else {
    confirmMap.set(title, true)
  }

  // 调用 ElMessageBox 的 confirm 方法
  ElMessageBox.confirm(msg, title, {
    confirmButtonText: '确定',
    type: 'warning',
    showClose: false,
    closeOnClickModal: true,
    confirmButtonClass: 'el-icon-check',
    showCancelButton: false,
    callback: (...args: any[]) => {
      confirmMap.delete(title) // 删除已处理的 title
      cb(...args) // 执行回调函数，传递 args
    },
  })
}
