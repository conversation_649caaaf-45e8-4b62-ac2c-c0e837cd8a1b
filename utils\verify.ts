import {
  sign,
  cMsg,
  // ignore
} from '~/config/request'

function urlPattern(url: any) {
  let nurl = url.replaceAll('*', '(.)*').replaceAll('(.)*(.)', '*(.)')
  nurl += '$'
  return nurl
}

export function verifyUrl(config: any, type: any) {
  let array = type == 'sign' ? sign : cMsg
  return array.find((item: any) => {
    return new RegExp(urlPattern(item)).test(config.url)
  })
}

export function verifyPhone (phone: any) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone)
}
