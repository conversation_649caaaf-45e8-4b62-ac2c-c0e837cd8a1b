import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { defineConfig, loadEnv } from 'vite'

// Web Components 自定义元素
const CUSTOM_ELEMENTS = ['cve-player']

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  // load .env
  const env = loadEnv(mode, process.cwd());
  // const { VITE_API_BASE_URL } = env;
  console.log(mode, command)
  console.log(env)
  const BASE_PATH = env.VITE_BASE_PATH;
  const OUT_DIR = env.VITE_OUT_DIR;

  console.log('从环境中读取的配置：', {
    BASE_PATH,
    OUT_DIR
  });

  return {
    base: BASE_PATH,
    build: {
      assetsDir: 'assets',
      assetsInlineLimit: 4096,
      chunkSizeWarningLimit: 500,
      cssCodeSplit: true,
      emptyOutDir: true,
      manifest: false,
      minify: 'esbuild',
      outDir: `./dist${OUT_DIR}`,
      reportCompressedSize: true,
      rollupOptions: {
        output: {
          manualChunks: {
            Echarts: ['echarts', 'echarts-gl'],
            Vue: ['vue', 'vue-router'],
          },
        },
      },
      sourcemap: false,
      target: 'es2015',
      write: true,
    },
    envDir: './',
    envPrefix: 'CVE_',
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag) => CUSTOM_ELEMENTS.includes(tag),
          },
        },
      }),

      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        dts: true,
        types: [
          {
            from: 'vue-router',
            names: ['RouterLink', 'RouterView'],
          },
        ],
        resolvers: [ElementPlusResolver()],
      }),

      visualizer({
        open: true,
      }),
    ],
    publicDir: 'static',
    // 设置scss的api类型为modern-compiler
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },
    resolve: {
      // alias: [
      //   {
      //     find: '~',
      //     replacement: resolve(process.cwd()),
      //   },
      // ],
      alias: {
        '~': resolve(process.cwd()),
        '@assets': fileURLToPath(new URL('./assets', import.meta.url)),
        '@components': '/router/components/',
      },
      // 忽略后缀名的配置选项, 添加 .vue 选项时要记得原本默认忽略的选项也要手动写入
      extensions: [
        '.mjs',
        '.js',
        '.mts',
        '.ts',
        '.jsx',
        '.tsx',
        '.json',
        '.vue',
      ],
    },
    root: process.cwd(),

    server: {
      proxy: {
        '/api/project-yjp-lly': {
          target: 'http://192.168.70.65:8080',
          changeOrigin: true,
          pathRewrite: {
            '/api/project-yjp-lly': ''
          },
          // 重要：代理个人本地环境需要加上rewrite；线上有nginx代理，不需要rewrite
          rewrite: (path) => path.replace(/^\/api\/project-yjp-lly/, '')
        },
        '/api/project-yjp-tcs-lly': {
          target: 'http://192.168.70.65:8081',
          changeOrigin: true,
          pathRewrite: {
            '/api/project-yjp-tcs-lly': ''
          },
          rewrite: (path) => path.replace(/^\/api\/project-yjp-tcs-lly/, '')
        },
        '/api/project-yjp-hanyong2': {
          target: 'http://192.168.70.230:8080',
          changeOrigin: true,
          pathRewrite: {
            '/api/project-yjp-hanyong2': ''
          },
          rewrite: (path) => path.replace(/^\/api\/project-yjp-hanyong2/, '')
        },
        '/api/project-yjp-scs-hanyong': {
          target: 'http://192.168.70.230:8082',
          changeOrigin: true,
          pathRewrite: {
            '/api/project-yjp-scs-hanyong': ''
          },
          rewrite: (path) => path.replace(/^\/api\/project-yjp-scs-hanyong/, '')
        },
        '/api/project-yjp-localtest': {
          target: 'http://192.168.70.68:8001', // 测试
          changeOrigin: true,
        },
        '/api/project-yjp-tcs-localtest': {
          target: 'http://192.168.70.68:8001', // 测试
          changeOrigin: true,
        },
        '/api/project-yjp-scs-localtest': {
          target: 'http://192.168.70.68:8001', // 测试
          changeOrigin: true,
        },
        '/api/project-yjp-api': {
          // target: 'http://36.137.93.26:8001', // 测试
          target: 'https://szjg.yaojiaping.com',
          changeOrigin: true,
        },
        '/api/project-yjp-tcs': {
          // target: 'http://36.137.93.26:8001', // 测试
          target: 'https://szjg.yaojiaping.com',
          changeOrigin: true,
        },
        '/api/project-yjp-scs': {
          // target: 'http://36.137.93.26:8001', // 测试
          target: 'https://szjg.yaojiaping.com',
          changeOrigin: true,
        },
        '/api/project-yjp-water': {
          // target: 'http://36.137.93.26:8001', // 测试
          target: 'https://szjg.yaojiaping.com',
          changeOrigin: true,
        },
        '/api': {
          // target: 'http://36.137.93.26:8001', // 测试
          target: 'https://szjg.yaojiaping.com',
          changeOrigin: true,
        },
        '/filePreview': {
          target: 'http://file-preview:8012',
          changeOrigin: true,
          pathRewrite: {
            '/filePreview': ''
          }
        },
        '/sub_app_wp/': {
          target: 'http://apigateway.hdec.com:8001/sub_app_wp/',
          changeOrigin: true,
          pathRewrite: {
            '/sub_app_wp': ''
          }
        },
        '/hdy_file/': {
          target: `http://********:8001/`,
          changeOrigin: true,
          pathRewrite: {
            '/hdy_file/': '/hdy_file/',
          },
        },
        '/hdy_kld/': {
          target: `http://********:8001/`,
          changeOrigin: true,
          pathRewrite: {
            '/hdy_kld/': '/hdy_kld/',
          },
        },
      },
      hmr: {
        overlay: true,
      },
      host: '0.0.0.0',
      open: false,
      port: 7512,
    },

    // VITE_API_BASE_URL,
  }
})
